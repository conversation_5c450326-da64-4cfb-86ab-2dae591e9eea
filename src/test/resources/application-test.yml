# 测试环境配置
spring:
  profiles:
    active: test
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false

# 算法配置
algorithm:
  genetic:
    population-size: 10
    max-generations: 5
    min-generations: 3
    crossover-rate: 0.8
    mutation-rate: 0.1
    tournament-size: 3
    early-terminate-threshold: 0.97
    global-timeout-seconds: 1
    
    # 适应度权重配置
    fitness-weights:
      score: 0.4
      quality: 0.2
      difficulty-distribution: 0.15
      cognitive-distribution: 0.15
      kp-coverage: 0.05
      topic-type-diversity: 0.05
    
    # 修复算子配置
    repair:
      enabled: true
      max-steps: 2
      greedy-threshold: 0.1
    
    # 自适应变异配置
    adaptive-mutation:
      enabled: true
      max-rate: 0.3
      min-rate: 0.05
      stagnation-threshold: 3
  
  # 内存管理配置
  memory:
    bitset-pool-size: 10
    bitset-size: 1000
    gc-frequency: 10
    memory-threshold: 0.8
  
  # 监控配置
  monitoring:
    enabled: true
    log-level: DEBUG
    metrics-collection: true
    fitness-tracking: true
    convergence-analysis: true

# 日志配置
logging:
  level:
    com.edu.maizi_edu_sys: DEBUG
    org.springframework: WARN
    org.hibernate: WARN
