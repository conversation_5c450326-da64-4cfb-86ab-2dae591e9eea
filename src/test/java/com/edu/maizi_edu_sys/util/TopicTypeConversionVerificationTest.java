package com.edu.maizi_edu_sys.util;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 题型转换验证测试
 * 专门验证前后端数据类型转换的正确性
 */
@DisplayName("题型转换验证测试")
public class TopicTypeConversionVerificationTest {

    private Map<String, String> frontendToDbMapping;
    private Map<String, String> dbToFrontendMapping;

    @BeforeEach
    void setUp() {
        // 根据您提供的准确信息配置映射关系

        // 前端实际发送的格式到数据库格式的映射
        frontendToDbMapping = new HashMap<>();
        frontendToDbMapping.put("SINGLE_CHOICE", "choice");     // 单选题
        frontendToDbMapping.put("MULTIPLE_CHOICE", "multiple"); // 多选题
        frontendToDbMapping.put("JUDGE", "judge");              // 判断题
        frontendToDbMapping.put("FILL", "fill");                // 填空题
        frontendToDbMapping.put("SHORT", "short");              // 简答题
        frontendToDbMapping.put("SUBJECTIVE", "subjective");    // 主观题
        frontendToDbMapping.put("GROUP", "group");              // 组合题

        // 数据库格式到前端格式的映射
        dbToFrontendMapping = new HashMap<>();
        dbToFrontendMapping.put("choice", "SINGLE_CHOICE");     // 单选题
        dbToFrontendMapping.put("multiple", "MULTIPLE_CHOICE"); // 多选题
        dbToFrontendMapping.put("judge", "JUDGE");              // 判断题
        dbToFrontendMapping.put("fill", "FILL");                // 填空题
        dbToFrontendMapping.put("short", "SHORT");              // 简答题
        dbToFrontendMapping.put("subjective", "SUBJECTIVE");    // 主观题
        dbToFrontendMapping.put("group", "GROUP");              // 组合题
    }

    @Test
    @DisplayName("验证前端请求格式转换")
    void testFrontendRequestConversion() {
        System.out.println("=== 验证前端请求格式转换 ===");
        
        for (Map.Entry<String, String> entry : frontendToDbMapping.entrySet()) {
            String frontendType = entry.getKey();
            String expectedDbType = entry.getValue();
            String actualDbType = TopicTypeMapper.toDbFormat(frontendType);
            
            System.out.printf("前端: %-15s -> 数据库: %-10s (期望: %-10s) %s%n", 
                frontendType, actualDbType, expectedDbType, 
                actualDbType.equals(expectedDbType) ? "✅" : "❌");
            
            assertEquals(expectedDbType, actualDbType, 
                String.format("前端格式 %s 转换失败", frontendType));
        }
    }

    @Test
    @DisplayName("验证数据库格式转换")
    void testDatabaseFormatConversion() {
        System.out.println("=== 验证数据库格式转换 ===");
        
        for (Map.Entry<String, String> entry : dbToFrontendMapping.entrySet()) {
            String dbType = entry.getKey();
            String expectedFrontendType = entry.getValue();
            String actualFrontendType = TopicTypeMapper.toFrontendFormat(dbType);
            
            System.out.printf("数据库: %-10s -> 前端: %-15s (期望: %-15s) %s%n", 
                dbType, actualFrontendType, expectedFrontendType, 
                actualFrontendType.equals(expectedFrontendType) ? "✅" : "❌");
            
            assertEquals(expectedFrontendType, actualFrontendType, 
                String.format("数据库格式 %s 转换失败", dbType));
        }
    }

    @Test
    @DisplayName("验证完整数据流转")
    void testCompleteDataFlow() {
        System.out.println("=== 验证完整数据流转 ===");
        
        // 模拟前端发送的题型配置
        Map<String, Integer> frontendRequest = new HashMap<>();
        frontendRequest.put("SINGLE_CHOICE", 10);
        frontendRequest.put("MULTIPLE_CHOICE", 5);
        frontendRequest.put("JUDGE", 8);
        frontendRequest.put("FILL", 6);
        frontendRequest.put("SHORT", 3);

        System.out.println("前端发送的题型配置:");
        frontendRequest.forEach((type, count) -> 
            System.out.printf("  %s: %d题%n", type, count));

        // 转换为数据库格式进行查询
        Map<String, Integer> dbQuery = new HashMap<>();
        for (Map.Entry<String, Integer> entry : frontendRequest.entrySet()) {
            String frontendType = entry.getKey();
            String dbType = TopicTypeMapper.toDbFormat(frontendType);
            dbQuery.put(dbType, entry.getValue());
        }

        System.out.println("\n转换为数据库查询格式:");
        dbQuery.forEach((type, count) -> 
            System.out.printf("  WHERE type='%s': %d题%n", type, count));

        // 模拟查询结果转换回前端格式
        Map<String, Integer> frontendResponse = new HashMap<>();
        for (Map.Entry<String, Integer> entry : dbQuery.entrySet()) {
            String dbType = entry.getKey();
            String frontendType = TopicTypeMapper.toFrontendFormat(dbType);
            frontendResponse.put(frontendType, entry.getValue());
        }

        System.out.println("\n返回给前端的格式:");
        frontendResponse.forEach((type, count) -> 
            System.out.printf("  %s: %d题%n", type, count));

        // 验证完整流转的一致性
        assertEquals(frontendRequest.size(), frontendResponse.size(), "题型数量应该保持一致");
        
        for (Map.Entry<String, Integer> entry : frontendRequest.entrySet()) {
            String type = entry.getKey();
            Integer count = entry.getValue();
            assertEquals(count, frontendResponse.get(type), 
                String.format("题型 %s 的数量在完整流转后应该保持一致", type));
        }

        System.out.println("\n✅ 完整数据流转验证通过！");
    }

    @Test
    @DisplayName("验证题型映射的对称性")
    void testMappingSymmetry() {
        System.out.println("=== 验证题型映射的对称性 ===");
        
        // 验证前端 -> 数据库 -> 前端的对称性
        for (String frontendType : frontendToDbMapping.keySet()) {
            String dbType = TopicTypeMapper.toDbFormat(frontendType);
            String backToFrontend = TopicTypeMapper.toFrontendFormat(dbType);
            
            System.out.printf("%s -> %s -> %s %s%n", 
                frontendType, dbType, backToFrontend,
                frontendType.equals(backToFrontend) ? "✅" : "❌");
            
            assertEquals(frontendType, backToFrontend, 
                String.format("题型 %s 的双向转换应该保持一致", frontendType));
        }

        // 验证数据库 -> 前端 -> 数据库的对称性
        for (String dbType : dbToFrontendMapping.keySet()) {
            String frontendType = TopicTypeMapper.toFrontendFormat(dbType);
            String backToDb = TopicTypeMapper.toDbFormat(frontendType);
            
            assertEquals(dbType, backToDb, 
                String.format("数据库题型 %s 的双向转换应该保持一致", dbType));
        }

        System.out.println("✅ 题型映射对称性验证通过！");
    }

    @Test
    @DisplayName("验证实际使用场景")
    void testRealWorldScenarios() {
        System.out.println("=== 验证实际使用场景 ===");

        // 场景1: 前端组卷请求
        System.out.println("场景1: 前端组卷请求");
        String[] frontendTypes = {"SINGLE_CHOICE", "MULTIPLE_CHOICE", "JUDGE", "FILL", "SHORT"};
        for (String type : frontendTypes) {
            String dbType = TopicTypeMapper.toDbFormat(type);
            System.out.printf("  前端请求 %s -> 数据库查询 %s ✅%n", type, dbType);
            assertNotNull(dbType);
            assertFalse(dbType.isEmpty());
        }

        // 场景2: 数据库查询结果返回
        System.out.println("\n场景2: 数据库查询结果返回");
        String[] dbTypes = {"choice", "multiple", "judge", "fill", "short"};
        for (String type : dbTypes) {
            String frontendType = TopicTypeMapper.toFrontendFormat(type);
            System.out.printf("  数据库结果 %s -> 前端显示 %s ✅%n", type, frontendType);
            assertNotNull(frontendType);
            assertFalse(frontendType.isEmpty());
        }

        // 场景3: 题型统计
        System.out.println("\n场景3: 题型统计");
        Map<String, Integer> dbStats = new HashMap<>();
        dbStats.put("choice", 100);
        dbStats.put("multiple", 50);
        dbStats.put("judge", 80);
        dbStats.put("fill", 60);
        dbStats.put("short", 30);

        Map<String, Integer> frontendStats = new HashMap<>();
        for (Map.Entry<String, Integer> entry : dbStats.entrySet()) {
            String frontendType = TopicTypeMapper.toFrontendFormat(entry.getKey());
            frontendStats.put(frontendType, entry.getValue());
        }

        System.out.println("  数据库统计 -> 前端显示:");
        frontendStats.forEach((type, count) -> 
            System.out.printf("    %s: %d题 ✅%n", type, count));

        assertEquals(5, frontendStats.size(), "统计结果应该包含5种题型");

        System.out.println("\n✅ 实际使用场景验证通过！");
    }
}
