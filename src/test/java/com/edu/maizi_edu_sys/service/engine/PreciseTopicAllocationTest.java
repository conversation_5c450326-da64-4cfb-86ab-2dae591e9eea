package com.edu.maizi_edu_sys.service.engine;

import com.edu.maizi_edu_sys.dto.KnowledgePointConfigRequest;
import com.edu.maizi_edu_sys.entity.Topic;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 精确题型分配测试
 * 验证简答题独立计算和精确题型数量控制
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("精确题型分配测试")
public class PreciseTopicAllocationTest {

    private PreciseTopicAllocator allocator;
    private List<Topic> testTopics;
    private List<KnowledgePointConfigRequest> testConfigs;

    @BeforeEach
    void setUp() {
        allocator = new PreciseTopicAllocator();
        createTestData();
    }

    private void createTestData() {
        testTopics = new ArrayList<>();

        // 知识点190的题目
        for (int i = 1; i <= 30; i++) {
            Topic topic = new Topic();
            topic.setId(i);
            topic.setKnowId(190);

            if (i <= 20) {
                topic.setType("choice");  // 20道单选题
            } else if (i <= 25) {
                topic.setType("multiple"); // 5道多选题
            } else {
                topic.setType("short");   // 5道简答题
            }

            topic.setTitle("题目 " + i);
            topic.setScore(5);
            topic.setDifficulty(0.2);
            testTopics.add(topic);
        }

        // 知识点191的题目
        for (int i = 31; i <= 50; i++) {
            Topic topic = new Topic();
            topic.setId(i);
            topic.setKnowId(191);

            if (i <= 40) {
                topic.setType("judge");   // 10道判断题
            } else {
                topic.setType("short");   // 10道简答题
            }

            topic.setTitle("题目 " + i);
            topic.setScore(3);
            topic.setDifficulty(0.3);
            testTopics.add(topic);
        }

        testConfigs = new ArrayList<>();
    }

    @Test
    @DisplayName("测试简答题独立计算")
    void testShortAnswerIndependentCalculation() {
        // 配置：知识点190需要基础题量 + 2道简答题（独立）
        KnowledgePointConfigRequest config = new KnowledgePointConfigRequest();
        config.setKnowledgeId(190L);
        config.setQuestionCount(10);      // 基础题量
        config.setShortAnswerCount(2);    // 简答题（独立）
        config.setIncludeShortAnswer(true);
        testConfigs.add(config);

        // 执行分配
        PreciseTopicAllocator.AllocationResult result =
            allocator.allocateTopics(testTopics, testConfigs);

        assertTrue(result.isSuccess(), "分配应该成功");

        List<Topic> allocatedTopics = result.getAllocatedTopics();
        assertEquals(12, allocatedTopics.size(), "应该分配12道题目（10道单选 + 2道简答）");

        // 验证题型分布
        long singleChoiceCount = allocatedTopics.stream()
            .filter(t -> "choice".equals(t.getType()))
            .count();
        long shortAnswerCount = allocatedTopics.stream()
            .filter(t -> "short".equals(t.getType()))
            .count();

        assertEquals(10, singleChoiceCount, "应该有10道单选题");
        assertEquals(2, shortAnswerCount, "应该有2道简答题");

        System.out.println("分配结果：" + result.getMessages());
    }

    @Test
    @DisplayName("测试精确题型数量控制")
    void testExactTypeQuantityControl() {
        // 配置：知识点190需要基础题量 + 简答题
        KnowledgePointConfigRequest config = new KnowledgePointConfigRequest();
        config.setKnowledgeId(190L);
        config.setQuestionCount(18);      // 基础题量
        config.setShortAnswerCount(1);    // 简答题
        config.setIncludeShortAnswer(true);
        testConfigs.add(config);

        // 执行分配
        PreciseTopicAllocator.AllocationResult result =
            allocator.allocateTopics(testTopics, testConfigs);

        assertTrue(result.isSuccess(), "分配应该成功");

        List<Topic> allocatedTopics = result.getAllocatedTopics();
        assertEquals(19, allocatedTopics.size(), "应该分配19道题目（15+3+1）");

        // 验证每种题型的精确数量
        Map<String, Long> typeCounts = new HashMap<>();
        allocatedTopics.forEach(topic -> {
            String type = normalizeType(topic.getType());
            typeCounts.put(type, typeCounts.getOrDefault(type, 0L) + 1);
        });

        assertEquals(15L, typeCounts.get("choice"), "单选题应该精确15道");
        assertEquals(3L, typeCounts.get("multiple"), "多选题应该精确3道");
        assertEquals(1L, typeCounts.get("short"), "简答题应该精确1道");
    }

    @Test
    @DisplayName("测试多知识点精确分配")
    void testMultipleKnowledgePointPreciseAllocation() {
        // 知识点190配置
        KnowledgePointConfigRequest config190 = new KnowledgePointConfigRequest();
        config190.setKnowledgeId(190L);
        config190.setQuestionCount(5);    // 基础题量
        config190.setShortAnswerCount(1);
        config190.setIncludeShortAnswer(true);
        testConfigs.add(config190);

        // 知识点191配置
        KnowledgePointConfigRequest config191 = new KnowledgePointConfigRequest();
        config191.setKnowledgeId(191L);
        config191.setQuestionCount(8);    // 基础题量
        config191.setShortAnswerCount(2);
        config191.setIncludeShortAnswer(true);
        testConfigs.add(config191);

        // 执行分配
        PreciseTopicAllocator.AllocationResult result =
            allocator.allocateTopics(testTopics, testConfigs);

        assertTrue(result.isSuccess(), "多知识点分配应该成功");

        List<Topic> allocatedTopics = result.getAllocatedTopics();
        assertEquals(16, allocatedTopics.size(), "应该分配16道题目（5+1+8+2）");

        // 按知识点分组验证
        Map<Integer, List<Topic>> topicsByKp = new HashMap<>();
        allocatedTopics.forEach(topic -> {
            topicsByKp.computeIfAbsent(topic.getKnowId(), k -> new ArrayList<>()).add(topic);
        });

        // 验证知识点190
        List<Topic> kp190Topics = topicsByKp.get(190);
        assertEquals(6, kp190Topics.size(), "知识点190应该有6道题目");

        // 验证知识点191
        List<Topic> kp191Topics = topicsByKp.get(191);
        assertEquals(10, kp191Topics.size(), "知识点191应该有10道题目");
    }

    @Test
    @DisplayName("测试题目不足情况")
    void testInsufficientTopics() {
        // 配置：要求的题目数量超过可用数量
        KnowledgePointConfigRequest config = new KnowledgePointConfigRequest();
        config.setKnowledgeId(190L);
        config.setQuestionCount(25);      // 基础题量（超过可用）
        config.setShortAnswerCount(10);   // 只有5道可用
        config.setIncludeShortAnswer(true);
        testConfigs.add(config);

        // 执行分配
        PreciseTopicAllocator.AllocationResult result =
            allocator.allocateTopics(testTopics, testConfigs);

        assertFalse(result.isSuccess(), "题目不足时分配应该失败");
        assertTrue(result.getMessages().stream()
                  .anyMatch(msg -> msg.contains("不足")), "应该包含不足的提示信息");
    }

    @Test
    @DisplayName("测试基础题目与简答题分离")
    void testBasicAndShortAnswerSeparation() {
        // 配置：只要简答题，不要基础题目
        KnowledgePointConfigRequest config = new KnowledgePointConfigRequest();
        config.setKnowledgeId(190L);
        config.setQuestionCount(0);  // 基础题目数量为0
        config.setShortAnswerCount(3);  // 只要3道简答题
        config.setIncludeShortAnswer(true);
        testConfigs.add(config);

        // 执行分配
        PreciseTopicAllocator.AllocationResult result =
            allocator.allocateTopics(testTopics, testConfigs);

        assertTrue(result.isSuccess(), "只分配简答题应该成功");

        List<Topic> allocatedTopics = result.getAllocatedTopics();
        assertEquals(3, allocatedTopics.size(), "应该只分配3道简答题");

        // 验证所有题目都是简答题
        boolean allShortAnswer = allocatedTopics.stream()
            .allMatch(topic -> "short".equals(topic.getType()));
        assertTrue(allShortAnswer, "所有分配的题目都应该是简答题");
    }

    @Test
    @DisplayName("测试题目多样性选择")
    void testTopicDiversitySelection() {
        // 创建不同难度的题目
        List<Topic> diverseTopics = new ArrayList<>();
        for (int i = 1; i <= 20; i++) {
            Topic topic = new Topic();
            topic.setId(i);
            topic.setKnowId(190);
            topic.setType("choice");
            topic.setTitle("单选题 " + i);
            topic.setScore(5);

            // 设置不同难度
            if (i <= 7) {
                topic.setDifficulty(0.1); // 简单
            } else if (i <= 14) {
                topic.setDifficulty(0.3); // 中等
            } else {
                topic.setDifficulty(0.5); // 困难
            }

            diverseTopics.add(topic);
        }

        // 配置：选择10道基础题
        KnowledgePointConfigRequest config = new KnowledgePointConfigRequest();
        config.setKnowledgeId(190L);
        config.setQuestionCount(10);  // 基础题量
        testConfigs.add(config);

        // 执行分配
        PreciseTopicAllocator.AllocationResult result =
            allocator.allocateTopics(diverseTopics, testConfigs);

        assertTrue(result.isSuccess(), "多样性选择应该成功");

        List<Topic> allocatedTopics = result.getAllocatedTopics();
        assertEquals(10, allocatedTopics.size(), "应该选择10道题目");

        // 验证难度多样性
        Set<Double> difficulties = new HashSet<>();
        allocatedTopics.forEach(topic -> difficulties.add(topic.getDifficulty()));

        assertTrue(difficulties.size() > 1, "应该包含多种难度的题目");
    }

    @Test
    @DisplayName("测试空配置处理")
    void testEmptyConfiguration() {
        // 空配置列表
        PreciseTopicAllocator.AllocationResult result1 =
            allocator.allocateTopics(testTopics, Collections.emptyList());
        assertFalse(result1.isSuccess(), "空配置应该返回失败");

        // 空题目列表
        PreciseTopicAllocator.AllocationResult result2 =
            allocator.allocateTopics(Collections.emptyList(), testConfigs);
        assertFalse(result2.isSuccess(), "空题目列表应该返回失败");
    }

    private String normalizeType(String type) {
        if (type == null) return "unknown";

        switch (type.toLowerCase()) {
            case "choice":
            case "single_choice":
                return "choice";
            case "multiple":
            case "multiple_choice":
                return "multiple";
            case "judge":
            case "judgment":
                return "judge";
            case "fill":
            case "fill_blank":
                return "fill";
            case "short":
            case "short_answer":
                return "short";
            default:
                return type.toLowerCase();
        }
    }
}
