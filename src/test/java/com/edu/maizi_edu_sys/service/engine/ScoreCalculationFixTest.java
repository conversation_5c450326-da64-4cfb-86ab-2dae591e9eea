package com.edu.maizi_edu_sys.service.engine;

import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.entity.TopicEnhancementData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 分数计算修复测试
 * 验证遗传算法使用typeScoreMap正确计算分数
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("分数计算修复测试")
public class ScoreCalculationFixTest {

    @InjectMocks
    private GeneticSolver geneticSolver;

    private List<Topic> testTopics;
    private Map<String, Integer> typeTargetCounts;
    private Map<String, Integer> typeScoreMap;
    private Map<Integer, TopicEnhancementData> enhancementDataMap;

    @BeforeEach
    void setUp() {
        // 设置遗传算法参数
        ReflectionTestUtils.setField(geneticSolver, "POPULATION_SIZE", 20);
        ReflectionTestUtils.setField(geneticSolver, "MAX_GENERATIONS", 10);
        ReflectionTestUtils.setField(geneticSolver, "MIN_GENERATIONS", 5);
        ReflectionTestUtils.setField(geneticSolver, "GLOBAL_TIMEOUT_SECONDS", 5);

        createTestData();
    }

    private void createTestData() {
        testTopics = new ArrayList<>();
        
        // 创建单选题（数据库中score=5，但typeScoreMap配置为2分）
        for (int i = 1; i <= 30; i++) {
            Topic topic = new Topic();
            topic.setId(i);
            topic.setKnowId(190);
            topic.setType("choice");
            topic.setTitle("单选题 " + i);
            topic.setScore(5); // 数据库中的分数
            topic.setDifficulty(0.2);
            testTopics.add(topic);
        }
        
        // 创建多选题（数据库中score=3，但typeScoreMap配置为1分）
        for (int i = 31; i <= 40; i++) {
            Topic topic = new Topic();
            topic.setId(i);
            topic.setKnowId(191);
            topic.setType("multiple");
            topic.setTitle("多选题 " + i);
            topic.setScore(3); // 数据库中的分数
            topic.setDifficulty(0.3);
            testTopics.add(topic);
        }
        
        // 创建判断题（数据库中score=4，但typeScoreMap配置为2分）
        for (int i = 41; i <= 55; i++) {
            Topic topic = new Topic();
            topic.setId(i);
            topic.setKnowId(86);
            topic.setType("judge");
            topic.setTitle("判断题 " + i);
            topic.setScore(4); // 数据库中的分数
            topic.setDifficulty(0.1);
            testTopics.add(topic);
        }

        // 设置题型目标数量（与前端请求一致）
        typeTargetCounts = new HashMap<>();
        typeTargetCounts.put("choice", 30);
        typeTargetCounts.put("multiple", 10);
        typeTargetCounts.put("judge", 15);

        // 设置题型分值映射（与前端请求一致）
        typeScoreMap = new HashMap<>();
        typeScoreMap.put("SINGLE_CHOICE", 2);  // 单选题2分
        typeScoreMap.put("MULTIPLE_CHOICE", 1); // 多选题1分
        typeScoreMap.put("JUDGE", 2);          // 判断题2分
        typeScoreMap.put("FILL", 0);
        typeScoreMap.put("SHORT", 0);

        // 创建增强数据
        enhancementDataMap = new HashMap<>();
        for (Topic topic : testTopics) {
            TopicEnhancementData data = new TopicEnhancementData();
            data.setTopicId(topic.getId());
            data.setUsageCount(0);
            enhancementDataMap.put(topic.getId(), data);
        }
    }

    @Test
    @DisplayName("测试使用typeScoreMap计算分数")
    void testScoreCalculationWithTypeScoreMap() {
        // 目标分数：30*2 + 10*1 + 15*2 = 60 + 10 + 30 = 100分
        int targetScore = 100;
        
        List<Topic> result = geneticSolver.solve(
            testTopics, 
            targetScore, 
            typeTargetCounts,
            null, // difficultyDistribution
            null, // cognitiveLevelDistribution
            enhancementDataMap,
            Arrays.asList(190, 191, 86), // targetKnowledgeIds
            null, // knowledgePointConfigs
            typeScoreMap // 关键：传入typeScoreMap
        );

        assertNotNull(result, "结果不应为空");
        
        // 验证题目数量
        long singleChoiceCount = result.stream().filter(t -> "choice".equals(t.getType())).count();
        long multipleChoiceCount = result.stream().filter(t -> "multiple".equals(t.getType())).count();
        long judgeCount = result.stream().filter(t -> "judge".equals(t.getType())).count();
        
        System.out.println("实际题目分布：");
        System.out.println("- 单选题：" + singleChoiceCount + " 题");
        System.out.println("- 多选题：" + multipleChoiceCount + " 题");
        System.out.println("- 判断题：" + judgeCount + " 题");
        
        // 计算实际分数（使用typeScoreMap）
        int actualScore = (int)(singleChoiceCount * 2 + multipleChoiceCount * 1 + judgeCount * 2);
        System.out.println("期望分数：" + targetScore);
        System.out.println("实际分数：" + actualScore);
        
        // 验证分数接近目标（允许一定误差）
        double scoreError = Math.abs(actualScore - targetScore) / (double)targetScore;
        assertTrue(scoreError <= 0.1, 
                  String.format("分数误差过大：期望%d，实际%d，误差%.2f%%", 
                               targetScore, actualScore, scoreError * 100));
    }

    @Test
    @DisplayName("测试不使用typeScoreMap时的分数计算")
    void testScoreCalculationWithoutTypeScoreMap() {
        // 不传入typeScoreMap，应该使用题目本身的分数
        int targetScore = 100;
        
        List<Topic> result = geneticSolver.solve(
            testTopics, 
            targetScore, 
            typeTargetCounts,
            null, // difficultyDistribution
            null, // cognitiveLevelDistribution
            enhancementDataMap,
            Arrays.asList(190, 191, 86), // targetKnowledgeIds
            null, // knowledgePointConfigs
            null  // 不传入typeScoreMap
        );

        assertNotNull(result, "结果不应为空");
        
        // 计算实际分数（使用题目本身的分数）
        int actualScore = result.stream().mapToInt(t -> t.getScore() != null ? t.getScore() : 0).sum();
        System.out.println("不使用typeScoreMap时：");
        System.out.println("期望分数：" + targetScore);
        System.out.println("实际分数：" + actualScore);
        
        // 这种情况下分数可能会偏高，因为题目本身的分数较高
        assertTrue(actualScore > 0, "实际分数应该大于0");
    }

    @Test
    @DisplayName("测试题型分值映射转换")
    void testTypeScoreMapping() {
        // 测试数据库格式到前端格式的转换
        Map<String, String> expectedMappings = new HashMap<>();
        expectedMappings.put("choice", "SINGLE_CHOICE");
        expectedMappings.put("multiple", "MULTIPLE_CHOICE");
        expectedMappings.put("judge", "JUDGE");
        expectedMappings.put("fill", "FILL");
        expectedMappings.put("short", "SHORT");

        // 通过反射调用私有方法测试转换
        try {
            java.lang.reflect.Method method = GeneticSolver.class.getDeclaredMethod(
                "convertDbTypeToFrontendType", String.class);
            method.setAccessible(true);

            for (Map.Entry<String, String> entry : expectedMappings.entrySet()) {
                String dbType = entry.getKey();
                String expectedFrontendType = entry.getValue();
                String actualFrontendType = (String) method.invoke(geneticSolver, dbType);
                
                assertEquals(expectedFrontendType, actualFrontendType,
                           String.format("数据库格式'%s'应该转换为前端格式'%s'", dbType, expectedFrontendType));
            }

        } catch (Exception e) {
            fail("题型转换测试失败: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("测试分数计算的精确性")
    void testScoreCalculationAccuracy() {
        // 创建精确的测试场景
        typeTargetCounts.clear();
        typeTargetCounts.put("choice", 20);   // 20道单选题
        typeTargetCounts.put("multiple", 5);  // 5道多选题
        typeTargetCounts.put("judge", 10);    // 10道判断题
        
        // 期望分数：20*2 + 5*1 + 10*2 = 40 + 5 + 20 = 65分
        int targetScore = 65;
        
        List<Topic> result = geneticSolver.solve(
            testTopics, 
            targetScore, 
            typeTargetCounts,
            null, null, enhancementDataMap,
            Arrays.asList(190, 191, 86),
            null, typeScoreMap
        );

        assertNotNull(result, "结果不应为空");
        
        // 统计实际题型分布
        Map<String, Long> actualTypeCounts = new HashMap<>();
        actualTypeCounts.put("choice", result.stream().filter(t -> "choice".equals(t.getType())).count());
        actualTypeCounts.put("multiple", result.stream().filter(t -> "multiple".equals(t.getType())).count());
        actualTypeCounts.put("judge", result.stream().filter(t -> "judge".equals(t.getType())).count());
        
        // 计算实际分数
        int actualScore = (int)(actualTypeCounts.get("choice") * 2 + 
                               actualTypeCounts.get("multiple") * 1 + 
                               actualTypeCounts.get("judge") * 2);
        
        System.out.println("精确性测试结果：");
        System.out.println("题型分布：" + actualTypeCounts);
        System.out.println("期望分数：" + targetScore + "，实际分数：" + actualScore);
        
        // 验证分数精确性（允许小误差）
        assertTrue(Math.abs(actualScore - targetScore) <= 5, 
                  "分数应该接近目标值（误差≤5分）");
    }

    @Test
    @DisplayName("测试空typeScoreMap的处理")
    void testEmptyTypeScoreMap() {
        Map<String, Integer> emptyTypeScoreMap = new HashMap<>();
        
        List<Topic> result = geneticSolver.solve(
            testTopics, 100, typeTargetCounts,
            null, null, enhancementDataMap,
            Arrays.asList(190, 191, 86),
            null, emptyTypeScoreMap
        );

        assertNotNull(result, "空typeScoreMap时结果不应为空");
        assertTrue(result.size() > 0, "应该选择一些题目");
    }
}
