package com.edu.maizi_edu_sys.service.engine;

import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.entity.TopicEnhancementData;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

import lombok.extern.slf4j.Slf4j;

/**
 * 这个测试类直接连接数据库获取真实数据，绕过Spring Boot自动配置
 */
@Slf4j
public class DirectDatabaseExamTest {

    private GeneticSolver geneticSolver;
    private ObjectMapper objectMapper = new ObjectMapper();
    
    // 数据库连接配置 - 请根据实际情况修改
    private static final String DB_URL = "***************************************************************************";
    private static final String DB_USER = "root";
    private static final String DB_PASSWORD = "Hilury157195!"; // 请修改为您的数据库密码

    @BeforeEach
    public void setup() {
        // 初始化GeneticSolver
        geneticSolver = new GeneticSolver();
        
        // 使用反射设置遗传算法参数
        ReflectionTestUtils.setField(geneticSolver, "POPULATION_SIZE", 50);
        ReflectionTestUtils.setField(geneticSolver, "MAX_GENERATIONS", 100);
        ReflectionTestUtils.setField(geneticSolver, "MIN_GENERATIONS", 20);
        ReflectionTestUtils.setField(geneticSolver, "CROSSOVER_RATE", 0.8);
        ReflectionTestUtils.setField(geneticSolver, "MUTATION_RATE", 0.1);
        ReflectionTestUtils.setField(geneticSolver, "TOURNAMENT_SIZE", 5);
        ReflectionTestUtils.setField(geneticSolver, "WEIGHT_SCORE", 0.4);
        ReflectionTestUtils.setField(geneticSolver, "WEIGHT_QUALITY", 0.2);
        ReflectionTestUtils.setField(geneticSolver, "WEIGHT_DIFFICULTY_DIST", 0.2);
        ReflectionTestUtils.setField(geneticSolver, "WEIGHT_COGNITIVE_DIST", 0.2);
        ReflectionTestUtils.setField(geneticSolver, "EARLY_TERMINATE_THRESHOLD", 0.95);
    }

    @Test
    @DisplayName("使用真实数据库数据生成试卷")
    public void generateExamWithRealData() throws IOException {
        // 从数据库加载题目
        List<Topic> allTopics = loadTopicsFromDatabase();
        
        if (allTopics.isEmpty()) {
            System.out.println("无法从数据库加载题目。测试终止。");
            return;
        }
        
        // 打印题库统计信息
        printTopicsInformation(allTopics);
        
        // 设置试卷生成参数
        int targetScore = 100; // 总分100分
        Map<String, Integer> typeScores = new HashMap<>();
        typeScores.put("单选题", 40); // 单选题总分40分
        typeScores.put("多选题", 30); // 多选题总分30分
        typeScores.put("判断题", 30); // 判断题总分30分
        
        Map<String, Double> difficultyDistribution = new HashMap<>();
        difficultyDistribution.put("easy", 0.3);    // 30%简单题
        difficultyDistribution.put("medium", 0.5);  // 50%中等题
        difficultyDistribution.put("hard", 0.2);    // 20%困难题
        
        // 生成认知层次数据 (在实际数据库中可能没有这个字段，这里模拟生成)
        Map<Integer, TopicEnhancementData> enhancementDataMap = createEnhancementData(allTopics);
        
        // 认知层次分布
        Map<String, Double> cognitiveLevelDistribution = new HashMap<>();
        cognitiveLevelDistribution.put("理解", 0.4); // 40%理解层次题目
        cognitiveLevelDistribution.put("应用", 0.3); // 30%应用层次题目
        cognitiveLevelDistribution.put("分析", 0.2); // 20%分析层次题目
        cognitiveLevelDistribution.put("评价", 0.1); // 10%评价层次题目
        
        // 提取所有知识点ID作为目标覆盖
        List<Integer> targetKnowledgeIds = allTopics.stream()
                                                  .map(Topic::getKnowId)
                                                  .filter(Objects::nonNull)
                                                  .distinct()
                                                  .collect(Collectors.toList());
        if (targetKnowledgeIds.isEmpty()) {
            // 如果没有有效的知识点ID（例如，所有knowId都为null），则传入一个虚拟的或空列表
            // 否则，geneticSolver中的逻辑可能会因targetKnowledgeIds为空而直接返回1.0适应度
            // 这里添加一个虚拟ID，确保覆盖逻辑被测试，或者传入Collections.emptyList()如果允许
            // 对于测试，最好确保至少有一个ID，除非空列表是有效场景
            // targetKnowledgeIds.add(-1); // Placeholder if no knowIds found but want to test logic
            log.warn("No valid knowledge IDs found in the loaded topics for the test. Passing an empty list for targetKnowledgeIds.");
        }

        // 执行遗传算法
        List<Topic> selectedTopics = geneticSolver.solve(
                allTopics,
                targetScore,
                typeScores,
                difficultyDistribution,
                cognitiveLevelDistribution,
                enhancementDataMap,
                targetKnowledgeIds // Pass the collected knowledge IDs
        );
        
        // 验证结果
        assertNotNull(selectedTopics, "生成的试卷不能为空");
        assertFalse(selectedTopics.isEmpty(), "生成的试卷题目不能为空");
        
        // 分析总分
        int totalScore = selectedTopics.stream().mapToInt(Topic::getScore).sum();
        System.out.printf("试卷总分: %d (目标: %d)\n", totalScore, targetScore);
        
        // 分析每种题型的数量
        Map<String, Long> typeCount = selectedTopics.stream()
                .collect(Collectors.groupingBy(Topic::getType, Collectors.counting()));
        
        // 分析每种题型的总分
        Map<String, Integer> actualTypeScores = selectedTopics.stream()
                .collect(Collectors.groupingBy(Topic::getType, 
                        Collectors.summingInt(Topic::getScore)));
        
        System.out.println("\n题型分布:");
        typeScores.forEach((type, targetTypeScore) -> {
            long count = typeCount.getOrDefault(type, 0L);
            int actualScore = actualTypeScores.getOrDefault(type, 0);
            System.out.printf("%s: %d题, 目标分数 %d, 实际分数 %d\n", 
                    type, count, targetTypeScore, actualScore);
        });
        
        // 保存试卷到文件
        Map<String, List<Topic>> topicsByType = selectedTopics.stream()
                .collect(Collectors.groupingBy(Topic::getType));
        
        saveExamToFile(topicsByType, totalScore);
    }
    
    /**
     * 从数据库直接加载题目
     */
    private List<Topic> loadTopicsFromDatabase() {
        List<Topic> topics = new ArrayList<>();
        
        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD)) {
            System.out.println("数据库连接成功！");
            
            // 准备SQL查询
            String sql = "SELECT * FROM topic_bak LIMIT 1000"; // 限制加载1000道题目
            
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(sql)) {
                
                int count = 0;
                while (rs.next()) {
                    Topic topic = new Topic();
                    topic.setId(rs.getInt("id"));
                    topic.setKnowId(rs.getInt("know_id"));
                    topic.setType(rs.getString("type"));
                    topic.setTitle(rs.getString("title"));
                    topic.setOptions(rs.getString("options"));
                    topic.setSubs(rs.getString("subs"));
                    topic.setAnswer(rs.getString("answer"));
                    topic.setParse(rs.getString("parse"));
                    topic.setScore(rs.getInt("score"));
                    topic.setSource(rs.getString("source"));
                    topic.setSort(rs.getInt("sort"));
                    topic.setDifficulty(rs.getDouble("difficulty"));
                    
                    // 将中文题型映射成标准类型名称
                    String standardType = mapToStandardType(topic.getType());
                    topic.setType(standardType);
                    
                    topics.add(topic);
                    count++;
                }
                
                System.out.println("从数据库成功加载 " + count + " 道题目");
            }
        } catch (SQLException e) {
            System.err.println("数据库操作失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return topics;
    }
    
    /**
     * 将数据库中的题型映射为标准类型名称
     */
    private String mapToStandardType(String originalType) {
        switch (originalType.trim()) {
            case "choice":
            case "单选":
            case "单项选择题":
            case "单选题":
                return "单选题";
                
            case "multiple":
            case "多选":
            case "多项选择题":
            case "多选题":
                return "多选题";
                
            case "judge":
            case "判断":
            case "判断题":
                return "判断题";
                
            default:
                return originalType;
        }
    }
    
    /**
     * 为题目创建增强数据(认知层次等)
     */
    private Map<Integer, TopicEnhancementData> createEnhancementData(List<Topic> topics) {
        Map<Integer, TopicEnhancementData> dataMap = new HashMap<>();
        
        for (Topic topic : topics) {
            TopicEnhancementData data = new TopicEnhancementData();
            data.setTopicId(topic.getId());
            
            // 根据难度分配认知层次
            String cognitiveLevel;
            double difficulty = topic.getDifficulty();
            if (difficulty <= 0.2) {
                cognitiveLevel = "理解";
            } else if (difficulty <= 0.3) {
                cognitiveLevel = "应用";
            } else if (difficulty <= 0.4) {
                cognitiveLevel = "应用";
            } else if (difficulty <= 0.45) {
                cognitiveLevel = "分析";
            } else {
                cognitiveLevel = "评价";
            }
            data.setCognitiveLevel(cognitiveLevel);
            dataMap.put(topic.getId(), data);
        }
        
        return dataMap;
    }
    
    /**
     * 保存试卷到HTML文件
     */
    private void saveExamToFile(Map<String, List<Topic>> topicsByType, int totalScore) throws IOException {
        // 创建resources/paper目录(如果不存在)
        File paperDir = new File("src/main/resources/paper");
        if (!paperDir.exists()) {
            paperDir.mkdirs();
        }
        
        // 生成文件名
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        File examFile = new File(paperDir, "real_data_exam_" + timestamp + ".html");
        
        try (FileWriter writer = new FileWriter(examFile)) {
            // HTML头部
            writer.write("<!DOCTYPE html>\n");
            writer.write("<html lang=\"zh-CN\">\n");
            writer.write("<head>\n");
            writer.write("    <meta charset=\"UTF-8\">\n");
            writer.write("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
            writer.write("    <title>真实数据试卷 - " + timestamp + "</title>\n");
            writer.write("    <style>\n");
            writer.write("        body { font-family: SimSun, sans-serif; line-height: 1.5; }\n");
            writer.write("        .exam-header { text-align: center; margin-bottom: 20px; }\n");
            writer.write("        .section { margin-bottom: 20px; }\n");
            writer.write("        .section-title { font-weight: bold; margin-bottom: 10px; }\n");
            writer.write("        .question { margin-bottom: 15px; }\n");
            writer.write("        .question-title { font-weight: normal; }\n");
            writer.write("        .options { margin-left: 20px; }\n");
            writer.write("        .answer-key { border-top: 1px solid #ccc; margin-top: 30px; padding-top: 20px; }\n");
            writer.write("        .hidden { display: none; }\n");
            writer.write("        button { margin: 20px 0; padding: 8px 16px; }\n");
            writer.write("    </style>\n");
            writer.write("</head>\n");
            writer.write("<body>\n");
            
            // 试卷头部
            writer.write("    <div class=\"exam-header\">\n");
            writer.write("        <h1>真实数据测试试卷</h1>\n");
            writer.write("        <p>总分：" + totalScore + "分 &nbsp;&nbsp; 考试时间：120分钟</p>\n");
            writer.write("        <p>日期：" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")) + "</p>\n");
            writer.write("    </div>\n");
            
            // 按题型组织试卷内容
            int questionNumber = 1;
            
            // 1. 单选题
            if (topicsByType.containsKey("单选题")) {
                List<Topic> singleChoiceQuestions = topicsByType.get("单选题");
                writer.write("    <div class=\"section\">\n");
                writer.write("        <div class=\"section-title\">一、单项选择题（每题1分，共" + singleChoiceQuestions.size() + "题，共" + 
                        singleChoiceQuestions.stream().mapToInt(Topic::getScore).sum() + "分）</div>\n");
                
                for (Topic topic : singleChoiceQuestions) {
                    writeQuestionToFile(writer, topic, questionNumber++);
                }
                
                writer.write("    </div>\n");
            }
            
            // 2. 多选题
            if (topicsByType.containsKey("多选题")) {
                List<Topic> multipleChoiceQuestions = topicsByType.get("多选题");
                writer.write("    <div class=\"section\">\n");
                writer.write("        <div class=\"section-title\">二、多项选择题（每题2分，共" + multipleChoiceQuestions.size() + "题，共" + 
                        multipleChoiceQuestions.stream().mapToInt(Topic::getScore).sum() + "分）</div>\n");
                
                for (Topic topic : multipleChoiceQuestions) {
                    writeQuestionToFile(writer, topic, questionNumber++);
                }
                
                writer.write("    </div>\n");
            }
            
            // 3. 判断题
            if (topicsByType.containsKey("判断题")) {
                List<Topic> judgmentQuestions = topicsByType.get("判断题");
                writer.write("    <div class=\"section\">\n");
                writer.write("        <div class=\"section-title\">三、判断题（每题1分，共" + judgmentQuestions.size() + "题，共" + 
                        judgmentQuestions.stream().mapToInt(Topic::getScore).sum() + "分）</div>\n");
                
                for (Topic topic : judgmentQuestions) {
                    writeQuestionToFile(writer, topic, questionNumber++);
                }
                
                writer.write("    </div>\n");
            }
            
            // 答案部分
            writer.write("    <button onclick=\"toggleAnswers()\">显示/隐藏答案</button>\n");
            writer.write("    <div id=\"answer-key\" class=\"answer-key hidden\">\n");
            writer.write("        <h2>参考答案与解析</h2>\n");
            
            // 重置问题编号
            questionNumber = 1;
            
            // 添加单选题答案
            if (topicsByType.containsKey("单选题")) {
                writer.write("        <div class=\"section\">\n");
                writer.write("            <div class=\"section-title\">一、单项选择题</div>\n");
                
                for (Topic topic : topicsByType.get("单选题")) {
                    writeAnswerToFile(writer, topic, questionNumber++);
                }
                
                writer.write("        </div>\n");
            }
            
            // 添加多选题答案
            if (topicsByType.containsKey("多选题")) {
                writer.write("        <div class=\"section\">\n");
                writer.write("            <div class=\"section-title\">二、多项选择题</div>\n");
                
                for (Topic topic : topicsByType.get("多选题")) {
                    writeAnswerToFile(writer, topic, questionNumber++);
                }
                
                writer.write("        </div>\n");
            }
            
            // 添加判断题答案
            if (topicsByType.containsKey("判断题")) {
                writer.write("        <div class=\"section\">\n");
                writer.write("            <div class=\"section-title\">三、判断题</div>\n");
                
                for (Topic topic : topicsByType.get("判断题")) {
                    writeAnswerToFile(writer, topic, questionNumber++);
                }
                
                writer.write("        </div>\n");
            }
            
            writer.write("    </div>\n");
            
            // JavaScript用于显示/隐藏答案
            writer.write("    <script>\n");
            writer.write("        function toggleAnswers() {\n");
            writer.write("            var answerKey = document.getElementById('answer-key');\n");
            writer.write("            if (answerKey.classList.contains('hidden')) {\n");
            writer.write("                answerKey.classList.remove('hidden');\n");
            writer.write("            } else {\n");
            writer.write("                answerKey.classList.add('hidden');\n");
            writer.write("            }\n");
            writer.write("        }\n");
            writer.write("    </script>\n");
            
            // HTML尾部
            writer.write("</body>\n");
            writer.write("</html>");
        }
        
        System.out.println("\n真实数据试卷已保存至: " + examFile.getAbsolutePath());
    }
    
    private void writeQuestionToFile(FileWriter writer, Topic topic, int questionNumber) throws IOException {
        writer.write("        <div class=\"question\">\n");
        writer.write("            <div class=\"question-title\">" + questionNumber + ". " + topic.getTitle() + "（" + topic.getScore() + "分）</div>\n");
        
        // 写入选项
        if (topic.getType().equals("单选题") || topic.getType().equals("多选题")) {
            writer.write("            <div class=\"options\">\n");
            
            try {
                // 解析选项JSON字符串
                JsonNode optionsNode = objectMapper.readTree(topic.getOptions());
                if (optionsNode.isArray()) {
                    for (JsonNode option : optionsNode) {
                        String key = option.has("key") ? option.get("key").asText() : "";
                        String name = option.has("name") ? option.get("name").asText() : option.toString();
                        writer.write("                <div>" + key + ". " + name + "</div>\n");
                    }
                } else {
                    writer.write("                <div>选项解析错误: " + topic.getOptions() + "</div>\n");
                }
            } catch (JsonProcessingException e) {
                writer.write("                <div>选项JSON解析错误: " + e.getMessage() + "</div>\n");
                writer.write("                <div>原始选项: " + topic.getOptions() + "</div>\n");
            }
            
            writer.write("            </div>\n");
        } else if (topic.getType().equals("判断题")) {
            writer.write("            <div class=\"options\">\n");
            writer.write("                <div>A. 正确</div>\n");
            writer.write("                <div>B. 错误</div>\n");
            writer.write("            </div>\n");
        }
        
        writer.write("        </div>\n");
    }
    
    private void writeAnswerToFile(FileWriter writer, Topic topic, int questionNumber) throws IOException {
        writer.write("            <div class=\"question\">\n");
        writer.write("                <div class=\"question-title\">" + questionNumber + ". 答案：" + topic.getAnswer() + "</div>\n");
        
        if (topic.getParse() != null && !topic.getParse().trim().isEmpty()) {
            writer.write("                <div class=\"explanation\">解析：" + topic.getParse() + "</div>\n");
        } else {
            writer.write("                <div class=\"explanation\">解析：该题暂无解析。</div>\n");
        }
        
        writer.write("            </div>\n");
    }
    
    private void printTopicsInformation(List<Topic> topics) {
        System.out.println("\n============ 数据库题目集信息 ============");
        System.out.println("总题目数量: " + topics.size());
        
        // 按难度统计
        Map<Double, Integer> difficultyCountMap = new HashMap<>();
        for (Topic topic : topics) {
            difficultyCountMap.merge(topic.getDifficulty(), 1, Integer::sum);
        }
        
        System.out.println("\n按难度值分布:");
        difficultyCountMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> {
                    double percent = (double) entry.getValue() / topics.size() * 100;
                    System.out.printf("难度 %.1f: %d题 (%.1f%%)\n", 
                            entry.getKey(), entry.getValue(), percent);
                });
        
        // 按难度类别统计
        Map<String, List<Topic>> topicsByCategory = new HashMap<>();
        topicsByCategory.put("easy", new ArrayList<>());
        topicsByCategory.put("medium", new ArrayList<>());
        topicsByCategory.put("hard", new ArrayList<>());
        
        for (Topic topic : topics) {
            String category = getDifficultyCategory(topic.getDifficulty());
            topicsByCategory.get(category).add(topic);
        }
        
        System.out.println("\n按难度类别分布:");
        topicsByCategory.forEach((category, topicsInCategory) -> {
            double percent = (double) topicsInCategory.size() / topics.size() * 100;
            System.out.printf("%s难度: %d题 (%.1f%%)\n", 
                    category, topicsInCategory.size(), percent);
        });
        
        // 按题型统计
        Map<String, Long> typeCountMap = topics.stream()
                .collect(Collectors.groupingBy(Topic::getType, Collectors.counting()));
        
        System.out.println("\n按题型分布:");
        typeCountMap.forEach((type, count) -> {
            double percent = (double) count / topics.size() * 100;
            System.out.printf("%s: %d题 (%.1f%%)\n", type, count, percent);
        });
        
        // 打印前20个题目的详细信息
        System.out.println("\n部分题目示例:");
        topics.stream().limit(10).forEach(topic -> {
            System.out.println("------------------------------");
            System.out.printf("ID: %d, 类型: %s, 难度: %.1f, 分值: %d\n", 
                    topic.getId(), topic.getType(), topic.getDifficulty(), topic.getScore());
            System.out.println("题目: " + topic.getTitle());
            System.out.println("选项: " + topic.getOptions());
            System.out.println("答案: " + topic.getAnswer());
        });
        
        System.out.println("============ 数据库题目信息结束 ============\n");
    }
    
    // Helper method for difficulty categorization
    private String getDifficultyCategory(double difficulty) {
        if (difficulty <= 0.2) return "easy";
        if (difficulty <= 0.4) return "medium";
        return "hard";
    }
} 