package com.edu.maizi_edu_sys.service.engine;

import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.entity.TopicEnhancementData;
import com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper;
import com.edu.maizi_edu_sys.repository.TopicMapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
public class GeneticSolverTest {

    @Autowired
    private GeneticSolver geneticSolver;

    @Autowired
    private TopicMapper topicMapper;

    @Autowired
    private TopicEnhancementDataMapper enhancementDataMapper;

    @Test
    @DisplayName("测试遗传算法难度分布-使用真实数据")
    public void testDifficultyDistributionWithRealData() {
        // 1. 获取真实数据用于测试
        List<Integer> knowIds = Arrays.asList(218,59,24,36,117); // 替换为实际的知识点ID
        List<Integer> topicIds = new ArrayList<>();
        
        for (Integer knowId : knowIds) {
            List<Integer> ids = topicMapper.findAnyTopicsByKnowledgeId(knowId);
            if (ids != null && !ids.isEmpty()) {
                topicIds.addAll(ids);
            }
        }
        
        if (topicIds.isEmpty()) {
            System.out.println("警告：未找到测试数据，测试将使用模拟数据");
            // 如果没有数据，创建一些测试用的题目
            topicIds = createMockTopicIds();
        }
        
        List<Topic> topics = topicMapper.selectBatchIds(topicIds);
        System.out.println("测试数据: " + topics.size() + "题");
        
        // 记录难度分布
        Map<Double, Long> difficultyDistribution = topics.stream()
                .collect(Collectors.groupingBy(Topic::getDifficulty, Collectors.counting()));
        
        System.out.println("数据库中题目难度分布:");
        difficultyDistribution.forEach((diff, count) -> 
            System.out.println(String.format("难度 %.1f: %d题 (%.1f%%)", 
                diff, count, (count * 100.0 / topics.size()))));
                
        // 2. 设置遗传算法参数
        int targetScore = 100;
        Map<String, Integer> typeTargetScores = new HashMap<>();
        typeTargetScores.put("单选题", 40);
        typeTargetScores.put("多选题", 30);
        typeTargetScores.put("填空题", 30);
        
        // 设置期望的难度分布
        Map<String, Double> difficultyDistributionTarget = new HashMap<>();
        difficultyDistributionTarget.put("easy", 0.3);    // 简单题占30%
        difficultyDistributionTarget.put("medium", 0.5);  // 中等题占50%
        difficultyDistributionTarget.put("hard", 0.2);    // 困难题占20%
        
        // 获取增强数据
        Map<Integer, TopicEnhancementData> enhancementDataMap = fetchEnhancementData(topicIds);
        
        // 提取所有知识点ID作为目标覆盖
        List<Integer> targetKnowledgeIds = topics.stream()
                                               .map(Topic::getKnowId)
                                               .filter(Objects::nonNull)
                                               .distinct()
                                               .collect(Collectors.toList());
        if (targetKnowledgeIds.isEmpty() && !topics.isEmpty()) {
            System.out.println("Warning: No distinct knowledge IDs found in topics for GeneticSolverTest. Test might not cover KP fitness adequately.");
            // Optionally, add a default ID if your test logic strictly requires it
            // targetKnowledgeIds.add(knowIds.get(0)); // Or a known existing ID from your DB for testing
        }
        
        // 3. 执行遗传算法
        List<Topic> result = geneticSolver.solve(
                topics,
                targetScore,
                typeTargetScores,
                difficultyDistributionTarget,
                Collections.emptyMap(), // 认知层次分布暂不测试
                enhancementDataMap,
                targetKnowledgeIds // Pass the collected knowledge IDs
        );
        
        assertNotNull(result, "遗传算法应返回非空结果");
        
        if (result.isEmpty()) {
            fail("遗传算法返回空结果");
        }
        
        // 4. 分析结果的难度分布
        Map<String, List<Topic>> resultByDifficultyCategory = new HashMap<>();
        resultByDifficultyCategory.put("easy", new ArrayList<>());
        resultByDifficultyCategory.put("medium", new ArrayList<>());
        resultByDifficultyCategory.put("hard", new ArrayList<>());
        
        for (Topic topic : result) {
            double difficulty = topic.getDifficulty();
            String category = getDifficultyCategory(difficulty);
            resultByDifficultyCategory.get(category).add(topic);
        }
        
        // 5. 输出结果并验证
        System.out.println("\n遗传算法选择结果: " + result.size() + "题，总分: " + 
                result.stream().mapToInt(Topic::getScore).sum());
        
        System.out.println("难度分布结果:");
        difficultyDistributionTarget.forEach((difficulty, targetPercent) -> {
            List<Topic> topicsInCategory = resultByDifficultyCategory.get(difficulty);
            double actualPercent = (double) topicsInCategory.size() / result.size();
            System.out.println(String.format("%s难度: 目标 %.1f%%, 实际 %.1f%% (%d题)", 
                    difficulty, targetPercent * 100, actualPercent * 100, topicsInCategory.size()));
            
            // 验证误差是否在可接受范围内 (误差不超过15%)
            assertTrue(Math.abs(targetPercent - actualPercent) <= 0.15, 
                    difficulty + "难度分布误差过大: 目标" + targetPercent + ", 实际" + actualPercent);
        });
        
        // 输出详细难度分布
        System.out.println("\n结果中的精确难度分布:");
        result.stream()
              .collect(Collectors.groupingBy(Topic::getDifficulty, Collectors.counting()))
              .forEach((diff, count) -> 
                  System.out.println(String.format("难度 %.1f: %d题 (%.1f%%)", 
                      diff, count, (count * 100.0 / result.size()))));
    }
    
    // 辅助方法：创建模拟题目ID
    private List<Integer> createMockTopicIds() {
        return Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8, 9, 10);
    }
    
    // 辅助方法：获取增强数据
    private Map<Integer, TopicEnhancementData> fetchEnhancementData(List<Integer> topicIds) {
        try {
            return enhancementDataMapper.selectBatchIds(topicIds)
                .stream()
                .collect(Collectors.toMap(
                    TopicEnhancementData::getTopicId, 
                    data -> data, 
                    (existing, replacement) -> existing
                ));
        } catch (Exception e) {
            System.out.println("获取增强数据失败: " + e.getMessage());
            return Collections.emptyMap();
        }
    }
    
    // 辅助方法：根据数值难度确定分类
    private String getDifficultyCategory(double difficulty) {
        if (difficulty <= 0.2) return "easy";
        if (difficulty <= 0.4) return "medium";
        return "hard";
    }
    
    @Test
    @DisplayName("测试改进的难度分布解释方法")
    public void testImprovedDifficultyInterpretation() {
        // 测试improved方法对边界情况的处理
        assertEquals("easy", getDifficultyCategory(0.1));
        assertEquals("easy", getDifficultyCategory(0.2));
        assertEquals("medium", getDifficultyCategory(0.3));
        assertEquals("medium", getDifficultyCategory(0.4));
        assertEquals("hard", getDifficultyCategory(0.5));
    }
} 