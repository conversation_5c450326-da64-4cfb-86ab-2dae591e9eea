package com.edu.maizi_edu_sys.service;

import com.edu.maizi_edu_sys.entity.KnowledgePoint;
import com.edu.maizi_edu_sys.repository.KnowledgePointRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 知识点修复测试
 * 验证知识点表名和字段映射是否正确
 */
@SpringBootTest
@ActiveProfiles("test")
public class KnowledgePointFixTest {

    @Autowired
    private KnowledgePointRepository knowledgePointRepository;

    @Test
    public void testKnowledgePointTableMapping() {
        try {
            // 测试基本查询是否能正常工作
            List<KnowledgePoint> allKnowledgePoints = knowledgePointRepository.selectList(null);
            System.out.println("✅ 成功查询知识点表，共找到 " + allKnowledgePoints.size() + " 条记录");
            
            // 如果有数据，验证字段映射
            if (!allKnowledgePoints.isEmpty()) {
                KnowledgePoint firstKp = allKnowledgePoints.get(0);
                System.out.println("✅ 第一个知识点信息:");
                System.out.println("   ID: " + firstKp.getId());
                System.out.println("   Knowledge ID: " + firstKp.getKnowledgeId());
                System.out.println("   Name: " + firstKp.getName());
                System.out.println("   Group Name: " + firstKp.getGroupName());
                System.out.println("   Is Free: " + firstKp.getIsFree());
                System.out.println("   Is Deleted: " + firstKp.getIsDeleted());
                
                // 验证必要字段不为空
                assertNotNull(firstKp.getId(), "ID不应为空");
                assertNotNull(firstKp.getKnowledgeId(), "Knowledge ID不应为空");
                assertNotNull(firstKp.getName(), "Name不应为空");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 知识点表查询失败: " + e.getMessage());
            e.printStackTrace();
            fail("知识点表查询失败: " + e.getMessage());
        }
    }

    @Test
    public void testBatchSelectByIds() {
        try {
            // 测试批量查询功能
            List<Integer> testIds = Arrays.asList(190, 191, 192, 193, 194);
            List<KnowledgePoint> knowledgePoints = knowledgePointRepository.selectBatchIds(testIds);
            
            System.out.println("✅ 批量查询测试成功，查询ID: " + testIds);
            System.out.println("✅ 返回结果数量: " + knowledgePoints.size());
            
            for (KnowledgePoint kp : knowledgePoints) {
                System.out.println("   - ID: " + kp.getId() + 
                                 ", Knowledge ID: " + kp.getKnowledgeId() + 
                                 ", Name: " + kp.getName());
            }
            
        } catch (Exception e) {
            System.err.println("❌ 批量查询失败: " + e.getMessage());
            e.printStackTrace();
            fail("批量查询失败: " + e.getMessage());
        }
    }

    @Test
    public void testKnowledgePointsWithQuestions() {
        try {
            // 测试查询有题目的知识点
            List<Long> testIds = Arrays.asList(190L, 191L, 192L, 193L, 194L);
            List<Long> kpWithQuestions = knowledgePointRepository.findKnowledgePointsWithQuestions(testIds);
            
            System.out.println("✅ 查询有题目的知识点测试成功");
            System.out.println("✅ 输入ID: " + testIds);
            System.out.println("✅ 有题目的知识点ID: " + kpWithQuestions);
            
        } catch (Exception e) {
            System.err.println("❌ 查询有题目的知识点失败: " + e.getMessage());
            e.printStackTrace();
            fail("查询有题目的知识点失败: " + e.getMessage());
        }
    }
}
