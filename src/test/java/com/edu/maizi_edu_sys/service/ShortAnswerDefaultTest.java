package com.edu.maizi_edu_sys.service;

import com.edu.maizi_edu_sys.dto.KnowledgePointConfigRequest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 简答题开关默认值测试
 * 验证简答题开关默认为关闭状态
 */
@DisplayName("简答题开关默认值测试")
public class ShortAnswerDefaultTest {

    @Test
    @DisplayName("测试KnowledgePointConfigRequest默认值")
    void testKnowledgePointConfigRequestDefaults() {
        // 创建新的配置对象
        KnowledgePointConfigRequest config = new KnowledgePointConfigRequest();

        // 验证includeShortAnswer默认为false
        assertFalse(config.getIncludeShortAnswer(),
                   "includeShortAnswer应该默认为false");
    }

    @Test
    @DisplayName("测试KnowledgePointConfigRequest构造函数")
    void testKnowledgePointConfigRequestConstructor() {
        // 使用setter方法设置值
        KnowledgePointConfigRequest config = new KnowledgePointConfigRequest();
        config.setKnowledgeId(1L);
        config.setQuestionCount(5);
        config.setIncludeShortAnswer(true);

        // 验证设置的值
        assertEquals(1L, config.getKnowledgeId());
        assertEquals(5, config.getQuestionCount());
        assertTrue(config.getIncludeShortAnswer());

        // 创建另一个对象，明确设置为false
        KnowledgePointConfigRequest config2 = new KnowledgePointConfigRequest();
        config2.setKnowledgeId(2L);
        config2.setQuestionCount(3);
        config2.setIncludeShortAnswer(false);
        assertFalse(config2.getIncludeShortAnswer(),
                   "明确设置为false时应该为false");
    }

    @Test
    @DisplayName("测试设置includeShortAnswer值")
    void testSetIncludeShortAnswer() {
        KnowledgePointConfigRequest config = new KnowledgePointConfigRequest();

        // 验证默认值
        assertFalse(config.getIncludeShortAnswer(),
                   "默认值应该为false");

        // 设置为true
        config.setIncludeShortAnswer(true);
        assertTrue(config.getIncludeShortAnswer(),
                  "设置为true后应该为true");

        // 设置为false
        config.setIncludeShortAnswer(false);
        assertFalse(config.getIncludeShortAnswer(),
                   "设置为false后应该为false");
    }

    @Test
    @DisplayName("测试null值处理")
    void testNullValueHandling() {
        KnowledgePointConfigRequest config = new KnowledgePointConfigRequest();

        // 设置为null（虽然有@NotNull注解，但测试边界情况）
        config.setIncludeShortAnswer(null);
        assertNull(config.getIncludeShortAnswer(),
                  "设置为null时应该为null");

        // 重新设置默认值
        config = new KnowledgePointConfigRequest();
        assertNotNull(config.getIncludeShortAnswer(),
                     "默认值不应该为null");
        assertFalse(config.getIncludeShortAnswer(),
                   "默认值应该为false");
    }

    @Test
    @DisplayName("测试多个配置对象的独立性")
    void testMultipleConfigIndependence() {
        // 创建多个配置对象
        KnowledgePointConfigRequest config1 = new KnowledgePointConfigRequest();
        KnowledgePointConfigRequest config2 = new KnowledgePointConfigRequest();
        KnowledgePointConfigRequest config3 = new KnowledgePointConfigRequest();

        // 验证所有对象的默认值都是false
        assertFalse(config1.getIncludeShortAnswer(), "config1默认值应该为false");
        assertFalse(config2.getIncludeShortAnswer(), "config2默认值应该为false");
        assertFalse(config3.getIncludeShortAnswer(), "config3默认值应该为false");

        // 修改其中一个对象
        config2.setIncludeShortAnswer(true);

        // 验证其他对象不受影响
        assertFalse(config1.getIncludeShortAnswer(), "config1不应该受到config2修改的影响");
        assertTrue(config2.getIncludeShortAnswer(), "config2应该为true");
        assertFalse(config3.getIncludeShortAnswer(), "config3不应该受到config2修改的影响");
    }

    @Test
    @DisplayName("测试边界值")
    void testBoundaryValues() {
        KnowledgePointConfigRequest config = new KnowledgePointConfigRequest();

        // 测试布尔值的两个边界
        config.setIncludeShortAnswer(Boolean.TRUE);
        assertTrue(config.getIncludeShortAnswer(), "Boolean.TRUE应该为true");

        config.setIncludeShortAnswer(Boolean.FALSE);
        assertFalse(config.getIncludeShortAnswer(), "Boolean.FALSE应该为false");

        // 测试原始类型
        config.setIncludeShortAnswer(true);
        assertTrue(config.getIncludeShortAnswer(), "原始类型true应该为true");

        config.setIncludeShortAnswer(false);
        assertFalse(config.getIncludeShortAnswer(), "原始类型false应该为false");
    }
}
