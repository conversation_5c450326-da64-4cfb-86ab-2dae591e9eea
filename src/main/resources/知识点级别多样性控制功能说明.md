# 知识点级别多样性控制功能说明

## 功能概述

本功能在原有的全局题目重复率控制基础上，增加了知识点级别的细粒度多样性控制，确保同一知识点内的题目也能满足重用间隔要求，进一步提高试卷的多样性和学习效果。

## 核心特性

### 1. 多层次重复率控制
- **全局级别**：控制整个题库的题目重用间隔
- **知识点级别**：控制单个知识点内部的题目重用间隔
- **智能优先级**：基于使用频率和时间的智能题目选择

### 2. 配置化参数管理
所有参数都可以通过 `application.yml` 配置文件进行调整：

```yaml
algorithm:
  diversity:
    knowledge-point-level:
      enabled: true                           # 是否启用知识点级别多样性控制
      min-reuse-interval-days: 3              # 知识点内部最小重用间隔（天）
      max-topics-per-knowledge-point: 10      # 每个知识点最大题目数量
      priority-weight-usage: 10.0             # 使用次数权重
      priority-weight-time: 1.0               # 时间权重
```

### 3. 智能过滤策略
- **时间间隔过滤**：排除在指定时间内使用过的题目
- **使用频率优化**：优先选择使用次数较少的题目
- **知识点内平衡**：确保每个知识点内部题目的多样性

## 技术实现

### 1. 核心组件

#### DiversityFilter 增强
- 新增 `filterByKnowledgePoint()` 方法：按知识点分组进行多样性过滤
- 新增 `smartFilter()` 方法：根据配置自动选择过滤策略
- 新增 `prioritizeTopicsByUsageAndTime()` 方法：智能题目优先级排序

#### KnowledgePointUsageTracker
- 专门负责知识点级别的使用统计跟踪
- 提供知识点多样性分析功能
- 记录知识点级别的使用模式

### 2. 工作流程

```
1. 题目候选池构建
   ↓
2. 全局重用间隔过滤
   ↓
3. 知识点级别分组
   ↓
4. 知识点内部重用间隔过滤
   ↓
5. 智能优先级排序
   ↓
6. 知识点题目数量限制
   ↓
7. 遗传算法优化
   ↓
8. 使用统计更新
```

### 3. 优先级计算算法

题目优先级分数计算公式：
```
优先级分数 = 使用次数 × 使用次数权重 + max(0, 重用间隔 - 实际间隔) × 时间权重
```

分数越低的题目优先级越高，越容易被选中。

## 配置参数详解

### enabled
- **类型**：boolean
- **默认值**：true
- **说明**：是否启用知识点级别的多样性控制。设为false时只使用全局过滤。

### min-reuse-interval-days
- **类型**：int
- **默认值**：3
- **说明**：知识点内部的最小重用间隔天数。同一知识点内的题目在此时间内不会重复使用。

### max-topics-per-knowledge-point
- **类型**：int
- **默认值**：10
- **说明**：每个知识点在单次组卷中的最大题目数量限制。

### priority-weight-usage
- **类型**：double
- **默认值**：10.0
- **说明**：使用次数在优先级计算中的权重。值越大，使用次数对优先级的影响越大。

### priority-weight-time
- **类型**：double
- **默认值**：1.0
- **说明**：时间因素在优先级计算中的权重。值越大，最近使用时间对优先级的影响越大。

## 使用效果

### 1. 提高题目多样性
- 避免同一知识点内题目的频繁重复
- 确保学生接触到更多样化的题目类型
- 提高学习效果和考试公平性

### 2. 智能资源利用
- 优先使用较少被选择的题目
- 平衡题库中各题目的使用频率
- 延长题库的有效使用周期

### 3. 灵活配置管理
- 可根据实际需求调整各项参数
- 支持不同场景下的多样性策略
- 便于系统维护和优化

## 监控和日志

系统提供详细的日志记录，便于监控和调试：

```
DiversityFilter: Starting smart diversity filtering. KP-level enabled: true
DiversityFilter: Knowledge point level filtering enabled. KP min reuse interval: 3 days, Max topics per KP: 10
DiversityFilter: Topics grouped into 5 knowledge points
DiversityFilter: Knowledge point 101 contributed 8 topics after filtering
KnowledgePointUsageTracker: Updating usage stats for 5 knowledge points
```

## 性能考虑

- 知识点级别过滤增加了少量计算开销
- 通过合理的参数配置可以平衡性能和效果
- 建议在题库较大时适当调整 `max-topics-per-knowledge-point` 参数

## 向后兼容性

- 新功能完全向后兼容，不影响现有功能
- 可以通过配置禁用知识点级别控制
- 原有的全局多样性控制继续有效

## 未来扩展

- 支持基于题目内容相似度的过滤
- 增加用户个性化的重复控制
- 提供更丰富的多样性分析报告
- 支持动态调整重用间隔策略
