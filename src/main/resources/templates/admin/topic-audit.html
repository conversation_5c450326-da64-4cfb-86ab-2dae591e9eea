<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题目审核管理 - 麦子教育系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js" rel="preload" as="script">
    <style>
        :root {
            --apple-blue: #007AFF;
            --apple-gray: #F2F2F7;
            --apple-gray-2: #E5E5EA;
            --apple-gray-3: #D1D1D6;
            --apple-gray-4: #C7C7CC;
            --apple-gray-5: #AEAEB2;
            --apple-gray-6: #8E8E93;
            --apple-text: #1D1D1F;
            --apple-text-secondary: #86868B;
            --apple-green: #34C759;
            --apple-orange: #FF9500;
            --apple-red: #FF3B30;
            --apple-yellow: #FFCC00;
        }

        body {
            background-color: var(--apple-gray);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: var(--apple-text);
            padding-top: 0; /* 移除默认的顶部padding，因为有统一导航栏 */
        }

        .admin-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            margin: 20px;
            margin-top: 10px; /* 减少顶部边距，因为有统一导航栏 */
            overflow: hidden;
        }

        .admin-header {
            background: linear-gradient(135deg, var(--apple-blue), #5AC8FA);
            color: white;
            padding: 20px 24px; /* 减少垂直padding */
            border-radius: 16px 16px 0 0;
            position: relative;
            overflow: hidden;
        }

        .admin-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .admin-header > * {
            position: relative;
            z-index: 1;
        }

        .admin-info {
            display: flex;
            align-items: center;
            gap: 12px;
            background: rgba(255, 255, 255, 0.15);
            padding: 8px 16px;
            border-radius: 12px;
            backdrop-filter: blur(10px);
        }

        .admin-avatar {
            font-size: 2rem;
            color: rgba(255, 255, 255, 0.9);
        }

        .admin-details {
            display: flex;
            flex-direction: column;
        }

        .admin-name {
            font-weight: 600;
            font-size: 0.9rem;
            line-height: 1.2;
        }

        .admin-role {
            font-size: 0.75rem;
            opacity: 0.8;
            line-height: 1.2;
        }

        .admin-sidebar {
            background: white;
            border-right: 1px solid var(--apple-gray-2);
            min-height: calc(100vh - 200px); /* 调整高度计算，考虑统一导航栏 */
        }

        .sidebar-nav {
            padding: 16px 0;
        }

        .sidebar-nav .nav-link {
            color: var(--apple-text);
            padding: 12px 24px;
            border-radius: 0;
            border: none;
            transition: all 0.2s ease;
            font-weight: 500;
        }

        .sidebar-nav .nav-link:hover {
            background-color: var(--apple-gray);
            color: var(--apple-blue);
        }

        .sidebar-nav .nav-link.active {
            background-color: var(--apple-blue);
            color: white;
            position: relative;
        }

        .sidebar-nav .nav-link.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: white;
        }

        .stats-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            border: 1px solid var(--apple-gray-2);
            transition: all 0.2s ease;
        }

        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            line-height: 1;
        }

        .stats-label {
            color: var(--apple-text-secondary);
            font-size: 0.9rem;
            margin-top: 4px;
        }

        .audit-status-pending { color: var(--apple-orange); }
        .audit-status-approved { color: var(--apple-green); }
        .audit-status-rejected { color: var(--apple-red); }

        .topic-content {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .audit-actions { min-width: 120px; }

        .btn-apple {
            border-radius: 8px;
            font-weight: 500;
            padding: 8px 16px;
            border: none;
            transition: all 0.2s ease;
        }

        .btn-apple-primary {
            background-color: var(--apple-blue);
            color: white;
        }

        .btn-apple-primary:hover {
            background-color: #0056CC;
            transform: translateY(-1px);
        }

        .btn-apple-success {
            background-color: var(--apple-green);
            color: white;
        }

        .btn-apple-danger {
            background-color: var(--apple-red);
            color: white;
        }

        .table-apple {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .table-apple thead th {
            background-color: var(--apple-gray);
            border: none;
            font-weight: 600;
            color: var(--apple-text);
            padding: 16px;
        }

        .table-apple tbody td {
            border: none;
            padding: 16px;
            border-bottom: 1px solid var(--apple-gray-2);
        }

        .table-apple tbody tr:hover {
            background-color: var(--apple-gray);
        }

        .search-container {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .form-control-apple {
            border: 1px solid var(--apple-gray-3);
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 0.95rem;
            transition: all 0.2s ease;
        }

        .form-control-apple:focus {
            border-color: var(--apple-blue);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }

        .modal-apple .modal-content {
            border-radius: 16px;
            border: none;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
        }

        .modal-apple .modal-header {
            border-bottom: 1px solid var(--apple-gray-2);
            padding: 20px 24px;
        }

        .modal-apple .modal-body {
            padding: 24px;
        }

        .modal-apple .modal-footer {
            border-top: 1px solid var(--apple-gray-2);
            padding: 16px 24px;
        }

        /* 响应式设计改进 */
        @media (max-width: 768px) {
            .admin-container {
                margin: 10px;
                border-radius: 12px;
            }

            .admin-header {
                padding: 16px;
                border-radius: 12px 12px 0 0;
            }

            .admin-sidebar {
                min-height: auto;
                border-right: none;
                border-bottom: 1px solid var(--apple-gray-2);
            }

            .sidebar-nav .nav-link {
                padding: 10px 16px;
                font-size: 0.9rem;
            }
        }

        /* 确保统一导航栏和页面内容的协调 */
        .navbar-apple + .admin-container {
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <!-- 统一导航栏 -->
    <div th:replace="fragments/unified-navbar :: unified-navbar"></div>

    <div class="admin-container">
        <!-- 管理员页面头部 -->
        <div class="admin-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 mb-1">题目审核管理</h1>
                    <p class="mb-0 opacity-75">管理用户提交的题目，确保题库质量</p>
                </div>
                <div class="d-flex align-items-center">
                    <div class="admin-info me-3">
                        <div class="admin-avatar">
                            <i class="bi bi-person-circle"></i>
                        </div>
                        <div class="admin-details">
                            <div class="admin-name">管理员</div>
                            <div class="admin-role">系统管理员</div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-light btn-apple me-2" onclick="refreshAuditList()">
                        <i class="bi bi-arrow-clockwise me-1"></i> 刷新
                    </button>
                    <button type="button" class="btn btn-light btn-apple me-2" onclick="exportAuditData()">
                        <i class="bi bi-download me-1"></i> 导出
                    </button>
                    <button type="button" class="btn btn-outline-light btn-apple" onclick="adminLogout()">
                        <i class="bi bi-box-arrow-right me-1"></i> 退出
                    </button>
                </div>
            </div>
        </div>

        <div class="row g-0">
            <!-- 侧边栏 -->
            <nav class="col-md-2 admin-sidebar">
                <div class="sidebar-nav">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="/admin/topics/audit">
                                <i class="bi bi-check-circle me-2"></i> 题目审核
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/users">
                                <i class="bi bi-people me-2"></i> 用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/stats">
                                <i class="bi bi-graph-up me-2"></i> 系统统计
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/permissions">
                                <i class="bi bi-key me-2"></i> 权限管理
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-10 p-4">
                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="stats-card">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-clock-history" style="font-size: 2rem; color: var(--apple-orange);"></i>
                                </div>
                                <div>
                                    <div class="stats-number audit-status-pending" id="pendingCount">-</div>
                                    <div class="stats-label">待审核</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stats-card">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-check-circle-fill" style="font-size: 2rem; color: var(--apple-green);"></i>
                                </div>
                                <div>
                                    <div class="stats-number audit-status-approved" id="approvedCount">-</div>
                                    <div class="stats-label">已通过</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stats-card">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-x-circle-fill" style="font-size: 2rem; color: var(--apple-red);"></i>
                                </div>
                                <div>
                                    <div class="stats-number audit-status-rejected" id="rejectedCount">-</div>
                                    <div class="stats-label">已拒绝</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stats-card">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-collection-fill" style="font-size: 2rem; color: var(--apple-blue);"></i>
                                </div>
                                <div>
                                    <div class="stats-number" style="color: var(--apple-blue);" id="totalCount">-</div>
                                    <div class="stats-label">总计</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据统计图表 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="stats-card">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0"><i class="bi bi-bar-chart-line-fill me-2"></i>题目审核趋势</h5>
                                <div class="d-flex align-items-center">
                                    <select class="form-select form-select-sm me-2 form-control-apple" id="statType" style="width: auto;">
                                        <option value="day">按日</option>
                                        <option value="week">按周</option>
                                        <option value="month">按月</option>
                                    </select>
                                    <input type="date" class="form-control form-control-sm form-control-apple me-1" id="startDate" style="width: auto;">
                                    <span class="me-1">-</span>
                                    <input type="date" class="form-control form-control-sm form-control-apple me-2" id="endDate" style="width: auto;">
                                    <button class="btn btn-sm btn-apple btn-apple-primary" id="searchBtn">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div id="auditStatsChart" style="height: 300px;"></div>
                        </div>
                    </div>
                </div>

                <!-- 筛选和搜索 -->
                <div class="search-container">
                    <div class="row align-items-end">
                        <div class="col-md-3">
                            <label class="form-label">审核状态</label>
                            <select class="form-select form-control-apple" id="statusFilter">
                                <option value="">全部状态</option>
                                <option value="0">待审核</option>
                                <option value="1">已通过</option>
                                <option value="2">已拒绝</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">搜索题目</label>
                            <input type="text" class="form-control form-control-apple" id="searchKeyword" placeholder="输入题目标题进行搜索...">
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-apple btn-apple-primary w-100" onclick="searchAudits()">
                                <i class="bi bi-search me-1"></i> 搜索
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 审核列表 -->
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">审核列表</h5>
                        <div class="d-flex align-items-center">
                            <span class="text-muted me-3">共 <span id="totalRecords">0</span> 条记录</span>
                            <button class="btn btn-sm btn-apple btn-apple-primary" onclick="batchApprove()">
                                <i class="bi bi-check-all me-1"></i> 批量通过
                            </button>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-apple">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" class="form-check-input" id="selectAll">
                                    </th>
                                    <th>ID</th>
                                    <th>题目标题</th>
                                    <th>题目类型</th>
                                    <th>提交用户</th>
                                    <th>提交时间</th>
                                    <th>审核状态</th>
                                    <th>审核员</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="auditTableBody">
                                <!-- 动态加载内容 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 分页 -->
                <nav aria-label="审核列表分页">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- 动态生成分页 -->
                    </ul>
                </nav>
            </main>
        </div>
    </div>

    <!-- 审核详情模态框 -->
    <div class="modal fade modal-apple" id="auditDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title fw-bold">题目审核详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="auditDetailContent">
                    <!-- 动态加载内容 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-apple btn-apple-success" onclick="approveAudit()">
                        <i class="bi bi-check-circle me-1"></i> 通过
                    </button>
                    <button type="button" class="btn btn-apple btn-apple-danger" onclick="showRejectModal()">
                        <i class="bi bi-x-circle me-1"></i> 拒绝
                    </button>
                    <button type="button" class="btn btn-secondary btn-apple" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 拒绝原因模态框 -->
    <div class="modal fade modal-apple" id="rejectModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title fw-bold">拒绝审核</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="rejectReason" class="form-label fw-semibold">拒绝原因</label>
                        <textarea class="form-control form-control-apple" id="rejectReason" rows="4" placeholder="请详细说明拒绝原因，帮助用户改进题目质量..."></textarea>
                    </div>
                    <div class="alert alert-warning d-flex align-items-center">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        <small>拒绝后，题目将被移至拒绝列表，用户会收到通知消息。</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-apple btn-apple-danger" onclick="rejectAudit()">
                        <i class="bi bi-x-circle me-1"></i> 确认拒绝
                    </button>
                    <button type="button" class="btn btn-secondary btn-apple" data-bs-dismiss="modal">取消</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量操作确认模态框 -->
    <div class="modal fade modal-apple" id="batchConfirmModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title fw-bold">批量操作确认</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="d-flex align-items-center mb-3">
                        <i class="bi bi-question-circle-fill text-warning me-2" style="font-size: 1.5rem;"></i>
                        <div>
                            <p class="mb-1 fw-semibold">确认批量通过审核？</p>
                            <small class="text-muted">您选择了 <span id="selectedCount">0</span> 个题目，确认全部通过审核吗？</small>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-apple btn-apple-success" onclick="confirmBatchApprove()">
                        <i class="bi bi-check-all me-1"></i> 确认通过
                    </button>
                    <button type="button" class="btn btn-secondary btn-apple" data-bs-dismiss="modal">取消</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script src="/js/common.js"></script>
    <script>
        let currentPage = 1;
        let currentAuditId = null;
        let auditChart = null;
        let selectedAudits = new Set();

        // 获取认证token（如果统一导航栏没有加载，则使用本地版本）
        function getAuthToken() {
            return localStorage.getItem('authToken') || getCookie('Authorization');
        }

        // 获取Cookie（如果统一导航栏没有加载，则使用本地版本）
        function getCookie(name) {
            const nameEQ = name + "=";
            const ca = document.cookie.split(';');
            for (let i = 0; i < ca.length; i++) {
                let c = ca[i];
                while (c.charAt(0) === ' ') c = c.substring(1, c.length);
                if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
            }
            return null;
        }

        // 检查认证状态
        function checkAuth() {
            const token = getAuthToken();
            if (!token) {
                alert('未登录或登录已过期，请重新登录');
                window.location.href = '/admin/login';
                return false;
            }
            return true;
        }

        // 显示成功消息
        function showSuccess(message) {
            // 创建成功提示元素
            const alert = document.createElement('div');
            alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
            alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alert.innerHTML = `
                <i class="bi bi-check-circle me-2"></i>${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alert);

            // 3秒后自动移除
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 3000);
        }

        // 显示错误消息
        function showError(message) {
            // 创建错误提示元素
            const alert = document.createElement('div');
            alert.className = 'alert alert-danger alert-dismissible fade show position-fixed';
            alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alert.innerHTML = `
                <i class="bi bi-exclamation-triangle me-2"></i>${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alert);

            // 5秒后自动移除
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 5000);
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 首先检查认证状态
            if (!checkAuth()) {
                return;
            }

            loadAuditList();
            loadAuditStatistics();
            loadAuditTrendChart();
            initEventListeners();
        });

        function initEventListeners() {
            // 全选/取消全选
            document.getElementById('selectAll').addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('#auditTableBody input[type="checkbox"]');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                    if (this.checked) {
                        selectedAudits.add(checkbox.value);
                    } else {
                        selectedAudits.delete(checkbox.value);
                    }
                });
                updateBatchButtons();
            });

            // 日期范围变化时重新加载图表
            document.getElementById('startDate').addEventListener('change', loadAuditTrendChart);
            document.getElementById('endDate').addEventListener('change', loadAuditTrendChart);
            document.getElementById('statType').addEventListener('change', loadAuditTrendChart);
            document.getElementById('searchBtn').addEventListener('click', loadAuditTrendChart);
        }

        // 加载审核统计数据
        function loadAuditStatistics() {
            fetch('/api/admin/stats/audit', {
                headers: {
                    'Authorization': `Bearer ${getAuthToken()}`
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    updateStatisticsCards(data.data);
                } else {
                    console.error('获取统计数据失败:', data.message);
                }
            })
            .catch(error => {
                console.error('获取统计数据失败:', error);
            });
        }

        // 更新统计卡片
        function updateStatisticsCards(stats) {
            document.getElementById('pendingCount').textContent = stats.pendingCount || 0;
            // 这里可以添加更多统计数据的更新
        }

        // 加载审核趋势图表
        function loadAuditTrendChart() {
            const type = document.getElementById('statType').value;
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            const params = new URLSearchParams({ type });
            if (startDate) params.append('startDate', startDate);
            if (endDate) params.append('endDate', endDate);

            fetch(`/api/admin/stats/audit/trend?${params}`, {
                headers: {
                    'Authorization': `Bearer ${getAuthToken()}`
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    renderAuditChart(data.data);
                } else {
                    console.error('获取趋势数据失败:', data.message);
                }
            })
            .catch(error => {
                console.error('获取趋势数据失败:', error);
            });
        }

        // 渲染审核趋势图表
        function renderAuditChart(chartData) {
            const ctx = document.getElementById('auditStatsChart');

            if (auditChart) {
                auditChart.destroy();
            }

            auditChart = new Chart(ctx, {
                type: 'line',
                data: chartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        }
                    },
                    elements: {
                        line: {
                            tension: 0.4
                        },
                        point: {
                            radius: 4,
                            hoverRadius: 6
                        }
                    }
                }
            });
        }

        // 加载审核列表
        function loadAuditList(page = 1) {
            const status = document.getElementById('statusFilter').value;
            const keyword = document.getElementById('searchKeyword').value;

            const params = new URLSearchParams({
                pageNum: page,
                pageSize: 20
            });

            if (status) params.append('auditStatus', status);
            if (keyword) params.append('keyword', keyword);

            fetch(`/api/admin/topics/audit?${params}`, {
                headers: {
                    'Authorization': `Bearer ${getAuthToken()}`
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    renderAuditList(data.data);
                    currentPage = page;
                } else {
                    showError(data.message);
                }
            })
            .catch(error => {
                console.error('加载审核列表失败:', error);
                showError('加载审核列表失败');
            });
        }

        // 渲染审核列表
        function renderAuditList(pageData) {
            const tbody = document.getElementById('auditTableBody');
            tbody.innerHTML = '';

            if (pageData.records && pageData.records.length > 0) {
                pageData.records.forEach(audit => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>
                            <input type="checkbox" class="form-check-input audit-checkbox" value="${audit.id}"
                                   ${audit.auditStatus === 0 ? '' : 'disabled'}>
                        </td>
                        <td>${audit.id}</td>
                        <td class="topic-content" title="${audit.title}">${audit.title}</td>
                        <td>${getTopicTypeText(audit.type)}</td>
                        <td>${audit.submitterUsername || '未知'}</td>
                        <td>${formatDateTime(audit.submitTime)}</td>
                        <td><span class="audit-status-${getStatusClass(audit.auditStatus)}">${getStatusText(audit.auditStatus)}</span></td>
                        <td>${audit.auditorUsername || '-'}</td>
                        <td class="audit-actions">
                            <button class="btn btn-sm btn-apple btn-apple-primary" onclick="viewAuditDetail(${audit.id})">
                                <i class="bi bi-eye me-1"></i> 查看
                            </button>
                            ${audit.auditStatus === 0 ? `
                                <button class="btn btn-sm btn-apple btn-apple-success ms-1" onclick="quickApprove(${audit.id})">
                                    <i class="bi bi-check"></i>
                                </button>
                                <button class="btn btn-sm btn-apple btn-apple-danger ms-1" onclick="quickReject(${audit.id})">
                                    <i class="bi bi-x"></i>
                                </button>
                            ` : ''}
                        </td>
                    `;
                    tbody.appendChild(row);
                });

                // 添加复选框事件监听
                document.querySelectorAll('.audit-checkbox').forEach(checkbox => {
                    checkbox.addEventListener('change', function() {
                        if (this.checked) {
                            selectedAudits.add(this.value);
                        } else {
                            selectedAudits.delete(this.value);
                        }
                        updateBatchButtons();
                    });
                });
            } else {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="9" class="text-center text-muted py-4">
                            <i class="bi bi-inbox" style="font-size: 2rem;"></i>
                            <div class="mt-2">暂无审核记录</div>
                        </td>
                    </tr>
                `;
            }

            // 更新总记录数
            document.getElementById('totalRecords').textContent = pageData.total || 0;

            renderPagination(pageData);
        }

        // 更新批量操作按钮状态
        function updateBatchButtons() {
            const hasSelected = selectedAudits.size > 0;
            // 这里可以添加批量操作按钮的启用/禁用逻辑
        }

        // 快速审核通过
        function quickApprove(auditId) {
            if (confirm('确认通过此题目的审核吗？')) {
                approveAuditById(auditId, '快速审核通过');
            }
        }

        // 快速审核拒绝
        function quickReject(auditId) {
            const reason = prompt('请输入拒绝原因：');
            if (reason && reason.trim()) {
                rejectAuditById(auditId, reason.trim());
            }
        }

        // 批量审核通过
        function batchApprove() {
            if (selectedAudits.size === 0) {
                showError('请先选择要审核的题目');
                return;
            }

            document.getElementById('selectedCount').textContent = selectedAudits.size;
            const modal = new bootstrap.Modal(document.getElementById('batchConfirmModal'));
            modal.show();
        }

        // 确认批量审核通过
        function confirmBatchApprove() {
            const promises = Array.from(selectedAudits).map(auditId =>
                approveAuditById(auditId, '批量审核通过')
            );

            Promise.all(promises)
                .then(() => {
                    showSuccess(`成功批量通过 ${selectedAudits.size} 个题目的审核`);
                    selectedAudits.clear();
                    loadAuditList(currentPage);
                    loadAuditStatistics();
                    bootstrap.Modal.getInstance(document.getElementById('batchConfirmModal')).hide();
                })
                .catch(error => {
                    console.error('批量审核失败:', error);
                    showError('批量审核失败，请重试');
                });
        }

        // 审核通过（通用方法）
        function approveAuditById(auditId, comment = '') {
            return fetch(`/api/admin/topics/audit/${auditId}/approve`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${getAuthToken()}`
                },
                body: JSON.stringify({ comment })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code !== 200) {
                    throw new Error(data.message);
                }
                return data;
            });
        }

        // 审核拒绝（通用方法）
        function rejectAuditById(auditId, rejectReason) {
            return fetch(`/api/admin/topics/audit/${auditId}/reject`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${getAuthToken()}`
                },
                body: JSON.stringify({ rejectReason })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showSuccess('审核拒绝成功');
                    loadAuditList(currentPage);
                    loadAuditStatistics();
                } else {
                    throw new Error(data.message);
                }
                return data;
            });
        }

        // 其他辅助函数...
        function getStatusClass(status) {
            switch(status) {
                case 0: return 'pending';
                case 1: return 'approved';
                case 2: return 'rejected';
                default: return 'pending';
            }
        }

        function getStatusText(status) {
            switch(status) {
                case 0: return '待审核';
                case 1: return '已通过';
                case 2: return '已拒绝';
                default: return '未知';
            }
        }

        function getTopicTypeText(type) {
            const typeMap = {
                'choice': '单选题',
                'multiple': '多选题',
                'judge': '判断题',
                'fill': '填空题',
                'short': '简答题'
            };
            return typeMap[type] || type;
        }

        function formatDateTime(dateTime) {
            return new Date(dateTime).toLocaleString('zh-CN');
        }

        // 管理员退出登录
        function adminLogout() {
            if (confirm('确定要退出管理后台吗？')) {
                // 清除本地存储的token
                localStorage.removeItem('authToken');

                // 清除Cookie中的token
                document.cookie = 'Authorization=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';

                // 显示退出成功消息
                showSuccess('已安全退出管理后台');

                // 延迟跳转到登录页面
                setTimeout(() => {
                    window.location.href = '/admin/login';
                }, 1000);
            }
        }

        // 更多功能函数将在后续添加...
    </script>
</body>
</html>
