-- 创建题目审核表
-- 用于存储用户提交的题目审核记录

-- 先删除可能存在的外键约束
SET FOREIGN_KEY_CHECKS = 0;

-- 删除相关表
DROP TABLE IF EXISTS `topic_rejected`;
DROP TABLE IF EXISTS `system_messages`;
DROP TABLE IF EXISTS `topic_audit`;
CREATE TABLE `topic_audit`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '审核记录ID',
  `user_id` bigint(0) UNSIGNED NOT NULL COMMENT '提交用户ID',
  `know_id` int(0) UNSIGNED NOT NULL COMMENT '知识点ID',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '题目类型',
  `title` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '题目标题',
  `options` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '选项数据(json)',
  `subs` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '组合题数据(json)',
  `answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '答案',
  `parse` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '答案解析',
  `score` int(0) UNSIGNED NOT NULL DEFAULT 3 COMMENT '分值',
  `source` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '来源',
  `difficulty` double NULL DEFAULT NULL COMMENT '题目难度',
  `tags` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '细分知识点标签',
  `audit_status` tinyint(0) NOT NULL DEFAULT 0 COMMENT '审核状态：0-待审核，1-审核通过，2-审核拒绝',
  `auditor_id` bigint(0) UNSIGNED NULL DEFAULT NULL COMMENT '审核员ID',
  `audit_time` datetime(0) NULL DEFAULT NULL COMMENT '审核时间',
  `audit_comment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '审核意见',
  `auto_approved` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否自动审核通过：0-否，1-是',
  `submit_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '提交时间',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_audit_status`(`audit_status`) USING BTREE,
  INDEX `idx_submit_time`(`submit_time`) USING BTREE,
  INDEX `idx_auditor_id`(`auditor_id`) USING BTREE,
  INDEX `idx_know_id`(`know_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '题目审核表' ROW_FORMAT = Dynamic;

-- 创建相关的被拒绝题目表
DROP TABLE IF EXISTS `topic_rejected`;
CREATE TABLE `topic_rejected`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '拒绝记录ID',
  `audit_id` bigint(0) NOT NULL COMMENT '审核记录ID',
  `user_id` bigint(0) UNSIGNED NOT NULL COMMENT '提交用户ID',
  `auditor_id` bigint(0) UNSIGNED NOT NULL COMMENT '审核员ID',
  `reject_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '拒绝原因',
  `reject_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '拒绝时间',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_audit_id`(`audit_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_auditor_id`(`auditor_id`) USING BTREE,
  INDEX `idx_reject_time`(`reject_time`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '被拒绝题目表' ROW_FORMAT = Dynamic;

-- 创建系统消息表
DROP TABLE IF EXISTS `system_messages`;
CREATE TABLE `system_messages`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '消息ID',
  `user_id` bigint(0) UNSIGNED NOT NULL COMMENT '接收用户ID',
  `sender_id` bigint(0) UNSIGNED NULL DEFAULT NULL COMMENT '发送者ID（系统消息为NULL）',
  `message_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '消息类型：AUDIT_APPROVED-审核通过，AUDIT_REJECTED-审核拒绝，SYSTEM_NOTICE-系统通知',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '消息标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '消息内容',
  `related_id` bigint(0) NULL DEFAULT NULL COMMENT '关联ID（如审核记录ID）',
  `related_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联类型：TOPIC_AUDIT-题目审核',
  `is_read` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已读：0-未读，1-已读',
  `read_time` datetime(0) NULL DEFAULT NULL COMMENT '阅读时间',
  `priority` tinyint(0) NOT NULL DEFAULT 1 COMMENT '优先级：1-普通，2-重要，3-紧急',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_message_type`(`message_type`) USING BTREE,
  INDEX `idx_is_read`(`is_read`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  INDEX `idx_sender_id`(`sender_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '系统消息表' ROW_FORMAT = Dynamic;

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;
