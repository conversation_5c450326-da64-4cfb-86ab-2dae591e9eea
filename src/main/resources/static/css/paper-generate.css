/* 知识点容器样式 */
.knowledge-points-container {
    max-height: 60vh;
    overflow: hidden;
}

.knowledge-categories {
    max-height: 60vh;
    overflow-y: auto;
}

#knowledge-points-container {
    max-height: 60vh;
    overflow-y: auto;
}

/* 已选择知识点样式 */
.selected-knowledge-point {
    position: relative;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.selected-knowledge-point .card {
    border-left: 4px solid #28a745;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.selected-knowledge-point .card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.selected-knowledge-point .card-body {
    padding: 1rem;
}

.selected-knowledge-point .card-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #495057;
}

.selected-knowledge-point .remove-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #dc3545;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    opacity: 0.7;
}

.selected-knowledge-point .remove-btn:hover {
    background-color: #dc3545;
    color: white;
    opacity: 1;
}

.selected-knowledge-point .badge {
    font-size: 0.75rem;
    padding: 0.25em 0.6em;
    margin-right: 0.5rem;
}

/* 知识点配置样式 */
.knowledge-point-config {
    border: 1px solid #e9ecef;
    border-radius: 0.25rem;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

.knowledge-point-config:hover {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-color: #dee2e6;
}

.knowledge-point-config .card-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.knowledge-point-config .form-group {
    margin-bottom: 0.5rem;
}

.knowledge-point-config .input-group-sm .form-control {
    height: calc(1.5em + 0.5rem + 2px);
}

.knowledge-point-config .custom-switch {
    padding-left: 2.25rem;
}

.knowledge-point-config .badge {
    font-size: 0.75rem;
    padding: 0.25em 0.6em;
    margin-right: 0.5rem;
}

/* 智能分配按钮样式 */
#smartDistributeBtn {
    transition: all 0.3s ease;
}

#smartDistributeBtn:hover {
    background-color: #e9ecef;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* 题量不匹配警告样式 */
.mismatch-warning {
    border-color: #ffc107;
    background-color: #fff8e1;
}

/* 题量匹配成功样式 */
.match-success {
    border-color: #28a745;
    background-color: #f0fff4;
}

/* 动画效果 */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

.btn-pulse {
    animation: pulse 1.5s infinite;
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateY(0) scale(1);
        max-height: 200px;
        margin-bottom: 15px;
    }
    to {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
        max-height: 0;
        margin-bottom: 0;
        padding-top: 0;
        padding-bottom: 0;
    }
}

.fade-out {
    animation: fadeOut 0.3s ease-out forwards;
    overflow: hidden;
}

/* 删除确认动画 */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.selected-knowledge-point.removing {
    animation: shake 0.5s ease-in-out;
}

/* 知识点卡片悬停效果 */
.knowledge-point-card {
    transition: all 0.3s ease;
    margin-bottom: 1rem;
    border-left: 3px solid transparent;
}

.knowledge-point-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-left-color: #007bff;
}

.knowledge-point-card .card-body {
    padding: 1rem;
}

.knowledge-point-card .card-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* 已选择知识点卡片样式增强 */
.selected-knowledge-point .card {
    transition: all 0.3s ease;
}

.selected-knowledge-point .card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.selected-knowledge-point .generate-single-btn {
    opacity: 0.8;
    transition: all 0.2s ease;
}

.selected-knowledge-point .generate-single-btn:hover {
    opacity: 1;
    transform: scale(1.05);
}

/* 排序和过滤控件样式 */
.sort-knowledge-points {
    transition: all 0.2s ease;
}

.sort-knowledge-points.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

#knowledgePointFilter {
    transition: all 0.3s ease;
}

#knowledgePointFilter:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    border-color: #80bdff;
}

/* 统计卡片样式 */
.stat-card {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.stat-card-body {
    display: flex;
    align-items: center;
    padding: 1.25rem;
    background-color: #fff;
    border: 1px solid rgba(0,0,0,0.05);
    border-radius: 8px;
}

.stat-card-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    margin-right: 1rem;
    color: white;
    font-size: 1.5rem;
}

.stat-card-content {
    flex: 1;
}

.stat-card-value {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    color: #333;
}

.stat-card-label {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0;
}

/* 已选择知识点卡片样式 */
#selected-knowledge-points-card {
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    border-radius: 8px;
    overflow: hidden;
    border: none;
}

#selected-knowledge-points-card .card-header {
    border-bottom: none;
    padding: 1rem 1.25rem;
}

#selected-knowledge-points-card .card-body {
    padding: 1.25rem;
}

#selected-knowledge-points-card .card-footer {
    background-color: rgba(0,0,0,0.02);
    border-top: 1px solid rgba(0,0,0,0.05);
    padding: 0.75rem 1.25rem;
}

.selected-knowledge-point {
    transition: all 0.3s ease;
}

.selected-knowledge-point .card {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    border: 1px solid rgba(0,0,0,0.05);
}

.selected-knowledge-point .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.selected-knowledge-point .card-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.selected-knowledge-point .badge {
    font-weight: 500;
    padding: 0.35em 0.65em;
    margin-right: 0.25rem;
}

.selected-knowledge-point .remove-btn {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    opacity: 0.7;
}

.selected-knowledge-point .remove-btn:hover {
    background-color: rgba(220, 53, 69, 0.1);
    opacity: 1;
    transform: scale(1.1);
}

.selected-knowledge-point .remove-btn:active {
    transform: scale(0.95);
}

/* 知识点统计卡片样式 */
#knowledge-stats-card {
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    border-radius: 8px;
    overflow: hidden;
    border: none;
}

#knowledge-stats-card .card-header {
    border-bottom: none;
    padding: 1rem 1.25rem;
}

#knowledge-stats-card .card-body {
    padding: 1.25rem;
}

/* 工具栏样式 */
.selected-points-toolbar {
    padding: 0.75rem;
    background-color: rgba(0,0,0,0.02);
    border-radius: 6px;
    margin-bottom: 1rem;
}

/* 按钮样式增强 */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.btn-outline-danger {
    color: #dc3545;
    border-color: #dc3545;
}

.btn-outline-danger:hover {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.3s ease forwards;
}

.fade-out {
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
}

/* 试卷预览样式 */
.paper-preview {
    padding: 1rem;
    background-color: #fff;
    border-radius: 0.5rem;
}

.paper-preview h3 {
    text-align: center;
    margin-bottom: 1rem;
    font-weight: 700;
}

.paper-meta {
    text-align: center;
    color: #6c757d;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.question-section {
    margin-bottom: 2rem;
}

.section-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.question-item {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px dashed #e9ecef;
}

.question-header {
    margin-bottom: 0.75rem;
}

.question-index {
    font-weight: 600;
    margin-right: 0.5rem;
}

.question-title {
    font-weight: 500;
}

.question-options {
    padding-left: 1.5rem;
}

.option-item {
    margin-bottom: 0.5rem;
}

.answer-area {
    margin-top: 1rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 0.25rem;
    min-height: 100px;
    position: relative;
}

.answer-lines {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: linear-gradient(#e9ecef 1px, transparent 1px);
    background-size: 100% 2rem;
    background-position: 0 1rem;
    pointer-events: none;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .selected-knowledge-point {
        margin-bottom: 1rem;
    }

    .knowledge-point-config .row {
        flex-direction: column;
    }

    .knowledge-point-config .col-md-6:first-child {
        margin-bottom: 1rem;
    }

    #quickConfigOptions .row {
        margin-bottom: 1rem;
    }

    #selectedKnowledgeStatsCard .row {
        margin-bottom: 0;
    }

    #selectedKnowledgeStatsCard .col-md-6 {
        margin-bottom: 1rem;
    }

    .paper-preview {
        padding: 0.5rem;
    }

    .section-title {
        font-size: 1.1rem;
    }
}

/* 知识点分类样式 */
.group-item {
    cursor: pointer;
    padding: 0.75rem 1rem;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
    transition: all 0.2s ease;
}

.group-item:hover {
    background-color: #f8f9fa;
}

.group-item.active {
    background-color: #007bff;
    color: white;
}

.group-item .badge {
    float: right;
}

/* 知识点列表样式 */
.knowledge-point-card {
    margin-bottom: 0.75rem;
    transition: transform 0.2s ease;
}

.knowledge-point-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.knowledge-point-card .card-body {
    padding: 0.75rem;
}

.knowledge-point-card .card-title {
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.knowledge-point-card .badge {
    font-size: 0.75rem;
}

/* 试卷配置样式 */
#paperConfigTabs .nav-link {
    padding: 0.5rem 1rem;
}

#paperConfigTabContent {
    padding: 1rem 0;
}

/* 试卷预览样式 */
.paper-preview {
    background-color: #fff;
    padding: 2rem;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.paper-preview h3 {
    text-align: center;
    margin-bottom: 1rem;
}

.paper-meta {
    text-align: center;
    color: #6c757d;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

/* 历史试卷表格样式 */
#paperHistoryTable td {
    vertical-align: middle;
}

.empty-state {
    text-align: center;
    padding: 2rem 0;
}

/* 按钮和操作区域样式 */
.action-buttons .btn {
    margin-right: 0.25rem;
}

.action-buttons .btn:last-child {
    margin-right: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .knowledge-points-container {
        max-height: none;
    }

    .knowledge-categories,
    #knowledge-points-container {
        max-height: 40vh;
    }

    .paper-preview {
        padding: 1rem;
    }
}

/* KaTeX 公式样式调整 */
.katex-display {
    margin: 1em 0 !important;
    overflow-x: auto;
    overflow-y: hidden;
    padding-top: 0.5em;
    padding-bottom: 0.5em;
}

.katex {
    font-size: 1.1em;
}

/* 题目预览样式 */
.question-section {
    margin-bottom: 2rem;
}

.question-section h4 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #dee2e6;
}

.question-item {
    margin-bottom: 1.5rem;
}

.question-item .question-content {
    margin-bottom: 0.75rem;
}

.question-item .options {
    padding-left: 1.5rem;
}

.question-item .option {
    margin-bottom: 0.5rem;
}

/* 加载状态样式 */
.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

/* 知识点表格样式 */
#knowledgeTable {
    font-size: 0.9rem;
}

#knowledgeTable th {
    background-color: #f8f9fa;
}

#knowledgeTable .form-check {
    margin: 0;
}

/* 搜索和筛选区域 */
.filter-section {
    margin-bottom: 1rem;
    padding: 0.75rem;
    background-color: #f8f9fa;
    border-radius: 0.25rem;
}

.search-box {
    position: relative;
}

.search-box .form-control {
    padding-right: 2.5rem;
}

.search-box .search-icon {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

/* 分页样式 */
.pagination {
    justify-content: center;
    margin-bottom: 0;
}

/* 模态框样式调整 */
.modal-xl {
    max-width: 1140px;
}

@media (max-width: 1200px) {
    .modal-xl {
        max-width: 90%;
    }
}

/* 图表容器样式 */
.chart-container {
    position: relative;
    height: 200px;
}

/* 自定义开关样式 */
.custom-switch .custom-control-label::before {
    width: 2.5rem;
    height: 1.25rem;
    border-radius: 0.625rem;
}

.custom-switch .custom-control-label::after {
    top: calc(0.25rem + 2px);
    left: calc(-2.5rem + 2px);
    width: calc(1.25rem - 4px);
    height: calc(1.25rem - 4px);
    border-radius: 0.625rem;
}

.custom-switch .custom-control-input:checked ~ .custom-control-label::after {
    transform: translateX(1.25rem);
}

/* 知识点项样式 */
.knowledge-point-item {
    transition: all 0.3s ease;
}

.knowledge-point-item:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* 提示和警告样式 */
.alert-info {
    background-color: #e3f2fd;
    border-color: #b3e5fc;
    color: #0277bd;
}

.alert-warning {
    background-color: #fff8e1;
    border-color: #ffecb3;
    color: #ff8f00;
}

/* 表单验证样式 */
.was-validated .form-control:invalid,
.form-control.is-invalid {
    border-color: #dc3545;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
