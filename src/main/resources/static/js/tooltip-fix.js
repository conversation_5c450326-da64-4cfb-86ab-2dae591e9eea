/**
 * Advanced tooltip fix script 
 * 
 * This script fixes:
 * 1. The "Cannot read properties of undefined (reading 'clientX')" error by adding global mouse tracking
 * 2. The issue with multiple tooltips appearing for book items
 * 3. The tooltip responsiveness problems
 */

// Execute immediately
(function() {
    console.log('Advanced tooltip fix loaded');

    // Global variables to track mouse position as a fallback
    window.mousePosition = { x: 0, y: 0 };
    
    // Track mouse position globally
    document.addEventListener('mousemove', function(e) {
        window.mousePosition.x = e.clientX;
        window.mousePosition.y = e.clientY;
    }, { passive: true });
    
    // Function to get current mouse position safely
    window.getMousePosition = function() {
        return { x: window.mousePosition.x, y: window.mousePosition.y };
    };
    
    // Fixed showBookTooltip function
    const fixedShowTooltip = function(element, book, event) {
        // Skip if no element or book
        if (!element || !book) return;
        
        // Remove any existing tooltips to prevent duplicates
        document.querySelectorAll('.tooltip-container, .tooltip-box, .book-tooltip').forEach(tooltip => {
            tooltip.remove();
        });
        
        // Create tooltip container
        const container = document.createElement('div');
        container.className = 'tooltip-container';
        
        // Create tooltip content
        const tooltip = document.createElement('div');
        tooltip.className = 'book-tooltip';
        
        // Extract chapter info if available
        let chapterInfo = '';
        if (book.title) {
            // Chinese chapter format: 第X章
            const cnMatch = book.title.match(/第(\d+)章/);
            if (cnMatch) {
                chapterInfo = cnMatch[0];
            } else {
                // English chapter format: Chapter X
                const enMatch = book.title.match(/Chapter\s+(\d+)/i);
                if (enMatch) {
                    chapterInfo = enMatch[0];
                } else {
                    // Section/Part format
                    const sectionMatch = book.title.match(/Section\s+(\d+)/i);
                    if (sectionMatch) {
                        chapterInfo = sectionMatch[0];
                    } else {
                        const partMatch = book.title.match(/Part\s+(\d+)/i);
                        if (partMatch) {
                            chapterInfo = partMatch[0];
                        }
                    }
                }
            }
        }
        
        // Set tooltip content
        tooltip.innerHTML = `
            <div class="tooltip-title">${book.title || 'Unknown Book'}</div>
            ${book.type ? `<div class="tooltip-type">类型: ${book.type}</div>` : ''}
            ${chapterInfo ? `<div class="tooltip-chapter">章节: ${chapterInfo}</div>` : ''}
            ${book.url ? `<div class="tooltip-url">链接: ${book.url}</div>` : ''}
            ${book.description
                ? `<div class="tooltip-description">${book.description}</div>`
                : '<div class="tooltip-no-desc">暂无简介</div>'}
        `;
        
        // Add tooltip to container
        container.appendChild(tooltip);
        
        // Add to document
        document.body.appendChild(container);
        
        // Position tooltip - safely get mouse position
        const rect = element.getBoundingClientRect();
        
        // Get mouse position from event or global tracker
        let mouseX, mouseY;
        if (event && typeof event.clientX === 'number') {
            // Use provided event if available
            mouseX = event.clientX;
            mouseY = event.clientY;
        } else {
            // Use global mouse position as fallback
            mouseX = window.mousePosition.x;
            mouseY = window.mousePosition.y;
            
            // If no mouse position available, use element position
            if (!mouseX && !mouseY) {
                mouseX = rect.right;
                mouseY = rect.top;
            }
        }
        
        // Calculate position
        const tooltipWidth = 350; // Fixed width from CSS
        let left = mouseX + 15;
        let top = mouseY;
        
        // Ensure tooltip doesn't go off-screen to the right
        if (left + tooltipWidth > window.innerWidth) {
            left = mouseX - tooltipWidth - 15;
            
            // If tooltip would go off-screen to the left, position below element
            if (left < 0) {
                left = rect.left;
                top = rect.bottom + 10;
            }
        }
        
        // Set position
        container.style.position = 'fixed';
        container.style.top = top + 'px';
        container.style.left = left + 'px';
        container.style.zIndex = '10000'; // Ensure tooltip appears above modals
        container.style.display = 'block';
        container.style.opacity = '0';
        
        // Fade in
        setTimeout(() => {
            container.style.transition = 'opacity 0.2s ease-in-out';
            container.style.opacity = '1';
        }, 10);
        
        // Add mouse leave event to container
        container.addEventListener('mouseleave', function() {
            // Fade out and remove
            container.style.opacity = '0';
            setTimeout(() => container.remove(), 200);
        });
        
        // Set a timeout to auto-remove tooltip (prevents orphaned tooltips)
        setTimeout(() => {
            if (document.body.contains(container)) {
                container.remove();
            }
        }, 8000);
    };
    
    // Replace the original function if it exists
    if (typeof window.showFixedTooltip === 'function') {
        window.originalShowFixedTooltip = window.showFixedTooltip;
        window.showFixedTooltip = fixedShowTooltip;
        console.log('Replaced original showFixedTooltip with fixed version');
    } else {
        window.showFixedTooltip = fixedShowTooltip;
        console.log('Added fixed showFixedTooltip function');
    }
    
    // Also replace the book tooltip function in book-search.js if it exists
    if (typeof window.showBookTooltip === 'function') {
        window.originalShowBookTooltip = window.showBookTooltip;
        window.showBookTooltip = function(element, event) {
            try {
                // Extract book info from the element
                const row = $(element).closest('tr');
                const bookId = row.attr('id');
                if (!bookId || !bookId.startsWith('book-')) return;
                
                const index = parseInt(bookId.replace('book-', ''), 10);
                if (isNaN(index) || !window.currentSearchBooks || !window.currentSearchBooks[index]) return;
                
                const book = window.currentSearchBooks[index];
                
                // Call the fixed tooltip function
                fixedShowTooltip(element, book, event);
            } catch (e) {
                console.error('Error in showBookTooltip:', e);
            }
        };
        console.log('Replaced original showBookTooltip with fixed version');
    }
    
    // Fix the event handler in book-search.js onMouseLeave
    document.addEventListener('click', function(e) {
        // Check if we need to remove tooltips
        const tooltips = document.querySelectorAll('.tooltip-container, .tooltip-box, .book-tooltip');
        if (tooltips.length === 0) return;
        
        // Check if click is inside a tooltip
        let clickedTooltip = false;
        tooltips.forEach(tooltip => {
            if (tooltip.contains(e.target)) {
                clickedTooltip = true;
            }
        });
        
        // If click was outside all tooltips, remove them
        if (!clickedTooltip) {
            tooltips.forEach(tooltip => {
                tooltip.remove();
            });
        }
    });
    
    // Add event to automatically clean up tooltips when modals are closed
    $(document).on('hidden.bs.modal', function() {
        document.querySelectorAll('.tooltip-container, .tooltip-box, .book-tooltip').forEach(tooltip => {
            tooltip.remove();
        });
    });
    
    console.log('Advanced tooltip fixes installed');
})(); 