// 通用功能
function escapeHtml(unsafe) {
    if (typeof unsafe !== 'string') {
        console.warn('escapeHtml called with non-string value:', unsafe);
        return String(unsafe); // Coerce to string if not already
    }
    return unsafe
         .replace(/&/g, "&amp;")
         .replace(/</g, "&lt;")
         .replace(/>/g, "&gt;")
         .replace(/"/g, "&quot;")
         .replace(/'/g, "&#039;");
}

const API_BASE_URL = '/api';

// Token处理函数 - 确保格式一致
function normalizeToken(token) {
    if (!token) return null;
    // 移除可能存在的Bearer前缀
    if (token.toLowerCase().startsWith('bearer ')) {
        return token.substring(7);
    }
    return token;
}

// 获取标准化的认证头
function getAuthHeader() {
    const token = localStorage.getItem('token');
    if (!token || token === 'undefined' || token === 'null') return null;
    // 返回标准化的token，不添加Bearer前缀
    return normalizeToken(token);
}

// 增强的Token有效性预检
async function validateCurrentToken() {
    const token = localStorage.getItem('token');
    if (!token) return false;

    // 尝试次数追踪
    let attempts = 0;
    const maxAttempts = 2;

    while (attempts < maxAttempts) {
        attempts++;

        try {
            console.log(`正在验证当前token有效性...(尝试 ${attempts}/${maxAttempts})`);

            // 使用标准化的token
            const normalizedToken = normalizeToken(token);
            if (!normalizedToken) {
                console.error("Token标准化失败，可能格式有误");
                return false;
            }

            // 调用专门的token验证接口
            const response = await fetch(`${API_BASE_URL}/user/validate`, {
                headers: {
                    'Authorization': normalizedToken
                },
                // 设置较短的超时时间
                signal: AbortSignal.timeout(5000)
            });

            if (!response.ok) {
                console.warn(`Token验证失败，HTTP状态码: ${response.status}`);

                if (response.status === 401) {
                    // 清除无效token
                    localStorage.removeItem('token');
                    return false;
                }

                // 如果是其他错误且未达到最大尝试次数，则重试
                if (attempts < maxAttempts) {
                    console.log(`将在1秒后重试验证 (${attempts}/${maxAttempts})...`);
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    continue;
                }

                return false;
            }

            const data = await response.json();
            console.log("Token验证响应:", data);

            if (data.code === 200) {
                console.log("Token验证成功");
                return true;
            } else {
                console.warn("Token验证返回非成功状态:", data);
                return false;
            }
        } catch (error) {
            console.error(`验证token时出错 (尝试 ${attempts}/${maxAttempts}):`, error);

            // 如果是网络错误且未达到最大尝试次数，则重试
            if (error.name === 'TypeError' && error.message.includes('fetch') && attempts < maxAttempts) {
                console.log(`网络错误，将在1秒后重试 (${attempts}/${maxAttempts})...`);
                await new Promise(resolve => setTimeout(resolve, 1000));
                continue;
            }

            return false;
        }
    }

    // 如果所有尝试都失败
    return false;
}

// 检查登录状态
async function checkAuth() {
    try {
        // 先验证本地是否有token
    const token = localStorage.getItem('token');
    if (!token) {
            console.warn("本地无token，需要登录");
        // 不在登录页时才跳转
        if (!window.location.pathname.includes('/auth/login')) {
            window.location.href = '/auth/login';
        }
        return null;
    }

        // 使用标准化的token
        const normalizedToken = normalizeToken(token);
        if (!normalizedToken) {
            console.error("Token标准化失败，可能格式有误");
            localStorage.removeItem('token');
            if (!window.location.pathname.includes('/auth/login')) {
                window.location.href = '/auth/login';
            }
            return null;
        }
        // 设置请求超时
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 8000);

    try {
        const response = await fetch(`${API_BASE_URL}/user/current`, {
            headers: {
                    'Authorization': normalizedToken
                },
                signal: controller.signal
        });

            // 清除超时
            clearTimeout(timeoutId);

        if (!response.ok) {
            if (response.status === 401) {
                    console.warn("Token无效或已过期 (状态码: 401)");
                localStorage.removeItem('token');
                if (!window.location.pathname.includes('/auth/login')) {
                    window.location.href = '/auth/login';
                }
                return null;
            }
                throw new Error(`服务器返回错误状态: ${response.status}`);
        }

        const data = await response.json();
        if (data.code === 200) {
            updateNavbarUserInfo(data.data);
            return data.data;
        } else {
            console.warn("checkAuth: Backend validation response error code:", data.code, data.message);
            throw new Error(data.message || "验证失败");
        }
    } catch (fetchError) {
        console.error("checkAuth: Error during fetch or JSON parsing:", fetchError);
        if (fetchError.name === 'AbortError') {
            console.error('验证请求超时');
            throw new Error('验证请求超时，请检查网络连接');
        }
        throw fetchError;
    }
} catch (error) {
    console.error('验证登录状态失败:', error);

    if (error.message.includes('无效') ||
        error.message.includes('过期') ||
        error.message.includes('401')) {
        localStorage.removeItem('token');
    }

    if (!window.location.pathname.includes('/auth/login')) {
        window.location.href = '/auth/login';
    }
    return null;
}
}

// 获取客户端IP
async function getClientIP() {
    try {
        const response = await fetch('https://api.ipify.org?format=json');
        const data = await response.json();
        return data.ip;
    } catch (error) {
        console.error('获取IP失败:', error);
        return '';
    }
}

// 添加更详细的错误处理
class ApiError extends Error {
    constructor(message, code) {
        super(message);
        this.code = code;
        this.name = 'ApiError';
    }
}

async function handleApiResponse(response) {
    const data = await response.json();
    if (data.code !== 200) {
        throw new ApiError(data.message, data.code);
    }
    return data.data;
}

function handleError(error) {
    if (error instanceof ApiError) {
        showToast(error.message, 'error');
    } else if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
        showToast('网络连接失败，请检查网络设置', 'error');
    } else {
        showToast('操作失败，请稍后重试', 'error');
    }
    console.error(error);
}

// 添加通用请求函数
async function apiRequest(url, options = {}) {
    const token = getAuthHeader(); // 使用统一获取认证头的函数
    if (!token && !url.includes('/login') && !url.includes('/register')) {
        window.location.href = '/auth/login';
        return;
    }

    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': token
        }
    };

    try {
        const response = await fetch(url, { ...defaultOptions, ...options });

        if (response.status === 401) {
            console.warn("API请求返回401未授权");
            localStorage.removeItem('token');
            window.location.href = '/auth/login';
            return;
        }

        const data = await response.json();
        if (data.code === 200) {
            return data.data;
        } else {
            throw new Error(data.message);
        }
    } catch (error) {
        handleError(error);
        throw error;
    }
}

// Toast 通知功能
function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toast-container') || createToastContainer();

    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <span class="toast-message">${message}</span>
        </div>
        <button class="toast-close">&times;</button>
    `;

    const closeButton = toast.querySelector('.toast-close');
    closeButton.addEventListener('click', () => {
        toast.classList.add('fade-out');
        setTimeout(() => toast.remove(), 300);
    });

    toastContainer.appendChild(toast);

    // 自动消失
    setTimeout(() => {
        toast.classList.add('fade-out');
        setTimeout(() => toast.remove(), 300);
    }, 3000);
}

function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toast-container';
    document.body.appendChild(container);
    return container;
}

// 在页面加载完成后初始化导航栏和通用功能
document.addEventListener('DOMContentLoaded', async function() {
    // 插入导航栏（如果页面没有）
    insertNavbar();

    // 设置导航项高亮
    setActiveNavItem();

    // 登录状态检查
    await checkAuth();

    // 设置用户下拉菜单
    setupUserDropdown();

    // AFTER insertNavbar() and setupUserDropdown(), re-bind or ensure logout listener is correctly setup
    // It's usually better to setup the listener inside insertNavbar or right after it.
    // For this example, assuming 'logout' element is available after insertNavbar.
    const logoutLink = document.getElementById('logout');
    if (logoutLink) {
        // Remove any existing listener to avoid duplicates if this runs multiple times
        // A more robust solution would be to ensure this setup runs only once.
        // For now, this is a simple way to try and ensure it's freshly bound.
        // logoutLink.replaceWith(logoutLink.cloneNode(true)); // Re-cloning can also remove listeners
        // const freshLogoutLink = document.getElementById('logout');

        // It's better to have the original event listener attachment point (likely in insertNavbar) modified.
        // However, if insertNavbar is not in the visible scope, we are modifying it here.
        // If the original listener is still there, this might lead to issues.
        // The ideal place for this logic is where the 'logout' element's listener is first defined.
        // We will assume this is the main point or that insertNavbar calls this.

        // Try to remove any pre-existing listener if this code might run multiple times.
        // A cleaner way is to ensure event listeners are set up once.
        // However, given the context, this is an attempt to make it work.
        // Create a new function for the listener to ensure it's the one we control
        const handleLogout = async function(e) {
            e.preventDefault();
            console.log("Logout link clicked. Attempting logout.");

            const token = getAuthHeader(); // Get the current token

            if (token) {
                try {
                    console.log("Calling backend /api/user/logout");
                    const response = await fetch(`${API_BASE_URL}/user/logout`, {
                        method: 'POST',
                        headers: {
                            // The getAuthHeader() should return the raw token.
                            // If your backend expects "Bearer <token>", and getAuthHeader() doesn't add it,
                            // and your $.ajaxSetup doesn't cover 'fetch', you might need 'Authorization': 'Bearer ' + token
                            'Authorization': token, // Assuming getAuthHeader() provides the correct format
                            'Content-Type': 'application/json'
                        }
                        // No body needed for this specific logout if backend only checks token
                    });

                    if (response.ok) {
                        const responseData = await response.json(); // Try to parse JSON
                        if (responseData.code === 200) {
                            console.log("Successfully logged out from backend:", responseData.message || "Success");
                        } else {
                            console.warn("Backend logout returned an error code:", responseData.code, responseData.message || "No message");
                        }
                    } else {
                        console.warn(`Backend logout call failed with status: ${response.status} - ${response.statusText}`);
                        // Try to get error message from body if any
                        try {
                            const errorData = await response.json();
                            console.warn("Backend logout error details:", errorData.message || "No error details");
                        } catch (parseError) {
                            // Silent if no JSON body
                        }
                    }
                } catch (error) {
                    console.error("Error during backend logout API call:", error);
                }
            } else {
                console.warn("No token found in localStorage for backend logout call.");
            }

            // Always perform client-side logout actions
            localStorage.removeItem('token');
            console.log("Token removed from localStorage.");
            showToast('您已成功退出。正在跳转到登录页面...', 'success');
            setTimeout(() => {
                window.location.href = '/auth/login';
                console.log("Redirecting to login page.");
            }, 1500); // Delay for toast visibility
        };

        // To avoid multiple listeners if this code runs more than once.
        // This is a common issue if DOMContentLoaded handlers are complex or re-run.
        // A simple flag or checking for an existing custom attribute can help.
        if (!logoutLink.dataset.logoutListenerAttached) {
            logoutLink.addEventListener('click', handleLogout);
            logoutLink.dataset.logoutListenerAttached = 'true';
            console.log("Logout event listener attached.");
        }

    } else {
        console.warn("Logout link with id 'logout' not found after DOMContentLoaded. Navbar might not be inserted yet or ID is incorrect.");
    }
});

// 插入统一导航栏
function insertNavbar() {
    // Temporarily comment out the condition to force insertion for diagnostics
    // if (!existingNavbar) {
        const navbarHTML = `
        <nav class="navbar">
            <div class="nav-brand">
                <a href="/">Maizi EDU</a>
            </div>
            <div class="nav-menu">
                <a href="/main/chat" class="nav-item">出题</a>
                <a href="/topics/upload-topics" class="nav-item">上传</a>
                <a href="/paper/generate" class="nav-item">组卷</a>
                <a href="/papers/duplicate-check" class="nav-item">查重</a>
                <a href="/topics/bank" class="nav-item">题库</a>
            </div>
            <div class="nav-user"> <!-- User section restored for testing -->
                <div class="user-info">
                    <img src="/static/images/default-avatar.png" alt="avatar" class="avatar">
                    <span class="username">加载中...</span>
                </div>
                <div class="dropdown-menu">
                    <a href="/user/profile">个人信息</a>
                    <a href="#" id="logout">退出登录</a>
                </div>
            </div>
        </nav>
        `;

        // 插入到body开始
        try {
            document.body.insertAdjacentHTML('afterbegin', navbarHTML);
        } catch (e) {
            console.error("insertNavbar: Error during insertAdjacentHTML:", e); // Diagnostic log for error
        }

        const logoutLink = document.getElementById('logout');
        if (logoutLink && !logoutLink.dataset.logoutListenerAttached) {
            logoutLink.addEventListener('click', async function(e) {
                e.preventDefault();
                console.log("退出登录按钮被点击");
                await logout();
            });
            logoutLink.dataset.logoutListenerAttached = 'true';
        }
    // } else { // End of temporarily commented out condition
    //     console.log("insertNavbar: Navbar already exists (document.querySelector('.navbar') found an element). Navbar not inserted by common.js."); // Diagnostic log
    // }
}

// 设置导航项高亮
function setActiveNavItem() {
    const currentPath = window.location.pathname;
    const navItems = document.querySelectorAll('.nav-menu .nav-item');

    navItems.forEach(item => {
        item.classList.remove('active');
        const href = item.getAttribute('href');

        if (currentPath === '/' && href === '/') {
            item.classList.add('active');
        } else if (currentPath.includes('chat') && href.includes('chat')) {
            item.classList.add('active');
        } else if (currentPath.includes('upload') && href.includes('upload')) {
            item.classList.add('active');
        } else if (currentPath.includes('generate') && href.includes('generate')) {
            item.classList.add('active');
        } else if (currentPath.includes('check') && href.includes('check')) {
            item.classList.add('active');
        } else if (currentPath.includes('bank') && href.includes('bank')) {
            item.classList.add('active');
        } else if (currentPath.includes('profile') && href.includes('profile')) {
            item.classList.add('active');
        }
    });
}

// 设置用户下拉菜单
function setupUserDropdown() {
    const userInfo = document.querySelector('.user-info');
    const dropdownMenu = document.querySelector('.dropdown-menu');

    if (userInfo && dropdownMenu) {
        userInfo.addEventListener('click', function() {
            dropdownMenu.style.display = dropdownMenu.style.display === 'block' ? 'none' : 'block';
        });

        // 点击外部关闭下拉菜单
        document.addEventListener('click', function(event) {
            if (!userInfo.contains(event.target) && !dropdownMenu.contains(event.target)) {
                dropdownMenu.style.display = 'none';
            }
        });
    }
}

// 设置全局AJAX头
$(document).ready(function() {
    // 为所有jQuery AJAX请求设置全局处理
    $.ajaxSetup({
        beforeSend: function(xhr) {
            const token = getAuthHeader();
            if (token) {
                xhr.setRequestHeader('Authorization', 'Bearer ' + token);
            }
        }
    });

    // 全局AJAX错误处理
    $(document).ajaxError(function(event, jqXHR, ajaxSettings, thrownError) {
        console.error("AJAX错误:", thrownError, "状态:", jqXHR.status);

        if (jqXHR.status === 401) {
            console.warn("认证失败（401）- 可能需要重新登录");
            // 仅在非登录页跳转
            if (!window.location.pathname.includes('/auth/login')) {
                localStorage.removeItem('token');
                window.location.href = '/auth/login';
            }
        }
    });
});

// 更新导航栏用户信息
function updateNavbarUserInfo(userData) {
    const usernameElements = document.querySelectorAll('.username');
    const avatarElements = document.querySelectorAll('.avatar');

    let newUsername = '用户'; // Default username
    if (userData && (userData.nickname || userData.username)) {
        newUsername = userData.nickname || userData.username;
    } else {
        console.warn("updateNavbarUserInfo: userData is missing or lacks nickname/username. Defaulting to '用户'.");
    }

    usernameElements.forEach(element => {
        element.textContent = newUsername;
    });

    if (userData && userData.avatar) {
        try {
            // 正确处理头像路径
            let avatarPath = userData.avatar.trim();

            // 确保是完整的URL或正确的路径
            if (avatarPath.startsWith('//')) {
                // 相对协议URL，添加协议
                avatarPath = window.location.protocol + avatarPath;
            } else if (!avatarPath.startsWith('http') && !avatarPath.startsWith('/')) {
                // 不是完整URL且不以/开头，添加/
                avatarPath = '/' + avatarPath;
            }

            // 修复常见路径问题
            if (avatarPath.includes('avatars/') && !avatarPath.includes('/uploads/')) {
                avatarPath = avatarPath.replace('avatars/', '/uploads/avatars/');
            }

            // 使用当前域名构建绝对URL（如果是相对路径）
            if (!avatarPath.startsWith('http')) {
                const baseUrl = window.location.origin; // 动态获取当前域名和端口

                // 确保没有双斜杠问题
                if (avatarPath.startsWith('/')) {
                    avatarPath = baseUrl + avatarPath;
                } else {
                    avatarPath = baseUrl + '/' + avatarPath;
                }
            }

            // 修复双斜杠问题
            avatarPath = avatarPath.replace(/([^:])\/\//g, '$1/');

            // 使用fixAvatarUrl辅助函数（如果存在）
        if (window.fixAvatarUrl) {
            avatarPath = window.fixAvatarUrl(avatarPath);
        }

        avatarElements.forEach(element => {
                // 直接使用内联SVG作为默认头像
                useFallbackSvgAvatar(element);

                // 然后尝试加载用户头像
                const img = new Image();
                img.onload = function() {
                    // 只有成功加载后才设置头像
                    element.src = `${avatarPath}?t=${new Date().getTime()}`;
                };
                img.onerror = function() {
                    // 如果加载失败，保留已设置的默认SVG头像
                    console.warn('Failed to preload avatar:', avatarPath);
                };
                img.src = avatarPath;
            });
        } catch (e) {
            console.error('处理头像路径时出错:', e);
            // 出错时使用默认头像
            avatarElements.forEach(element => {
                useFallbackSvgAvatar(element);
            });
        }
    } else {
        // Default avatar logic if no userData or no userData.avatar
        console.warn("updateNavbarUserInfo: No avatar data found. Applying default avatar.");
        avatarElements.forEach(element => {
            useFallbackSvgAvatar(element); // Ensure this is robust
        });
    }
}

// 获取题目类型的显示信息（文本和图标）
function getTopicTypeInfo(typeKey) {
    const typeMap = {
        'choice': { text: '单选题', icon: 'bi-check-circle' },
        'single_choice': { text: '单选题', icon: 'bi-check-circle' }, // Alias for modal value
        'multiple': { text: '多选题', icon: 'bi-list-check' },
        'multiple_choice': { text: '多选题', icon: 'bi-list-check' }, // Alias for modal value
        'judge': { text: '判断题', icon: 'bi-question-octagon' },
        'fill': { text: '填空题', icon: 'bi-input-cursor-text' },
        'fill_in_blank': { text: '填空题', icon: 'bi-input-cursor-text' }, // Alias for modal value
        'short': { text: '简答题', icon: 'bi-pencil-square' },
        'subjective': { text: '主观题', icon: 'bi-pencil-square' },
        'essay': { text: '简答题/主观题', icon: 'bi-pencil-square' }, // Alias for modal value
        'group': { text: '题组', icon: 'bi-collection' }, // Using '题组' for sub-questions of a group
        'composite': { text: '综合题', icon: 'bi-grid-3x3-gap' }, // Alias for modal value, typically main topic
        'programming': { text: '编程题', icon: 'bi-code-slash' },
        'default': { text: '未知类型', icon: 'bi-question-lg' }
    };
    return typeMap[typeKey.toLowerCase()] || typeMap['default'];
}

// 统一的退出登录函数
async function logout() {
    try {
        const token = getAuthHeader();
        if (token) {
            try {
                console.log("调用后端退出登录API");
                const response = await fetch(`${API_BASE_URL}/user/logout`, {
                    method: 'POST',
                    headers: {
                        'Authorization': token,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const responseData = await response.json();
                    console.log("后端退出登录成功:", responseData.message || "Success");
                } else {
                    console.warn(`后端退出登录失败，状态码: ${response.status}`);
                }
            } catch (error) {
                console.error("调用后端退出登录API时出错:", error);
            }
        }

        // 清除本地存储
        localStorage.removeItem('token');
        localStorage.removeItem('currentUser');

        // 清除Cookie
        document.cookie = 'JWT_TOKEN=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
        document.cookie = 'Authorization=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';

        // 显示提示并跳转
        if (typeof showToast === 'function') {
            showToast('您已成功退出登录', 'success');
        }

        setTimeout(() => {
            window.location.href = '/auth/login';
        }, 1500);

    } catch (error) {
        console.error("退出登录过程中出错:", error);
        // 即使出错也要清除本地数据并跳转
        localStorage.removeItem('token');
        localStorage.removeItem('currentUser');
        window.location.href = '/auth/login';
    }
}

// 调试辅助函数
window.debugAuth = async function() {
    const token = localStorage.getItem('token');
    console.log("当前token:", token);
    console.log("标准化后token:", normalizeToken(token));

    try {
        const response = await fetch(`${API_BASE_URL}/user/validate`, {
            headers: {
                'Authorization': normalizeToken(token)
            }
        });

        console.log("验证状态:", response.status);
        const data = await response.json();
        console.log("验证响应:", data);

        return {
            token,
            normalizedToken: normalizeToken(token),
            isValid: data.code === 200,
            response: data
        };
    } catch (error) {
        console.error("调试验证失败:", error);
        return {
            token,
            normalizedToken: normalizeToken(token),
            isValid: false,
            error: error.message
        };
    }
};

// 修复头像加载失败的处理逻辑
function handleAvatarError(element) {
    // 记录错误
    console.error('Failed to load avatar from:', element.src);

    try {
        // 尝试使用完整URL路径
        const baseUrl = window.location.origin;

    // 只有当当前src不是默认头像时才替换为默认头像
    if (!element.src.includes('default-avatar.png')) {
            console.log('Trying to load default avatar...');

            // 使用完整路径
            const defaultAvatarUrl = baseUrl + '/static/images/default-avatar.png';
            element.src = defaultAvatarUrl;

            // 如果再次失败，使用内联SVG
            element.onerror = function() {
                console.warn('默认头像也加载失败，使用内联SVG作为备用');
                useFallbackSvgAvatar(element);
            };
        } else {
            // 直接使用内联SVG作为最后的备用方案
            useFallbackSvgAvatar(element);
        }
    } catch (e) {
        console.error('Avatar error handler failed:', e);
        // 确保在任何情况下都有一个可用的头像
        useFallbackSvgAvatar(element);
    }
}

// 使用内联SVG作为默认头像
function useFallbackSvgAvatar(element) {
    try {
        // 生成一个随机的头像颜色 (从主题色中选择)
        const colors = [
            { bg: '#e3f2fd', fg: '#2196f3' }, // 蓝色
            { bg: '#e8f5e9', fg: '#4caf50' }, // 绿色
            { bg: '#fff3e0', fg: '#ff9800' }, // 橙色
            { bg: '#f3e5f5', fg: '#9c27b0' }, // 紫色
            { bg: '#e8eaf6', fg: '#3f51b5' }, // 靛蓝色
            { bg: '#fce4ec', fg: '#e91e63' }  // 粉色
        ];

        // 使用用户ID或随机选择一个颜色
        const userId = element.getAttribute('data-user-id') || '';
        const colorIndex = userId ? (userId.split('').reduce((a, b) => a + b.charCodeAt(0), 0) % colors.length) :
            Math.floor(Math.random() * colors.length);
        const color = colors[colorIndex];

        // 创建一个更美观的用户头像SVG
        // 使用encodeURIComponent确保特殊字符被正确编码
        const svgContent = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36 36" width="36" height="36">
            <rect width="36" height="36" fill="${color.bg}" rx="18" ry="18" />
            <circle cx="18" cy="15" r="7" fill="${color.fg}" />
            <path d="M18,24c-5.5,0-10,3-10,7h20C28,27,23.5,24,18,24z" fill="${color.fg}" />
        </svg>`;
        const svgData = 'data:image/svg+xml;base64,' + btoa(svgContent);

        element.src = svgData;
        element.onerror = null; // 防止无限循环
    } catch (e) {
        console.error('Failed to set SVG fallback:', e);

        // 作为绝对最后的手段，设置一个简单的彩色SVG
        try {
            const simpleColor = '#3466f6'; // 主题蓝色
            const simpleSvgContent = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36 36" width="36" height="36">
                <rect width="36" height="36" fill="#e3f2fd" rx="18" ry="18" />
                <text x="18" y="24" font-family="Arial" font-size="20" fill="${simpleColor}" text-anchor="middle">U</text>
            </svg>`;
            element.src = 'data:image/svg+xml;base64,' + btoa(simpleSvgContent);
        } catch (e2) {
            console.error('Critical avatar error:', e2);

            // 绝对最小的SVG作为最后手段
            const fallbackSvgContent = '<svg xmlns="http://www.w3.org/2000/svg" width="36" height="36"><circle cx="18" cy="18" r="18" fill="#3466f6"/></svg>';
            element.src = 'data:image/svg+xml;base64,' + btoa(fallbackSvgContent);
        }
    }
}

// 为所有头像添加错误处理
document.addEventListener('DOMContentLoaded', function() {
    const avatars = document.querySelectorAll('.avatar');
    avatars.forEach(avatar => {
        avatar.onerror = function() {
            handleAvatarError(this);
        };
    });
});

// 全局图片错误处理
window.addEventListener('error', function(e) {
    if (e.target.tagName === 'IMG') {
        const imgSrc = e.target.src || '';

        // 过滤无效的URL，如当前页面URL或空值
        if (!imgSrc || imgSrc === 'about:blank' || imgSrc.includes('#') ||
            imgSrc === window.location.href || imgSrc === window.location.origin + window.location.pathname) {
            // 对于无效的URL，直接删除src属性或设置为空
            e.target.removeAttribute('src');
            return;
        }

        console.warn('Image failed to load:', imgSrc);

        // 防止重复请求不存在的资源
        if (e.target.dataset.errorHandled !== 'true') {
            e.target.dataset.errorHandled = 'true';

            // 对于头像特殊处理
            if (e.target.classList.contains('avatar') && !imgSrc.includes('default-avatar.png')) {
                e.target.src = '/static/images/default-avatar.png';
            }
        }
    }
}, true);