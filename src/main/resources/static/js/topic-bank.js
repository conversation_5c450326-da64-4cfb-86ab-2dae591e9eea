/**
 * Topic Bank Management JavaScript
 * Handles all client-side logic for the topic management interface
 */

// Global variables
let currentPage = 1;
let pageSize = 10;
let totalPages = 0;
let currentSortBy = 'id';
let currentSortOrder = 'desc';
let isEditMode = false;
let currentTopicId = null;
let selectedKnowledgePointId = null;
let knowledgePoints = [];

// Initialize when DOM is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize event listeners
    initEventListeners();
    
    // Load knowledge points for sidebar
    loadKnowledgePoints();
    
    // Load topics on page load
    loadTopics();
});

/**
 * Initialize all event listeners
 */
function initEventListeners() {
    // Search form submission
    document.getElementById('searchForm').addEventListener('submit', function(e) {
        e.preventDefault();
        currentPage = 1; // Reset to first page on new search
        loadTopics();
    });
    
    // Refresh button
    document.getElementById('refreshBtn').addEventListener('click', function() {
        // Clear any selected knowledge point
        selectedKnowledgePointId = null;
        document.getElementById('knowId').value = '';
        
        // Reset active class on all knowledge point links
        document.querySelectorAll('.knowledge-point-link').forEach(link => {
            link.classList.remove('active');
        });
        
        loadTopics();
    });
    
    // Add Topic button - already has data-bs-toggle="modal" and data-bs-target="#topicModal"
    document.getElementById('addTopicBtn').addEventListener('click', function() {
        resetTopicForm();
        isEditMode = false;
        document.getElementById('topicModalLabel').innerHTML = '<i class="bi bi-plus-circle me-2"></i>新增题目';
        
        // Pre-fill knowledge point ID if one is selected
        if (selectedKnowledgePointId) {
            document.getElementById('topicKnowId').value = selectedKnowledgePointId;
        }
    });
    
    // Save Topic button in modal
    document.getElementById('saveTopicButton').addEventListener('click', function() {
        saveTopic();
    });
    
    // Topic type change in modal - to show/hide options container
    document.getElementById('topicTypeModal').addEventListener('change', function() {
        handleTopicTypeChange();
    });
    
    // Add Option button in modal
    document.getElementById('addOptionButton').addEventListener('click', function() {
        addOptionField();
    });
    
    // Sorting dropdown items
    document.querySelectorAll('.sort-item').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            currentSortBy = this.getAttribute('data-sort-by');
            currentSortOrder = this.getAttribute('data-sort-order');
            
            // Update dropdown button text
            const sortText = this.textContent;
            document.getElementById('sortDropdown').innerHTML = `<i class="bi bi-sort-down me-1"></i>${sortText}`;
            
            // Reload topics with new sorting
            loadTopics();
        });
    });
}

/**
 * Load knowledge points for the sidebar
 */
function loadKnowledgePoints() {
    console.log('Loading knowledge points for sidebar...');
    
    // Show loading state in the sidebar
    document.getElementById('knowledgePointAccordion').innerHTML = `
        <div class="text-center py-4" id="knowledgePointsLoading">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-3 text-muted">正在加载知识点分类...</p>
        </div>
    `;
    
    const token = localStorage.getItem('token');
    if (!token) {
        console.error('Authentication token missing');
        showToast('请先登录', 'error');
        setTimeout(() => {
            window.location.href = '/auth/login';
        }, 1500);
        return;
    }
    
    // First, fetch all knowledge groups
    console.log('Fetching knowledge groups from API...');
    fetch('/api/knowledge/groups', {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    })
    .then(response => {
        console.log('Knowledge groups API response status:', response.status);
        if (!response.ok) {
            throw new Error(`Failed to fetch knowledge groups: ${response.status} ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Knowledge groups data received:', data);
        if (data.success && data.data && Array.isArray(data.data)) {
            // Process the knowledge groups and load knowledge points for each group
            // Map the response data to the expected format
            const groups = data.data.map(group => {
                console.log('Processing group:', group);
                return {
                    id: group.id,
                    name: group.groupName || group.name,
                    count: group.count || 0,
                    order: group.sort || group.order || 0
                };
            });
            console.log('Processed groups:', groups);
            processKnowledgeGroups(groups, token);
        } else {
            console.error('Invalid knowledge groups data format:', data);
            throw new Error(data.message || 'Failed to fetch knowledge groups: Invalid data format');
        }
    })
    .catch(error => {
        console.error('Error fetching knowledge groups:', error);
        showToast('加载知识点分类失败: ' + error.message, 'error');
        // Show error in the sidebar with more detailed information
        document.getElementById('knowledgePointAccordion').innerHTML = `
            <div class="text-center py-4">
                <i class="bi bi-exclamation-circle text-danger" style="font-size: 2rem;"></i>
                <p class="mt-3 text-muted">加载知识点失败</p>
                <p class="small text-danger mb-3">${error.message}</p>
                <button class="btn btn-outline-primary mt-2" onclick="loadKnowledgePoints()">
                    <i class="bi bi-arrow-clockwise me-1"></i>重试
                </button>
            </div>
        `;
    });
}

/**
 * Process knowledge groups and load knowledge points for each group
 */
function processKnowledgeGroups(groups, token) {
    console.log('Processing knowledge groups:', groups.length, 'groups found');
    
    // Sort groups by their order property if available
    const sortedGroups = groups.sort((a, b) => {
        if (a.order !== undefined && b.order !== undefined) {
            return a.order - b.order;
        }
        return 0; // Keep original order if no order property
    });
    
    // Prepare the accordion HTML
    let html = '';
    
    if (sortedGroups.length === 0) {
        html = `
            <div class="alert alert-info m-3">
                <i class="bi bi-info-circle me-2"></i>
                暂无知识点分类数据
            </div>
        `;
    } else {
        sortedGroups.forEach((group, index) => {
            const groupId = group.id;
            const headingId = `heading-${groupId}`;
            const collapseId = `collapse-${groupId}`;
            
            // Format the count badge
            const countBadge = group.count > 0 
                ? `<span class="badge bg-primary rounded-pill ms-2" title="该分类下的知识点数量">${group.count}</span>` 
                : '';
            
            html += `
                <div class="accordion-item">
                    <h2 class="accordion-header" id="${headingId}">
                        <button class="accordion-button ${index === 0 ? '' : 'collapsed'}" type="button" 
                                data-bs-toggle="collapse" data-bs-target="#${collapseId}" 
                                aria-expanded="${index === 0 ? 'true' : 'false'}" aria-controls="${collapseId}">
                            <i class="bi bi-folder me-2 text-primary"></i>
                            <span class="fw-medium">${group.name}</span> ${countBadge}
                        </button>
                    </h2>
                    <div id="${collapseId}" class="accordion-collapse collapse ${index === 0 ? 'show' : ''}" 
                         aria-labelledby="${headingId}" data-bs-parent="#knowledgePointAccordion">
                        <div class="accordion-body p-0">
                            <ul class="list-group list-group-flush" id="group-points-${groupId}">
                                <li class="list-group-item text-center py-3">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <span class="ms-2">加载知识点...</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            `;
        });
    }
    
    // Update the accordion with the groups
    const accordionContainer = document.getElementById('knowledgePointAccordion');
    accordionContainer.innerHTML = html;
    
    // Load knowledge points for each group
    if (sortedGroups.length > 0) {
        console.log('Loading knowledge points for each group...');
        sortedGroups.forEach(group => {
            loadKnowledgePointsByGroup(group.id, token);
        });
    }
}

/**
 * Load knowledge points for a specific group
 */
function loadKnowledgePointsByGroup(groupId, token) {
    console.log(`Loading knowledge points for group ${groupId}...`);
    
    // Set a timeout to handle API timeouts
    const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Request timeout after 10 seconds')), 10000);
    });
    
    // Actual fetch request
    const fetchPromise = fetch(`/api/knowledge/points?groupId=${groupId}`, {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/json',
            'Cache-Control': 'no-cache'
        }
    });
    
    // Race between fetch and timeout
    Promise.race([fetchPromise, timeoutPromise])
        .then(response => {
            console.log(`Group ${groupId} API response status:`, response.status);
            if (!response.ok) {
                throw new Error(`Failed to fetch knowledge points for group ${groupId}: ${response.status} ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log(`Group ${groupId} data received:`, data);
            if (data.success && data.data) {
                // Map the response data to the expected format
                const points = data.data.map(point => {
                    // Log any unusual data to help with debugging
                    if (!point.knowledgeId && !point.id) {
                        console.warn('Knowledge point missing ID:', point);
                    }
                    if (!point.name) {
                        console.warn('Knowledge point missing name:', point);
                    }
                    
                    // Extract ID from different possible formats
                    let id = null;
                    if (point.knowledgeId) {
                        id = point.knowledgeId;
                    } else if (point.id) {
                        id = point.id;
                    } else if (point.knowledge_id) {
                        id = point.knowledge_id;
                    }
                    
                    // Extract name from different possible formats
                    let name = null;
                    if (point.name) {
                        name = point.name;
                    } else if (point.knowledgeName) {
                        name = point.knowledgeName;
                    } else if (point.knowledge_name) {
                        name = point.knowledge_name;
                    }
                    
                    return {
                        id: id,
                        name: name || '未命名知识点' // Default name if missing
                    };
                }).filter(point => point.id && point.name); // Filter out points without an ID or name
                
                renderKnowledgePointsForGroup(groupId, points);
            } else {
                console.error(`Invalid data format for group ${groupId}:`, data);
                throw new Error(data.message || `Failed to fetch knowledge points for group ${groupId}: Invalid data format`);
            }
        })
        .catch(error => {
            console.error(`Error fetching knowledge points for group ${groupId}:`, error);
            // Show error in the group's points list with retry button
            const container = document.getElementById(`group-points-${groupId}`);
            if (container) {
                container.innerHTML = `
                    <li class="list-group-item text-center py-3">
                        <i class="bi bi-exclamation-triangle text-warning me-2"></i>
                        <span class="text-muted">加载失败: ${error.message}</span>
                        <button class="btn btn-sm btn-outline-primary mt-2" onclick="retryLoadKnowledgePoints(${groupId})">
                            <i class="bi bi-arrow-clockwise me-1"></i>重试
                        </button>
                    </li>
                `;
            }
        });
}

/**
 * Render knowledge points for a specific group
 */
function renderKnowledgePointsForGroup(groupId, points) {
    console.log(`Rendering knowledge points for group ${groupId}:`, points);
    const pointsContainer = document.getElementById(`group-points-${groupId}`);
    
    if (!pointsContainer) {
        console.error(`Container for group ${groupId} not found`);
        return;
    }
    
    if (!points || points.length === 0) {
        pointsContainer.innerHTML = `
            <li class="list-group-item text-center py-3">
                <i class="bi bi-info-circle text-muted me-2"></i>
                <span class="text-muted">暂无知识点</span>
            </li>`;
        return;
    }
    
    let html = '';
    
    // Sort points alphabetically by name
    const sortedPoints = points.sort((a, b) => {
        if (a.name && b.name) {
            return a.name.localeCompare(b.name, 'zh-CN');
        }
        return 0;
    });
    
    sortedPoints.forEach(point => {
        // Create a safe version of the name for use in onclick
        const safeName = point.name.replace(/'/g, "\\'");
        
        html += `
            <li class="list-group-item py-2">
                <a href="#" class="knowledge-point-link d-flex justify-content-between align-items-center" 
                   data-id="${point.id}" onclick="selectKnowledgePoint(${point.id}, '${safeName}'); return false;">
                    <div>
                        <i class="bi bi-bookmark me-2 text-primary"></i>
                        <span>${point.name}</span>
                    </div>
                    <span class="badge bg-primary rounded-pill knowledge-point-count" 
                          data-id="${point.id}" title="相关题目数量">
                        <i class="bi bi-question-circle-fill me-1"></i>
                        <span class="count-value">-</span>
                    </span>
                </a>
            </li>
        `;
    });
    
    pointsContainer.innerHTML = html;
    
    // After rendering, fetch topic counts for this group's knowledge points
    updateKnowledgePointCountsForGroup(groupId);
}

/**
 * Update the topic counts for knowledge points in a specific group
 */
function updateKnowledgePointCountsForGroup(groupId) {
    console.log(`Updating topic counts for knowledge points in group ${groupId}...`);
    const token = localStorage.getItem('token');
    if (!token) {
        console.warn('Token missing, cannot update topic counts');
        return;
    }
    
    // Get all knowledge point IDs in this group
    const knowledgePointIds = [];
    document.querySelectorAll(`#group-points-${groupId} .knowledge-point-link`).forEach(link => {
        const id = link.getAttribute('data-id');
        if (id) knowledgePointIds.push(parseInt(id));
    });
    
    if (knowledgePointIds.length === 0) {
        console.log(`No knowledge points found in group ${groupId}`);
        return;
    }
    
    console.log(`Fetching topic counts for ${knowledgePointIds.length} knowledge points in group ${groupId}`);
    
    // Set a timeout for the request
    const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Request timeout after 8 seconds')), 8000);
    });
    
    // Use GET request with query parameters instead of POST
    // Build query string with knowledge point IDs
    const queryParams = new URLSearchParams();
    knowledgePointIds.forEach(id => queryParams.append('ids', id));
    
    const fetchPromise = fetch(`/api/knowledge/topic-counts?${queryParams.toString()}`, {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/json',
            'Cache-Control': 'no-cache'
        }
    });
    
    // Race between fetch and timeout
    Promise.race([fetchPromise, timeoutPromise])
        .then(response => {
            if (!response.ok) {
                // If the API returns an error, fall back to mock data
                console.warn(`API returned error ${response.status}, using fallback data`);
                return { 
                    success: true, 
                    data: generateFallbackTopicCounts(knowledgePointIds) 
                };
            }
            return response.json();
        })
        .then(data => {
            console.log(`Raw topic counts data for group ${groupId}:`, data);
            
            if (data.success === true && data.data) {
                console.log(`Topic counts received for group ${groupId}:`, data.data);
                updateKnowledgePointCountBadges(data.data);
            } else if (data.typeAvailability !== undefined) {
                // Direct response format without ApiResponse wrapper
                console.log(`Direct topic counts received for group ${groupId}:`, data);
                updateKnowledgePointCountBadges(data);
            } else {
                console.warn(`Invalid topic count data format for group ${groupId}:`, data);
                // Fall back to mock data
                updateKnowledgePointCountBadges(generateFallbackTopicCounts(knowledgePointIds));
            }
        })
        .catch(error => {
            console.error(`Error fetching topic counts for group ${groupId}:`, error);
            // Use fallback data instead of hiding badges
            updateKnowledgePointCountBadges(generateFallbackTopicCounts(knowledgePointIds));
        });
}

/**
 * Generate fallback topic counts when API fails
 */
function generateFallbackTopicCounts(knowledgePointIds) {
    console.log('Generating fallback topic counts for', knowledgePointIds.length, 'knowledge points');
    const counts = {};
    
    // Try to use localStorage to persist mock counts between page reloads
    let storedCounts = {};
    try {
        const stored = localStorage.getItem('mockTopicCounts');
        if (stored) {
            storedCounts = JSON.parse(stored);
            console.log('Retrieved stored mock counts:', storedCounts);
        }
    } catch (e) {
        console.warn('Failed to retrieve stored counts:', e);
    }
    
    knowledgePointIds.forEach(id => {
        if (storedCounts[id]) {
            // Use stored count if available
            counts[id] = storedCounts[id];
        } else {
            // Generate a new deterministic count based on ID
            counts[id] = (id % 10) + Math.floor(id / 10) % 5;
            // Store for future use
            storedCounts[id] = counts[id];
        }
    });
    
    // Save updated counts to localStorage
    try {
        localStorage.setItem('mockTopicCounts', JSON.stringify(storedCounts));
    } catch (e) {
        console.warn('Failed to store mock counts:', e);
    }
    
    return counts;
}

/**
 * Retry loading knowledge points for a specific group
 */
function retryLoadKnowledgePoints(groupId) {
    console.log(`Retrying load of knowledge points for group ${groupId}...`);
    const token = localStorage.getItem('token');
    if (!token) {
        showToast('请先登录', 'error');
        return;
    }
    
    // Show loading state
    const container = document.getElementById(`group-points-${groupId}`);
    if (container) {
        container.innerHTML = `
            <li class="list-group-item text-center py-3">
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <span class="ms-2">重新加载知识点...</span>
            </li>
        `;
    }
    
    // Retry loading
    loadKnowledgePointsByGroup(groupId, token);
}

/**
 * Update the topic counts for all knowledge points
 */
function updateKnowledgePointCounts() {
    // This function will be called after all groups are loaded
    // We'll update counts group by group instead of all at once
    // This is handled by updateKnowledgePointCountsForGroup
}

/**
 * Get topic counts for a specific knowledge point
 * This function is no longer needed as we're using the real API
 * but kept for backward compatibility
 */
function getMockTopicCounts(knowledgePointIds) {
    console.warn('getMockTopicCounts is deprecated, use the real API instead');
    const counts = {};
    knowledgePointIds.forEach(id => {
        counts[id] = 0; // Default to 0 instead of random values
    });
    return counts;
}

/**
 * Clear the knowledge point filter
 */
function clearKnowledgePointFilter() {
    console.log('Clearing knowledge point filter');
    
    // Clear selected knowledge point ID
    selectedKnowledgePointId = null;
    
    // Clear knowId input field
    document.getElementById('knowId').value = '';
    
    // Hide the selected knowledge point badge
    const selectedKnowledgeBadge = document.getElementById('selectedKnowledgePointBadge');
    if (selectedKnowledgeBadge) {
        selectedKnowledgeBadge.classList.remove('d-inline-flex');
        selectedKnowledgeBadge.classList.add('d-none');
    }
    
    // Remove active class from all knowledge point links
    document.querySelectorAll('.knowledge-point-link').forEach(link => {
        link.classList.remove('active');
    });
    
    // Show a toast notification
    showToast('已清除知识点筛选', 'info', 1500);
    
    // Reset to first page and load topics
    currentPage = 1;
    loadTopics();
}

/**
 * Update the knowledge point count badges
 */
function updateKnowledgePointCountBadges(counts) {
    console.log('Updating knowledge point count badges with data:', counts);
    document.querySelectorAll('.knowledge-point-count').forEach(badge => {
        const id = badge.getAttribute('data-id');
        const countElement = badge.querySelector('.count-value');
        
        if (!countElement) {
            console.warn(`Count element not found in badge for ID ${id}`);
            return;
        }
        
        if (id && counts[id] && counts[id] > 0) {
            countElement.textContent = counts[id];
            badge.classList.remove('bg-secondary');
            badge.classList.add('bg-primary');
            badge.style.display = 'inline-flex';
        } else {
            countElement.textContent = '0';
            badge.classList.remove('bg-primary');
            badge.classList.add('bg-secondary');
            // Still show the badge but with a different style
            badge.style.display = 'inline-flex';
        }
    });
}

/**
 * Select a knowledge point and filter topics
 */
function selectKnowledgePoint(id, name) {
    console.log(`Selecting knowledge point: ${id} - ${name}`);
    
    // Update selected knowledge point ID
    selectedKnowledgePointId = id;
    
    // Update knowId input field
    document.getElementById('knowId').value = id;
    
    // Update UI to show selected knowledge point
    const selectedKnowledgeDisplay = document.getElementById('selectedKnowledgePoint');
    const selectedKnowledgeBadge = document.getElementById('selectedKnowledgePointBadge');
    if (selectedKnowledgeDisplay && selectedKnowledgeBadge) {
        selectedKnowledgeDisplay.textContent = name;
        selectedKnowledgeBadge.classList.remove('d-none');
        selectedKnowledgeBadge.classList.add('d-inline-flex');
    }
    
    // Update active class on knowledge point links
    document.querySelectorAll('.knowledge-point-link').forEach(link => {
        if (link.getAttribute('data-id') == id) {
            link.classList.add('active');
            // Expand the parent accordion if it's collapsed
            const accordionItem = link.closest('.accordion-item');
            if (accordionItem) {
                const collapseElement = accordionItem.querySelector('.accordion-collapse');
                const button = accordionItem.querySelector('.accordion-button');
                if (collapseElement && !collapseElement.classList.contains('show')) {
                    // Programmatically expand the accordion
                    new bootstrap.Collapse(collapseElement).show();
                }
            }
        } else {
            link.classList.remove('active');
        }
    });
    
    // Show a toast notification
    showToast(`已选择知识点: ${name}`, 'info', 1500);
    
    // Reset to first page and load topics
    currentPage = 1;
    loadTopics();
}

/**
 * Load topics based on current filters and pagination
 */
function loadTopics() {
    // Show loading state
    document.getElementById('topicsList').innerHTML = `
        <tr>
            <td colspan="6" class="text-center py-5">
                <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-3 text-muted">正在加载题目数据...</p>
            </td>
        </tr>
    `;
    
    // Get filter values
    const keyword = document.getElementById('keyword').value.trim();
    const topicType = document.getElementById('topicType').value;
    const knowId = document.getElementById('knowId').value.trim() || (selectedKnowledgePointId ? selectedKnowledgePointId : '');
    
    // Build query parameters
    let queryParams = new URLSearchParams({
        pageNum: currentPage,  // Changed from 'page' to 'pageNum' to match backend
        pageSize: pageSize,
        sortBy: currentSortBy,
        sortOrder: currentSortOrder
    });
    
    // Add optional filters if they have values
    if (keyword) queryParams.append('keyword', keyword);
    if (topicType) queryParams.append('type', mapTopicTypeToBackend(topicType));
    if (knowId) queryParams.append('knowId', knowId);
    
    // Fetch topics from backend
    const token = localStorage.getItem('token');
    if (!token) {
        showToast('请先登录', 'error');
        setTimeout(() => {
            window.location.href = '/auth/login';
        }, 1500);
        return;
    }
    
    fetch(`/api/topics?${queryParams.toString()}`, {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.json();
    })
    .then(data => {
        if (data.code === 200 && data.data) {
            console.log('Data received for rendering topics:', JSON.stringify(data.data.records, null, 2));
            renderTopics(data.data);
            renderPagination(data.data);
        } else {
            showToast(data.message || '获取题目失败', 'error');
            document.getElementById('topicsList').innerHTML = `
                <tr>
                    <td colspan="6" class="text-center py-5">
                        <i class="bi bi-exclamation-circle text-danger" style="font-size: 3rem;"></i>
                        <p class="mt-3 text-muted">获取数据失败: ${data.message || '未知错误'}</p>
                    </td>
                </tr>
            `;
        }
    })
    .catch(error => {
        console.error('Error fetching topics:', error);
        showToast('获取题目失败: ' + error.message, 'error');
        document.getElementById('topicsList').innerHTML = `
            <tr>
                <td colspan="6" class="text-center py-5">
                    <i class="bi bi-exclamation-circle text-danger" style="font-size: 3rem;"></i>
                    <p class="mt-3 text-muted">获取数据失败: ${error.message}</p>
                    <button class="btn btn-outline-primary mt-2" onclick="loadTopics()">
                        <i class="bi bi-arrow-clockwise me-1"></i>重试
                    </button>
                </td>
            </tr>
        `;
    });
}

/**
 * Map frontend topic type values to backend enum values
 */
function mapTopicTypeToBackend(frontendType) {
    const typeMap = {
        'choice': 'SINGLE_CHOICE',
        'multiple': 'MULTIPLE_CHOICE',
        'judge': 'JUDGE',
        'fill': 'FILL_IN_BLANK',
        'short': 'ESSAY',
        'subjective': 'ESSAY'
        // Add other mappings as needed
    };
    
    return typeMap[frontendType] || frontendType;
}

/**
 * Map backend topic type enum values to human-readable text and badge class
 */
/**
 * Map backend topic type enum values to human-readable text and badge class
 */
function getTopicTypeInfo(backendType) {
    const typeInfo = {
        'choice': { text: '单选题', badgeClass: 'badge-single-choice' },
        'multiple': { text: '多选题', badgeClass: 'badge-multiple-choice' },
        'judge': { text: '判断题', badgeClass: 'badge-judge' },
        'fill': { text: '填空题', badgeClass: 'badge-fill' }, // Matched to DTO pattern 'fill'
        'short': { text: '简答题', badgeClass: 'badge-essay' }, // Matched to DTO pattern 'short'
        'subjective': { text: '主观题', badgeClass: 'badge-essay' }, // Matched to DTO pattern 'subjective', might be same as 简答题 or more general
        'group': { text: '组合题', badgeClass: 'badge-composite' }, // Matched to DTO pattern 'group'
        // 'PROGRAMMING' was in old version, not in DTO pattern, kept for now commented out
        // 'PROGRAMMING': { text: '编程题', badgeClass: 'badge-essay' }, 
    };
    
    return typeInfo[backendType.toLowerCase()] || { text: backendType, badgeClass: 'bg-secondary' };
}

/**
 * Render topics in the table
 */
function renderTopics(data) {
    const records = data.records || [];
    const tbody = document.getElementById('topicsList');
    
    if (records.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center py-5">
                    <i class="bi bi-inbox text-muted" style="font-size: 3rem;"></i>
                    <p class="mt-3 text-muted">暂无题目数据</p>
                </td>
            </tr>
        `;
        return;
    }
    
    let html = '';
    records.forEach(topic => {
        // Truncate title if too long
        let displayTitle = topic.title;
        if (displayTitle && displayTitle.length > 50) {
            displayTitle = displayTitle.substring(0, 50) + '...';
        }
        
        // Get type info for badge
        const typeInfo = getTopicTypeInfo(topic.type);
        
        // Generate difficulty stars
        const difficulty = parseFloat(topic.difficulty) || 0;
        let difficultyHtml = '';
        if (difficulty > 0) {
            difficultyHtml = `<div class="difficulty-indicator">`;
            for (let i = 0; i < Math.floor(difficulty); i++) {
                difficultyHtml += `<i class="bi bi-star-fill difficulty-star"></i>`;
            }
            if (difficulty % 1 > 0) {
                difficultyHtml += `<i class="bi bi-star-half difficulty-star"></i>`;
            }
            difficultyHtml += `<span class="ms-1">${difficulty.toFixed(1)}</span></div>`;
        } else {
            difficultyHtml = '-';
        }
        
        html += `
            <tr>
                <td>${topic.id}</td>
                <td><span class="badge badge-topic-type ${typeInfo.badgeClass}">${typeInfo.text}</span></td>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="ms-2">
                            <div class="fw-bold">${displayTitle || ''}</div>
                            <div class="small text-muted">${topic.score ? `分值: ${topic.score}` : ''}</div>
                        </div>
                    </div>
                </td>
                <td>${difficultyHtml}</td>
                <td>${topic.knowId ? `<span class="badge bg-light text-dark">${topic.knowId}</span>` : '-'}</td>
                <td class="action-buttons">
                    <div class="btn-group btn-group-sm" role="group" aria-label="Topic actions for ${topic.id}">
                        <button type="button" class="btn btn-outline-primary" onclick="viewTopic(${topic.id})" title="查看" data-bs-toggle="tooltip" data-bs-placement="top">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="editTopic(${topic.id})" title="编辑" data-bs-toggle="tooltip" data-bs-placement="top">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-custom-delete-hover" onclick="deleteTopic(${topic.id})" title="删除" data-bs-toggle="tooltip" data-bs-placement="top">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
    
    tbody.innerHTML = html;

    // Initialize Bootstrap tooltips for the newly rendered buttons
    const tooltipTriggerList = [].slice.call(tbody.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * Render pagination controls
 */
function renderPagination(data) {
    const pagination = document.getElementById('pagination');
    const total = data.total || 0;
    const pages = data.pages || 1;
    totalPages = pages;
    
    let html = '';
    
    // Previous button
    html += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1}); return false;">上一页</a>
        </li>
    `;
    
    // Page numbers
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(pages, startPage + 4);
    
    for (let i = startPage; i <= endPage; i++) {
        html += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="changePage(${i}); return false;">${i}</a>
            </li>
        `;
    }
    
    // Next button
    html += `
        <li class="page-item ${currentPage === pages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1}); return false;">下一页</a>
        </li>
    `;
    
    pagination.innerHTML = html;
    
    // Add page info
    const pageInfo = document.createElement('li');
    pageInfo.className = 'ms-2 d-flex align-items-center';
    pageInfo.innerHTML = `<span class="text-muted">共 ${total} 条记录，${pages} 页</span>`;
    pagination.appendChild(pageInfo);
}

/**
 * Change current page and reload topics
 */
function changePage(page) {
    if (page < 1 || page > totalPages) {
        return;
    }
    
    currentPage = page;
    loadTopics();
}

/**
 * Reset topic form for adding a new topic
 */
function resetTopicForm() {
    document.getElementById('topicForm').reset();
    document.getElementById('topicId').value = '';
    document.getElementById('optionsListContainer').innerHTML = '';
    document.getElementById('optionsContainer').style.display = 'none';
    document.getElementById('subsContainer').style.display = 'none';
}

/**
 * Handle topic type change in the modal
 * Show/hide options container based on topic type
 */
function handleTopicTypeChange() {
    const topicType = document.getElementById('topicTypeModal').value;
    const optionsContainer = document.getElementById('optionsContainer');
    const subsContainer = document.getElementById('subsContainer');
    
    // Show options container for choice questions
    if (topicType === 'SINGLE_CHOICE' || topicType === 'MULTIPLE_CHOICE') {
        optionsContainer.style.display = 'block';
        // Add at least 4 option fields if there are none
        if (document.getElementById('optionsListContainer').children.length === 0) {
            for (let i = 0; i < 4; i++) {
                addOptionField();
            }
        }
    } else {
        optionsContainer.style.display = 'none';
    }
    
    // Show subs container for composite/group questions
    if (topicType === 'COMPOSITE') {
        subsContainer.style.display = 'block';
    } else {
        subsContainer.style.display = 'none';
    }
}

/**
 * Add a new option field to the options container
 */
function addOptionField() {
    const optionsListContainer = document.getElementById('optionsListContainer');
    const optionIndex = optionsListContainer.children.length;
    const optionLetter = String.fromCharCode(65 + optionIndex); // A, B, C, D, ...
    
    const optionDiv = document.createElement('div');
    optionDiv.className = 'input-group mb-2';
    optionDiv.innerHTML = `
        <span class="input-group-text">${optionLetter}</span>
        <input type="text" class="form-control option-input" placeholder="选项内容">
        <button type="button" class="btn btn-outline-danger" onclick="removeOptionField(this)">
            <i class="bi bi-x"></i>
        </button>
    `;
    
    optionsListContainer.appendChild(optionDiv);
    
    // Add animation effect
    optionDiv.style.opacity = '0';
    optionDiv.style.transform = 'translateY(10px)';
    optionDiv.style.transition = 'opacity 0.3s, transform 0.3s';
    
    // Trigger reflow to apply initial styles before changing them
    void optionDiv.offsetWidth;
    
    // Apply final styles
    optionDiv.style.opacity = '1';
    optionDiv.style.transform = 'translateY(0)';
}

/**
 * Remove an option field
 */
function removeOptionField(button) {
    const optionDiv = button.parentNode;
    const optionsListContainer = document.getElementById('optionsListContainer');
    
    // Don't remove if it's the last option
    if (optionsListContainer.children.length <= 1) {
        showToast('至少需要保留一个选项', 'warning');
        return;
    }
    
    optionsListContainer.removeChild(optionDiv);
    
    // Update option letters (A, B, C, ...) after removal
    const optionDivs = optionsListContainer.children;
    for (let i = 0; i < optionDivs.length; i++) {
        const optionLetter = String.fromCharCode(65 + i);
        optionDivs[i].querySelector('.input-group-text').textContent = optionLetter;
    }
}

/**
 * View topic details
 */
function viewTopic(id) {
    fetchTopic(id, true);
}

/**
 * Edit topic
 */
function editTopic(id) {
    fetchTopic(id, false);
}

/**
 * Fetch a single topic by ID
 */
function fetchTopic(id, viewOnly) {
    const token = localStorage.getItem('token');
    if (!token) {
        showToast('请先登录', 'error');
        return;
    }
    
    fetch(`/api/topics/${id}`, {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.json();
    })
    .then(data => {
        if (data.success && data.data) {
            populateTopicForm(data.data, viewOnly);
        } else {
            showToast(data.message || '获取题目详情失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error fetching topic:', error);
        showToast('获取题目详情失败: ' + error.message, 'error');
    });
}

/**
 * Populate the topic form with data
 */
function populateTopicForm(topic, viewOnly) {
    resetTopicForm();
    
    // Set form values
    document.getElementById('topicId').value = topic.id;
    document.getElementById('topicKnowId').value = topic.knowId || '';
    // Map backend type to modal select value if necessary
    // Backend sends: 'choice', 'multiple', 'judge', 'fill', 'short', 'subjective', 'group'
    // Modal expects: 'SINGLE_CHOICE', 'MULTIPLE_CHOICE', 'JUDGE', 'FILL_IN_BLANK', 'ESSAY', 'PROGRAMMING', 'COMPOSITE'
    let modalTopicType = topic.type || '';
    switch (topic.type) {
        case 'choice': modalTopicType = 'SINGLE_CHOICE'; break;
        case 'multiple': modalTopicType = 'MULTIPLE_CHOICE'; break;
        case 'judge': modalTopicType = 'JUDGE'; break;
        case 'fill': modalTopicType = 'FILL_IN_BLANK'; break;
        case 'short': case 'subjective': modalTopicType = 'ESSAY'; break; // Assuming short/subjective map to ESSAY
        case 'group': modalTopicType = 'COMPOSITE'; break;
        // 'PROGRAMMING' is in modal but not in DTO pattern, handle if needed
    }
    document.getElementById('topicTypeModal').value = modalTopicType;
    // Handle topicTitle and groupTopicPassagePreview visibility
    const topicTitleTextarea = document.getElementById('topicTitle');
    const groupTopicPassagePreviewDiv = document.getElementById('groupTopicPassagePreview');

    if (viewOnly && topic.type === 'group') {
        topicTitleTextarea.style.display = 'none';
        groupTopicPassagePreviewDiv.innerHTML = topic.title ? topic.title.replace(/\n/g, '<br>') : '没有提供主要内容。'; // Replace newlines with <br>
        groupTopicPassagePreviewDiv.style.display = 'block';
    } else {
        topicTitleTextarea.value = topic.title || '';
        topicTitleTextarea.style.display = 'block';
        groupTopicPassagePreviewDiv.style.display = 'none';
    }
    document.getElementById('topicAnswer').value = topic.answer || '';
    document.getElementById('topicParse').value = topic.parse || '';
    document.getElementById('topicScore').value = topic.score || 3;
    document.getElementById('topicDifficulty').value = topic.difficulty || '';
    document.getElementById('topicSort').value = topic.sort || 1;
    document.getElementById('topicSource').value = topic.source || '';
    
    // Handle options for choice questions (SINGLE_CHOICE, MULTIPLE_CHOICE)
    // Note: The DTO uses 'choice', 'multiple'. The modal uses 'SINGLE_CHOICE', 'MULTIPLE_CHOICE'. This might need alignment.
    // For now, assuming topic.type from backend matches DTO values like 'choice', 'multiple', 'group'.
    // The existing options in topicTypeModal are 'SINGLE_CHOICE', 'MULTIPLE_CHOICE', 'JUDGE', 'FILL_IN_BLANK', 'ESSAY', 'PROGRAMMING', 'COMPOSITE'.
    // We should map these or ensure consistency. For now, this part might not trigger correctly if backend sends 'choice'.
    if (topic.type === 'choice' || topic.type === 'multiple' || topic.type === 'SINGLE_CHOICE' || topic.type === 'MULTIPLE_CHOICE') { // Adjusted to check for DTO values too
        document.getElementById('optionsContainer').style.display = 'block';
        const optionsListContainer = document.getElementById('optionsListContainer');
        optionsListContainer.innerHTML = '';
        
        // Parse options string into array
        let options = [];
        if (topic.options) {
            try {
                options = JSON.parse(topic.options);
            } catch (e) {
                // If not valid JSON, try to split by newlines or other delimiters
                options = topic.options.split(/\n|\\n|;/).filter(opt => opt.trim());
            }
        }
        
        // Add option fields
        if (options.length > 0) {
            options.forEach((option, index) => {
                const optionDiv = document.createElement('div');
                optionDiv.className = 'input-group mb-2';
                const optionLetter = String.fromCharCode(65 + index);
                
                optionDiv.innerHTML = `
                    <span class="input-group-text">${optionLetter}</span>
                    <input type="text" class="form-control option-input" value="${option}" ${viewOnly ? 'disabled' : ''}>
                    ${viewOnly ? '' : `
                    <button type="button" class="btn btn-outline-danger" onclick="removeOptionField(this)">
                        <i class="bi bi-x"></i>
                    </button>
                    `}
                `;
                
                optionsListContainer.appendChild(optionDiv);
            });
        } else {
            // Add default empty options if none exist
            for (let i = 0; i < 4; i++) {
                addOptionField();
            }
        }
    }
    
    // Handle subs for group questions (type 'group')
    const subsContainerDiv = document.getElementById('subsContainer');
    const topicSubsTextarea = document.getElementById('topicSubs');
    const previewSubQuestionsContainerDiv = document.getElementById('previewSubQuestionsContainer');
    const previewSubQuestionsListDiv = document.getElementById('previewSubQuestionsList');

    if (topic.type === 'group') {
        subsContainerDiv.style.display = 'block';
        if (viewOnly) {
            topicSubsTextarea.style.display = 'none';
            document.querySelector('#subsContainer .edit-mode-only').style.display = 'none'; // Hide edit-mode helper text
            previewSubQuestionsContainerDiv.style.display = 'block';
            previewSubQuestionsListDiv.innerHTML = ''; // Clear previous sub-questions
            // TODO: Parse topic.subs and render sub-questions into previewSubQuestionsListDiv
            if (topic.subs) {
                try {
                    const subQuestions = JSON.parse(topic.subs);
                    if (Array.isArray(subQuestions) && subQuestions.length > 0) {
                        subQuestions.forEach((sub, index) => {
                            const subQuestionHtml = renderSubQuestionPreview(sub, index + 1);
                            previewSubQuestionsListDiv.insertAdjacentHTML('beforeend', subQuestionHtml);
                        });
                    } else {
                        previewSubQuestionsListDiv.innerHTML = '<p class="text-muted">此题目没有子题目或子题目格式不正确。</p>';
                    }
                } catch (e) {
                    console.error('Error parsing sub-questions JSON:', e);
                    previewSubQuestionsListDiv.innerHTML = '<p class="text-danger">无法解析子题目数据。</p>';
                }
            } else {
                previewSubQuestionsListDiv.innerHTML = '<p class="text-muted">没有提供子题目数据。</p>';
            }
        } else { // Edit mode for group topic
            topicSubsTextarea.value = topic.subs || '';
            topicSubsTextarea.style.display = 'block';
            document.querySelector('#subsContainer .edit-mode-only').style.display = 'block';
            previewSubQuestionsContainerDiv.style.display = 'none';
        }
    } else {
        document.getElementById('subsContainer').style.display = 'block';
        subsContainerDiv.style.display = 'none';
    }
    
    // Update UI based on view/edit mode
    isEditMode = !viewOnly;
    currentTopicId = topic.id;
    
    // Update modal title and button visibility
    const iconClass = viewOnly ? 'bi-eye' : 'bi-pencil-square';
    document.getElementById('topicModalLabel').innerHTML = `<i class="bi ${iconClass} me-2"></i>${viewOnly ? '查看题目' : '编辑题目'}`;
    document.getElementById('saveTopicButton').style.display = viewOnly ? 'none' : 'block';
    
    // Disable form fields in view mode
    const formElements = document.getElementById('topicForm').elements;
    for (let i = 0; i < formElements.length; i++) {
        formElements[i].disabled = viewOnly;
    }
    
    // Show the modal
    const topicModal = new bootstrap.Modal(document.getElementById('topicModal'));
    topicModal.show();
}

/**
 * Renders the HTML for a single sub-question preview.
 * @param {object} subQuestion - The sub-question object.
 * @param {number} index - The 1-based index of the sub-question.
 * @returns {string} HTML string for the sub-question.
 */
function renderSubQuestionPreview(subQuestion, index) {
    let optionsHtml = '';
    if (subQuestion.options && Array.isArray(subQuestion.options)) {
        optionsHtml = '<ul class="list-unstyled ps-3">';
        subQuestion.options.forEach(opt => {
            optionsHtml += `<li><strong>${opt.key}:</strong> ${opt.name}</li>`;
        });
        optionsHtml += '</ul>';
    }

    // Sanitize content before injecting - basic example, consider a proper sanitizer if content can be malicious
    const escapeHtml = (unsafe) => {
        return unsafe
             .replace(/&/g, "&amp;")
             .replace(/</g, "&lt;")
             .replace(/>/g, "&gt;")
             .replace(/"/g, "&quot;")
             .replace(/'/g, "&#039;");
    };

    const title = subQuestion.title ? escapeHtml(subQuestion.title) : '无标题';
    const answer = subQuestion.answer ? escapeHtml(subQuestion.answer) : '未提供';
    const parse = subQuestion.parse ? escapeHtml(subQuestion.parse).replace(/\n/g, '<br>') : '未提供'; // Allow newlines in parse
    const typeDisplay = getTopicTypeInfo(subQuestion.type.toLowerCase()).text || subQuestion.type; // Get Chinese type display

    return `
        <div class="list-group-item sub-question-preview-item mb-3 border rounded p-3">
            <h6 class="mb-2"><span class="badge bg-info me-2">${index}</span> (${typeDisplay}) ${title}</h6>
            ${optionsHtml ? `<div class="mb-2"><strong>选项:</strong>${optionsHtml}</div>` : ''}
            <p class="mb-1"><strong>答案:</strong> <span class="text-success fw-bold">${answer}</span></p>
            <div><strong>解析:</strong> <div class="p-2 bg-light border rounded mt-1">${parse}</div></div>
        </div>
    `;
}

/**
 * Save topic (create new or update existing)
 */
function saveTopic() {
    // Validate form
    const form = document.getElementById('topicForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    // Get form values
    const topicId = document.getElementById('topicId').value;
    const knowId = document.getElementById('topicKnowId').value;
    const type = document.getElementById('topicTypeModal').value;
    const title = document.getElementById('topicTitle').value;
    const answer = document.getElementById('topicAnswer').value;
    const parse = document.getElementById('topicParse').value;
    const score = document.getElementById('topicScore').value;
    const difficulty = document.getElementById('topicDifficulty').value;
    const sort = document.getElementById('topicSort').value;
    const source = document.getElementById('topicSource').value;
    
    // Process options for choice questions
    let options = null;
    if (type === 'SINGLE_CHOICE' || type === 'MULTIPLE_CHOICE') {
        const optionInputs = document.querySelectorAll('.option-input');
        const optionsArray = Array.from(optionInputs).map(input => input.value.trim());
        
        // Validate that all options have content
        if (optionsArray.some(opt => !opt)) {
            showToast('所有选项必须填写内容', 'warning');
            return;
        }
        
        options = JSON.stringify(optionsArray);
    }
    
    // Process subs for composite questions
    let subs = null;
    if (type === 'COMPOSITE') {
        subs = document.getElementById('topicSubs').value;
    }
    
    // Prepare topic data
    const topicData = {
        knowId: parseInt(knowId),
        type,
        title,
        options,
        answer,
        parse,
        score: parseFloat(score),
        difficulty: parseFloat(difficulty),
        sort: parseInt(sort),
        source
    };
    
    if (subs) {
        topicData.subs = subs;
    }
    
    // Add ID if in edit mode
    if (isEditMode && topicId) {
        topicData.id = parseInt(topicId);
    }
    
    // Send request to backend
    const token = localStorage.getItem('token');
    if (!token) {
        showToast('请先登录', 'error');
        return;
    }
    
    const url = isEditMode ? `/api/topics/${topicId}` : '/api/topics';
    const method = isEditMode ? 'PUT' : 'POST';
    
    fetch(url, {
        method,
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(topicData)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            showToast(isEditMode ? '题目更新成功' : '题目添加成功', 'success');
            
            // Close modal
            const topicModal = bootstrap.Modal.getInstance(document.getElementById('topicModal'));
            topicModal.hide();
            
            // Reload topics
            loadTopics();
        } else {
            showToast(data.message || (isEditMode ? '题目更新失败' : '题目添加失败'), 'error');
        }
    })
    .catch(error => {
        console.error('Error saving topic:', error);
        showToast((isEditMode ? '题目更新失败: ' : '题目添加失败: ') + error.message, 'error');
    });
}

/**
 * Delete a topic
 */
function deleteTopic(id) {
    // Create a Bootstrap confirmation modal instead of using the browser's confirm dialog
    const confirmModal = document.createElement('div');
    confirmModal.className = 'modal fade';
    confirmModal.id = 'deleteConfirmModal';
    confirmModal.setAttribute('tabindex', '-1');
    confirmModal.setAttribute('aria-hidden', 'true');
    confirmModal.innerHTML = `
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title"><i class="bi bi-exclamation-triangle me-2"></i>确认删除</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>您确定要删除这个题目吗？此操作不可恢复。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><i class="bi bi-x me-1"></i>取消</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn"><i class="bi bi-trash me-1"></i>删除</button>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(confirmModal);
    
    // Show the modal
    const modal = new bootstrap.Modal(confirmModal);
    modal.show();
    
    // Handle confirm button click
    document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
        modal.hide();
        // The modal removal is handled by the listener below, no need for a duplicate here.
        
        // Proceed with deletion
        proceedWithDeletion(id);
    });
    
    // Remove the modal from DOM when hidden, ensuring it only runs once
    confirmModal.addEventListener('hidden.bs.modal', function() {
        if (document.body.contains(confirmModal)) {
            document.body.removeChild(confirmModal);
        }
    }, { once: true });
}

/**
 * Proceed with topic deletion after confirmation
 */
function proceedWithDeletion(id) {
    // Show loading indicator
    showToast('正在删除题目...', 'info');
    
    const token = localStorage.getItem('token');
    if (!token) {
        showToast('请先登录', 'error');
        return;
    }
    
    fetch(`/api/topics/${id}`, {
        method: 'DELETE',
        headers: {
            'Authorization': `Bearer ${token}`
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            showToast('题目删除成功', 'success');
            loadTopics();
        } else {
            showToast(data.message || '题目删除失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error deleting topic:', error);
        showToast('题目删除失败: ' + error.message, 'error');
    });
}
