/**
 * 数学公式处理工具
 * 用于处理PDF下载时的数学公式渲染
 */

// 全局变量，存储已渲染的公式图片
const renderedFormulas = new Map();

/**
 * 初始化数学公式处理
 */
function initMathFormulaHandler() {
    console.log('初始化数学公式处理器');
    
    // 拦截PDF下载按钮点击事件
    interceptPdfDownloadButtons();
}

/**
 * 拦截PDF下载按钮点击事件
 */
function interceptPdfDownloadButtons() {
    // 查找所有PDF下载按钮
    const pdfButtons = document.querySelectorAll('a[href*="/api/papers/download/"], a[href*="/papers/download/"]');
    
    pdfButtons.forEach(button => {
        // 保存原始链接
        const originalHref = button.getAttribute('href');
        
        // 移除原始链接的href属性，防止直接点击
        button.removeAttribute('href');
        
        // 添加自定义点击事件
        button.addEventListener('click', function(event) {
            event.preventDefault();
            
            // 显示加载提示
            Swal.fire({
                title: '准备下载',
                text: '正在处理数学公式，请稍候...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            
            // 预处理数学公式
            preprocessMathFormulas().then(() => {
                // 处理完成后，执行原始下载操作
                window.location.href = originalHref;
                
                // 关闭加载提示
                Swal.close();
            }).catch(error => {
                console.error('处理数学公式时出错:', error);
                
                // 显示错误提示
                Swal.fire({
                    icon: 'error',
                    title: '处理失败',
                    text: '处理数学公式时出错，将尝试直接下载',
                    confirmButtonText: '继续下载'
                }).then(() => {
                    // 继续执行原始下载操作
                    window.location.href = originalHref;
                });
            });
        });
        
        // 添加自定义样式，使其看起来仍然像链接
        button.style.cursor = 'pointer';
        button.title = '下载PDF（包含数学公式）';
    });
    
    console.log(`已拦截 ${pdfButtons.length} 个PDF下载按钮`);
}

/**
 * 预处理数学公式
 * 将页面上的所有数学公式渲染为SVG，并发送到服务器
 */
async function preprocessMathFormulas() {
    return new Promise((resolve, reject) => {
        try {
            // 查找页面上的所有数学公式
            const formulas = findMathFormulas();
            
            if (formulas.length === 0) {
                console.log('页面上没有数学公式，无需预处理');
                resolve();
                return;
            }
            
            console.log(`找到 ${formulas.length} 个数学公式，开始预处理`);
            
            // 使用KaTeX渲染所有公式
            const renderPromises = formulas.map(formula => renderFormula(formula));
            
            // 等待所有公式渲染完成
            Promise.all(renderPromises)
                .then(() => {
                    console.log('所有数学公式预处理完成');
                    resolve();
                })
                .catch(error => {
                    console.error('渲染数学公式时出错:', error);
                    reject(error);
                });
        } catch (error) {
            console.error('预处理数学公式时出错:', error);
            reject(error);
        }
    });
}

/**
 * 查找页面上的所有数学公式
 */
function findMathFormulas() {
    const formulas = [];
    
    // 查找所有可能包含数学公式的元素
    const elements = document.querySelectorAll('.question-title, .option-item, .answer-content');
    
    elements.forEach(element => {
        const text = element.textContent;
        
        // 检查是否包含数学公式分隔符
        if (text.includes('$') || text.includes('\\(') || text.includes('\\[')) {
            formulas.push({
                element: element,
                text: text
            });
        }
    });
    
    return formulas;
}

/**
 * 渲染单个数学公式
 */
async function renderFormula(formula) {
    return new Promise((resolve, reject) => {
        try {
            // 检查是否已经渲染过
            if (renderedFormulas.has(formula.text)) {
                console.log('使用缓存的公式渲染结果');
                resolve(renderedFormulas.get(formula.text));
                return;
            }
            
            // 创建临时容器
            const container = document.createElement('div');
            container.style.visibility = 'hidden';
            container.style.position = 'absolute';
            container.style.left = '-9999px';
            document.body.appendChild(container);
            
            // 设置公式文本
            container.textContent = formula.text;
            
            // 使用KaTeX渲染公式
            renderKaTeX(container);
            
            // 等待渲染完成
            setTimeout(() => {
                // 获取渲染结果
                const renderedHtml = container.innerHTML;
                
                // 存储渲染结果
                renderedFormulas.set(formula.text, renderedHtml);
                
                // 清理临时容器
                document.body.removeChild(container);
                
                resolve(renderedHtml);
            }, 100);
        } catch (error) {
            console.error('渲染公式时出错:', error);
            reject(error);
        }
    });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', initMathFormulaHandler);
