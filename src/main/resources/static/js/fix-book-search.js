/**
 * Fix script for book-search.js issues
 * 
 * This script fixes:
 * 1. The "jQuery search function not found" error
 * 2. The tooltip display issues with "Unknown Book"
 * 3. The global error catch problems
 */

// Make sure the fix runs after DOM is loaded
$(document).ready(function() {
    console.log('Book search fix script loaded');
    
    // Fix: Ensure searchBooks function is globally available
    if (typeof window.searchBooks !== 'function') {
        window.searchBooks = function(keyword) {
            console.log('Using fixed searchBooks function with keyword:', keyword);
            
            // Show loading state
            $('#bookSearchResults').html('');
            $('#bookSearchMessage').addClass('d-none');
            $('#bookSearchLoading').removeClass('d-none');
            
            // Remove any existing tooltips
            $('.book-tooltip').remove();
            
            // Build URL
            const searchUrl = `/api/books/search?keyword=${encodeURIComponent(keyword)}`;
            
            // Get auth token
            const token = getAuthToken();
            
            // Send API request
            $.ajax({
                url: searchUrl,
                method: 'GET',
                timeout: 10000, // 10 seconds
                beforeSend: function(xhr) {
                    // Add token to headers if available
                    if (token) {
                        xhr.setRequestHeader('Authorization', token);
                    }
                },
                success: function(response) {
                    $('#bookSearchLoading').addClass('d-none');
                    
                    // Process search results
                    const books = response.data || [];
                    
                    if (books.length === 0) {
                        $('#bookSearchMessage').text('未找到相关书籍').removeClass('d-none');
                        return;
                    }
                    
                    // Display results using fixed display function
                    displaySearchResultsFixed(books);
                },
                error: function(error) {
                    $('#bookSearchLoading').addClass('d-none');
                    console.error('搜索失败:', error.status, error.statusText);
                    
                    // Check if authentication issue
                    if (error.status === 401) {
                        $('#bookSearchMessage').text('您的登录已过期，请重新登录').removeClass('d-none');
                        return;
                    }
                    
                    $('#bookSearchMessage').text('搜索失败，请重试').removeClass('d-none');
                    
                    // Try fallback search
                    fallbackSearchFixed(keyword);
                }
            });
        };
        
        console.log('Fixed searchBooks function installed');
    }
    
    // Fix: Ensure getAuthToken function is available
    if (typeof window.getAuthToken !== 'function') {
        window.getAuthToken = function() {
            try {
                const token = localStorage.getItem('token');
                if (!token || token === 'undefined' || token === 'null') {
                    return null;
                }
                
                // Use normalizeToken if available
                if (typeof normalizeToken === 'function') {
                    return normalizeToken(token);
                } else {
                    // Simple normalize implementation
                    if (token.toLowerCase().startsWith('bearer ')) {
                        return token.substring(7);
                    }
                    return token;
                }
            } catch (error) {
                console.error('获取认证令牌失败:', error);
                return null;
            }
        };
        
        console.log('Fixed getAuthToken function installed');
    }
    
    // Fix: Ensure cleanupAllModals function is available
    if (typeof window.cleanupAllModals !== 'function') {
        window.cleanupAllModals = function() {
            console.log('执行修复版模态框清理');
            try {
                // Remove all backdrop overlays
                $('.modal-backdrop').remove();
                document.querySelectorAll('.modal-backdrop').forEach(el => {
                    try { el.parentNode.removeChild(el); } catch(e) {}
                });
                
                // Reset body styles
                $('body').removeClass('modal-open').css({
                    'overflow': '',
                    'padding-right': '',
                    'height': ''
                });
                document.body.classList.remove('modal-open');
                document.body.style.removeProperty('padding-right');
                document.body.style.removeProperty('overflow');
                
                // Close all modals
                $('.modal').removeClass('show').css('display', 'none');
                document.querySelectorAll('.modal').forEach(modal => {
                    modal.classList.remove('show');
                    modal.style.display = 'none';
                });
                
                // Remove all tooltips
                $('.tooltip-container, .book-tooltip, .tooltip-box').remove();
                
                console.log('修复版模态框清理完成');
            } catch (error) {
                console.error('清理模态框过程中发生错误:', error);
            }
            return true;
        };
        
        console.log('Fixed cleanupAllModals function installed');
    }
    
    // Fix: Ensure showToast function is available
    if (typeof window.showToast !== 'function') {
        window.showToast = function(message, type) {
            console.log(`[${type || 'info'}] ${message}`);
            
            // Try to use toast container if available
            const toastContainer = document.getElementById('toast-container');
            if (toastContainer) {
                const toast = document.createElement('div');
                toast.className = `toast ${type || 'info'}`;
                toast.innerHTML = `<div class="toast-content"><span class="toast-message">${message}</span></div>`;
                
                toastContainer.appendChild(toast);
                
                // Auto remove
                setTimeout(() => {
                    toast.classList.add('fade-out');
                    setTimeout(() => toast.remove(), 300);
                }, 3000);
            } else {
                // Fallback to alert
                alert(message);
            }
        };
        
        console.log('Fixed showToast function installed');
    }
    
    // Fix the search button click event
    $('#bookSearchButton').off('click').on('click', function() {
        const query = $('#bookSearchInput').val().trim();
        
        if (!query) {
            $('#bookSearchMessage').text('请输入搜索关键词').removeClass('d-none');
            return;
        }
        
        window.searchBooks(query);
    });
    
    // Fix the search input enter key event
    $('#bookSearchInput').off('keypress').on('keypress', function(e) {
        if (e.which === 13) {
            e.preventDefault();
            $('#bookSearchButton').click();
        }
    });
    
    // Fixed display search results function
    window.displaySearchResultsFixed = function(books) {
        const $results = $('#bookSearchResults');
        $results.html('');
        
        // Store books in global variable for tooltip access
        window.currentSearchBooks = books;
        
        // Process each book
        $.each(books, function(index, book) {
            // Extract chapter info
            let chapterInfo = null;
            let chapterNumber = null;
            let chapterType = '';
            
            if (book.title) {
                // Chinese chapter format: 第X章
                const cnMatch = book.title.match(/第(\d+)章/);
                if (cnMatch) {
                    chapterInfo = cnMatch[0];
                    chapterNumber = cnMatch[1];
                    chapterType = '章';
                } else {
                    // English chapter format: Chapter X
                    const enMatch = book.title.match(/Chapter\s+(\d+)/i);
                    if (enMatch) {
                        chapterInfo = enMatch[0];
                        chapterNumber = enMatch[1];
                        chapterType = 'Chapter';
                    }
                }
            }
            
            const hasChapter = chapterInfo !== null;
            const bookId = `book-${index}`;
            
            // Create row
            const $row = $(`
                <tr class="book-row" id="${bookId}">
                    <td class="book-title-cell">
                        <div class="book-title">${escapeHtml(book.title || '')}</div>
                        ${hasChapter ?
                            `<span class="chapter-badge">${chapterType} ${chapterNumber}</span>` :
                            ''}
                    </td>
                    <td>${escapeHtml(book.type || '-')}</td>
                    <td>
                        <button class="btn btn-sm btn-primary select-book">选择</button>
                    </td>
                </tr>
            `);
            
            // Add select button event
            $row.find('.select-book').on('click', function() {
                selectBookFixed(book);
            });
            
            // Add to results
            $results.append($row);
        });
        
        // Add direct tooltip event handlers
        $('.book-title-cell').off('mouseenter').on('mouseenter', function(event) {
            try {
                // Get book index from row ID
                const row = $(this).closest('tr');
                const bookId = row.attr('id');
                if (!bookId || !bookId.startsWith('book-')) return;
                
                const index = parseInt(bookId.replace('book-', ''), 10);
                if (isNaN(index) || !window.currentSearchBooks || !window.currentSearchBooks[index]) return;
                
                const book = window.currentSearchBooks[index];
                
                // First remove any existing tooltips to prevent duplicates
                $('.tooltip-container, .book-tooltip, .tooltip-box').remove();
                
                // Pass the event to the tooltip function
                showFixedTooltip(this, book, event);
            } catch (e) {
                console.error('显示提示框错误:', e);
            }
        });
        
        $('.book-title-cell').off('mouseleave').on('mouseleave', function() {
            setTimeout(function() {
                if (!$('.tooltip-container:hover').length) {
                    $('.tooltip-container').remove();
                }
            }, 100);
        });
        
        console.log(`加载了 ${books.length} 本书籍，设置了修复版悬停提示`);
    };
    
    // Fixed tooltip display
    window.showFixedTooltip = function(element, book, event) {
        if (!element || !book) return;
        
        // Remove existing tooltips
        $('.tooltip-container').remove();
        
        // Create tooltip content
        const tooltipContent = `
            <div class="book-tooltip-title">${escapeHtml(book.title || '未命名书籍')}</div>
            ${book.type ? `<div class="book-tooltip-type">类型: ${escapeHtml(book.type)}</div>` : ''}
            ${book.description
                ? `<div class="book-tooltip-desc">${escapeHtml(book.description)}</div>`
                : '<div class="book-tooltip-no-desc">暂无简介</div>'}
            ${book.url ? `<div class="book-tooltip-url">链接: ${escapeHtml(book.url)}</div>` : ''}
        `;
        
        // Create container
        const $tooltipContainer = $('<div class="tooltip-container"></div>');
        const $tooltip = $('<div class="book-tooltip"></div>').html(tooltipContent);
        $tooltipContainer.append($tooltip);
        $('body').append($tooltipContainer);
        
        // Position tooltip - safely get mouse position
        const elementRect = element.getBoundingClientRect();
        const tooltipWidth = 350;
        
        // Get mouse position safely
        let mouseX, mouseY;
        if (event && typeof event.clientX === 'number') {
            // Use provided event if available
            mouseX = event.clientX;
            mouseY = event.clientY;
        } else if (typeof window.getMousePosition === 'function') {
            // Try to use the global mouse position tracker
            const pos = window.getMousePosition();
            mouseX = pos.x;
            mouseY = pos.y;
        } else {
            // Fallback to element position
            mouseX = elementRect.right;
            mouseY = elementRect.top;
        }
        
        // Calculate position
        let left = mouseX + 15;
        let top = mouseY;
        
        // Check if tooltip would go off-screen to the right
        if (left + tooltipWidth > window.innerWidth) {
            left = mouseX - tooltipWidth - 15;
            
            // If tooltip would go off-screen to the left, position below element
            if (left < 0) {
                left = elementRect.left;
                top = elementRect.bottom + 10;
            }
        }
        
        // Set position
        $tooltipContainer.css({
            top: top + 'px',
            left: left + 'px',
            display: 'block',
            opacity: 1
        });
        
        // Add mouse leave event to hide tooltip
        $tooltipContainer.on('mouseleave', function() {
            $(this).fadeOut(100, function() {
                $(this).remove();
            });
        });
    };
    
    // Fixed fallback search
    window.fallbackSearchFixed = function(keyword) {
        console.log('使用修复版备用方法搜索书籍，关键词:', keyword);
        $('#bookSearchLoading').removeClass('d-none');
        
        // Get auth token
        const token = getAuthToken();
        
        // Send request
        $.ajax({
            url: '/api/books',
            method: 'GET',
            timeout: 10000,
            beforeSend: function(xhr) {
                if (token) {
                    xhr.setRequestHeader('Authorization', token);
                }
            },
            success: function(response) {
                $('#bookSearchLoading').addClass('d-none');
                console.log('获取所有书籍成功，开始前端过滤');
                
                const allBooks = response.data || [];
                if (allBooks.length === 0) {
                    $('#bookSearchMessage').text('系统中没有书籍数据').removeClass('d-none');
                    return;
                }
                
                // Filter books
                const lowercaseKeyword = keyword.toLowerCase();
                const filteredBooks = allBooks.filter(book =>
                    (book.title && book.title.toLowerCase().includes(lowercaseKeyword)) ||
                    (book.type && book.type.toLowerCase().includes(lowercaseKeyword)) ||
                    (book.description && book.description.toLowerCase().includes(lowercaseKeyword))
                );
                
                if (filteredBooks.length === 0) {
                    $('#bookSearchMessage').text('未找到相关书籍').removeClass('d-none');
                    return;
                }
                
                // Display results
                displaySearchResultsFixed(filteredBooks);
                showToast('已使用备用搜索方法', 'info');
            },
            error: function(error) {
                $('#bookSearchLoading').addClass('d-none');
                console.error('备用搜索失败:', error);
                
                // Check if authentication issue
                if (error.status === 401) {
                    $('#bookSearchMessage').text('您的登录已过期，请重新登录').removeClass('d-none');
                    return;
                }
                
                $('#bookSearchMessage').text('搜索失败，请重试').removeClass('d-none');
            }
        });
    };
    
    // Fixed select book function
    window.selectBookFixed = function(book) {
        try {
            if (!book) {
                console.error('无效的书籍对象');
                showToast('选择书籍失败：无效的书籍数据', 'error');
                return;
            }
            
            // Get context from modal
            const modalElement = document.getElementById('bookSearchModal');
            if (!modalElement) {
                console.error('找不到模态框元素');
                showToast('选择书籍失败：界面元素错误', 'error');
                return;
            }
            
            const context = modalElement.dataset.source === 'new-chat' ? 'new' : 'current';
            const bookTitle = book.title || '未命名';
            const bookUrl = book.url || '';
            
            console.log(`选择书籍: ${bookTitle}, URL: ${bookUrl}, 上下文: ${context}`);
            
            // Save book URL
            let saveSuccess = false;
            
            // Try using ChatState
            if (typeof window.ChatState !== 'undefined' && typeof window.ChatState.saveBookUrl === 'function') {
                try {
                    window.ChatState.saveBookUrl(bookUrl, context);
                    saveSuccess = true;
                    console.log('使用ChatState保存URL成功');
                } catch (e) {
                    console.error('使用ChatState保存URL失败:', e);
                }
            }
            
            // If ChatState fails, directly update DOM
            if (!saveSuccess) {
                try {
                    if (context === 'new') {
                        $('#newBookUrl').val(bookUrl);
                    } else {
                        $('#currentBookUrl').val(bookUrl);
                    }
                    saveSuccess = true;
                    console.log('使用jQuery更新输入框成功');
                } catch (e) {
                    console.error('使用jQuery更新输入框失败:', e);
                }
            }
            
            // Close modal
            closeBookSearchModalFixed();
            
            // Show success message
            showToast(`已选择书籍: ${bookTitle}`, 'success');
            
            return saveSuccess;
        } catch (error) {
            console.error('选择书籍过程中发生错误:', error);
            showToast('选择书籍失败，请重试', 'error');
            
            // Ensure modal is closed
            closeBookSearchModalFixed();
            
            return false;
        }
    };
    
    // Fixed close modal function
    window.closeBookSearchModalFixed = function() {
        try {
            const modalElement = document.getElementById('bookSearchModal');
            if (!modalElement) return;
            
            // Try to use Bootstrap API
            try {
                const instance = bootstrap.Modal.getInstance(modalElement);
                if (instance) {
                    instance.hide();
                } else {
                    $(modalElement).modal('hide');
                }
            } catch (e) {
                console.warn('使用Bootstrap API关闭模态框失败:', e);
                $(modalElement).modal('hide');
            }
            
            // Clean up modals
            if (typeof cleanupAllModals === 'function') {
                setTimeout(cleanupAllModals, 100);
            }
        } catch (error) {
            console.error('关闭书籍搜索模态框失败:', error);
        }
    };
    
    // Helper function to escape HTML
    window.escapeHtml = function(text) {
        if (!text) return '';
        return $('<div>').text(text).html();
    };
    
    console.log('Book search fix script initialization complete');
}); 