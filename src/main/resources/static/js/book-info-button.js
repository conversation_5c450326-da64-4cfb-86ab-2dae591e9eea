/**
 * Book information button script
 * 
 * This script adds a dedicated "查看书籍信息" button to view book details
 * as an alternative to hovering for tooltip display
 */

$(document).ready(function() {
    console.log('Book info button script loaded');
    
    // Enhanced display search results function
    const originalDisplaySearchResults = window.displaySearchResultsFixed;
    
    if (typeof originalDisplaySearchResults === 'function') {
        // Override with enhanced version that adds the info button
        window.displaySearchResultsFixed = function(books) {
            const $results = $('#bookSearchResults');
            $results.html('');
            
            // Store books in global variable for tooltip access
            window.currentSearchBooks = books;
            
            // Process each book
            $.each(books, function(index, book) {
                // Extract chapter info
                let chapterInfo = null;
                let chapterNumber = null;
                let chapterType = '';
                
                if (book.title) {
                    // Chinese chapter format: 第X章
                    const cnMatch = book.title.match(/第(\d+)章/);
                    if (cnMatch) {
                        chapterInfo = cnMatch[0];
                        chapterNumber = cnMatch[1];
                        chapterType = '章';
                    } else {
                        // English chapter format: Chapter X
                        const enMatch = book.title.match(/Chapter\s+(\d+)/i);
                        if (enMatch) {
                            chapterInfo = enMatch[0];
                            chapterNumber = enMatch[1];
                            chapterType = 'Chapter';
                        }
                    }
                }
                
                const hasChapter = chapterInfo !== null;
                const bookId = `book-${index}`;
                
                // Create row with additional info button
                const $row = $(`
                    <tr class="book-row" id="${bookId}">
                        <td class="book-title-cell">
                            <div class="book-title">${escapeHtml(book.title || '')}</div>
                            ${hasChapter ?
                                `<span class="chapter-badge">${chapterType} ${chapterNumber}</span>` :
                                ''}
                        </td>
                        <td>${escapeHtml(book.type || '-')}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-primary select-book">选择</button>
                                <button class="btn btn-outline-info view-book-info">查看信息</button>
                            </div>
                        </td>
                    </tr>
                `);
                
                // Add select button event
                $row.find('.select-book').on('click', function() {
                    selectBookFixed(book);
                });
                
                // Add view info button event
                $row.find('.view-book-info').on('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    showBookInfoModal(book);
                });
                
                // Add to results
                $results.append($row);
            });
            
            // Add direct tooltip event handlers for hover functionality
            $('.book-title-cell').off('mouseenter').on('mouseenter', function(event) {
                try {
                    // Get book index from row ID
                    const row = $(this).closest('tr');
                    const bookId = row.attr('id');
                    if (!bookId || !bookId.startsWith('book-')) return;
                    
                    const index = parseInt(bookId.replace('book-', ''), 10);
                    if (isNaN(index) || !window.currentSearchBooks || !window.currentSearchBooks[index]) return;
                    
                    const book = window.currentSearchBooks[index];
                    
                    // First remove any existing tooltips to prevent duplicates
                    $('.tooltip-container, .book-tooltip, .tooltip-box').remove();
                    
                    // Pass the event to the tooltip function
                    showFixedTooltip(this, book, event);
                } catch (e) {
                    console.error('显示提示框错误:', e);
                }
            });
            
            $('.book-title-cell').off('mouseleave').on('mouseleave', function() {
                setTimeout(function() {
                    if (!$('.tooltip-container:hover').length) {
                        $('.tooltip-container').remove();
                    }
                }, 100);
            });
            
            console.log(`加载了 ${books.length} 本书籍，设置了修复版悬停提示和信息按钮`);
        };
        
        console.log('Enhanced displaySearchResults with book info button installed');
    } else {
        console.warn('Could not find displaySearchResultsFixed function');
    }
    
    // Function to show book info in a modal
    window.showBookInfoModal = function(book) {
        if (!book) {
            console.error('无效的书籍信息');
            return;
        }
        
        console.log('显示书籍信息:', book.title);
        
        // Remove any existing modal to avoid duplication
        $('#bookInfoModal').remove();
        
        // Create modal HTML
        const modalHtml = `
            <div class="modal fade" id="bookInfoModal" tabindex="-1" aria-labelledby="bookInfoModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="bookInfoModalLabel">书籍信息</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="book-info-container">
                                <div class="book-info-title">
                                    <strong>标题：</strong> ${escapeHtml(book.title || '未命名书籍')}
                                </div>
                                <div class="book-info-type">
                                    <strong>类型：</strong> ${escapeHtml(book.type || '未指定')}
                                </div>
                                ${book.url ? 
                                    `<div class="book-info-url">
                                        <strong>链接：</strong> <a href="${escapeHtml(book.url)}" target="_blank">${escapeHtml(book.url)}</a>
                                    </div>` : ''}
                                <div class="book-info-description">
                                    <strong>描述：</strong>
                                    <div class="book-description-text">
                                        ${book.description ? escapeHtml(book.description) : '<em>暂无描述</em>'}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            <button type="button" class="btn btn-primary select-from-modal" data-book-id="${escapeHtml(book.id || '')}">
                                选择此书籍
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Add to body
        $('body').append(modalHtml);
        
        // Add custom CSS
        if (!$('#bookInfoStyles').length) {
            const styles = `
                <style id="bookInfoStyles">
                    .book-info-container { padding: 10px; }
                    .book-info-title { font-size: 1.2em; margin-bottom: 10px; word-break: break-word; }
                    .book-info-type, .book-info-url { margin-bottom: 8px; word-break: break-word; }
                    .book-description-text { 
                        background: #f8f9fa; 
                        padding: 10px; 
                        border-radius: 4px; 
                        margin-top: 5px;
                        max-height: 200px;
                        overflow-y: auto;
                        white-space: pre-line;
                        line-height: 1.5;
                    }
                </style>
            `;
            $('head').append(styles);
        }
        
        // Init modal
        const modal = new bootstrap.Modal(document.getElementById('bookInfoModal'));
        
        // Add click handler for select button
        $('#bookInfoModal .select-from-modal').on('click', function() {
            // Hide modal
            modal.hide();
            
            // Select book
            if (typeof window.selectBookFixed === 'function') {
                window.selectBookFixed(book);
            } else {
                console.error('选择书籍失败：找不到selectBookFixed函数');
                
                if (typeof showToast === 'function') {
                    showToast('选择书籍失败：系统错误', 'error');
                }
            }
        });
        
        // Show modal
        modal.show();
        
        // Add event for cleaning up
        $('#bookInfoModal').on('hidden.bs.modal', function() {
            // Clean up modal
            $(this).remove();
            
            // Run additional cleanup
            if (typeof cleanupAllModals === 'function') {
                setTimeout(cleanupAllModals, 100);
            }
        });
    };
    
    // Utility function to ensure escapeHtml is available
    if (typeof window.escapeHtml !== 'function') {
        window.escapeHtml = function(text) {
            if (!text) return '';
            return $('<div>').text(text).html();
        };
    }
    
    console.log('Book info button script initialization complete');
}); 