// Fix for missing or incorrectly implemented functions
// This script overwrites any broken implementations

// Fix showToast function
window.showToast = function(message, type) {
    console.log(`[${type || 'info'}] ${message}`);
    if (typeof window.alert === 'function') {
        window.alert(message);
    }
};

// Fix cleanupAllModals function if not defined
if (typeof window.cleanupAllModals !== 'function') {
    window.cleanupAllModals = function() {
        console.log('执行模态框清理');
        try {
            // 移除所有遮罩层
            $('.modal-backdrop').remove();
            
            // 重置body样式
            $('body').removeClass('modal-open').css({
                'overflow': '',
                'padding-right': ''
            });
            
            // 关闭所有模态框
            $('.modal').removeClass('show').css('display', 'none');
            
            // 移除所有tooltip
            $('.tooltip-container, .book-tooltip').remove();
        } catch (error) {
            console.error('清理模态框错误:', error);
        }
        return true;
    };
}

// Fix getAuthToken function if not defined
if (typeof window.getAuthToken !== 'function') {
    window.getAuthToken = function() {
        try {
            const token = localStorage.getItem('token');
            if (!token) return null;
            return token;
        } catch (error) {
            console.error('获取Token错误:', error);
            return null;
        }
    };
}

console.log('关键函数修复完成'); 