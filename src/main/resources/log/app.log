2025-06-26 00:26:36.607 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-26 00:26:36.653 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on shixiqiangdeMacBook-Pro.local with PID 4407 (/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes started by shixiqiang in /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys)
2025-06-26 00:26:36.655 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-06-26 00:26:36.656 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-06-26 00:26:37.228 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 00:26:37.229 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-26 00:26:37.371 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 138 ms. Found 5 JPA repository interfaces.
2025-06-26 00:26:37.382 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 00:26:37.383 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 00:26:37.392 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.BookRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 00:26:37.393 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatMessageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 00:26:37.393 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatSessionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 00:26:37.393 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperConfigRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 00:26:37.394 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 00:26:37.394 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 Redis repository interfaces.
2025-06-26 00:26:37.458 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 00:26:37.458 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 00:26:37.459 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 00:26:37.459 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperConfigRepository' and 'com.edu.maizi_edu_sys.repository.PaperConfigRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 00:26:37.459 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 00:26:37.756 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-06-26 00:26:37.762 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-06-26 00:26:37.762 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 00:26:37.762 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-26 00:26:37.837 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 00:26:37.837 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1161 ms
2025-06-26 00:26:37.993 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-26 00:26:38.032 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-26 00:26:38.128 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-26 00:26:38.175 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-26 00:26:38.435 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-26 00:26:38.448 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-06-26 00:26:38.961 [main] INFO  org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-26 00:26:38.966 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-26 00:26:38.974 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-06-26 00:26:38.974 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-06-26 00:26:38.974 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-06-26 00:26:38.990 [main] WARN  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'adminInterceptor': Unsatisfied dependency expressed through field 'authService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authServiceImpl' defined in file [/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes/com/edu/maizi_edu_sys/service/impl/AuthServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 1; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userServiceImpl' defined in file [/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes/com/edu/maizi_edu_sys/service/impl/UserServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 2; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'captchaServiceImpl' defined in file [/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes/com/edu/maizi_edu_sys/service/impl/CaptchaServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'org.springframework.data.redis.core.RedisTemplate<java.lang.String, java.lang.Object>' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-06-26 00:26:38.990 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-26 00:26:38.992 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-26 00:26:38.996 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-26 00:26:38.997 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-26 00:26:39.004 [main] INFO  org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-26 00:26:39.015 [main] ERROR org.springframework.boot.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl required a bean of type 'org.springframework.data.redis.core.RedisTemplate' that could not be found.

The injection point has the following annotations:
	- @org.springframework.beans.factory.annotation.Autowired(required=true)


Action:

Consider defining a bean of type 'org.springframework.data.redis.core.RedisTemplate' in your configuration.

2025-06-26 02:30:09.918 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-26 02:30:09.960 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on shixiqiangdeMacBook-Pro.local with PID 8895 (/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes started by shixiqiang in /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys)
2025-06-26 02:30:09.961 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-06-26 02:30:09.962 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-06-26 02:30:10.468 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 02:30:10.469 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-26 02:30:10.602 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 127 ms. Found 5 JPA repository interfaces.
2025-06-26 02:30:10.607 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 02:30:10.608 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 02:30:10.620 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.BookRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:30:10.621 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatMessageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:30:10.621 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatSessionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:30:10.622 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperConfigRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:30:10.622 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:30:10.622 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-06-26 02:30:10.679 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:30:10.680 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:30:10.680 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:30:10.680 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperConfigRepository' and 'com.edu.maizi_edu_sys.repository.PaperConfigRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:30:10.680 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:30:10.977 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-06-26 02:30:10.982 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-06-26 02:30:10.983 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 02:30:10.983 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-26 02:30:11.053 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 02:30:11.053 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1072 ms
2025-06-26 02:30:11.191 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-26 02:30:11.222 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-26 02:30:11.307 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-26 02:30:11.358 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-26 02:30:11.593 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-26 02:30:11.602 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-06-26 02:30:12.021 [main] INFO  org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-26 02:30:12.024 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-26 02:30:12.032 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-06-26 02:30:12.032 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-06-26 02:30:12.032 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-06-26 02:30:12.047 [main] WARN  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'adminInterceptor': Unsatisfied dependency expressed through field 'authService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authServiceImpl' defined in file [/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes/com/edu/maizi_edu_sys/service/impl/AuthServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 1; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userServiceImpl' defined in file [/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes/com/edu/maizi_edu_sys/service/impl/UserServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 2; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'captchaServiceImpl' defined in file [/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes/com/edu/maizi_edu_sys/service/impl/CaptchaServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'org.springframework.data.redis.core.RedisTemplate<java.lang.String, java.lang.Object>' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-06-26 02:30:12.047 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-26 02:30:12.049 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-26 02:30:12.053 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-26 02:30:12.054 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-26 02:30:12.061 [main] INFO  org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-26 02:30:12.072 [main] ERROR org.springframework.boot.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl required a bean of type 'org.springframework.data.redis.core.RedisTemplate' that could not be found.

The injection point has the following annotations:
	- @org.springframework.beans.factory.annotation.Autowired(required=true)


Action:

Consider defining a bean of type 'org.springframework.data.redis.core.RedisTemplate' in your configuration.

2025-06-26 02:34:52.649 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-26 02:34:52.695 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on shixiqiangdeMacBook-Pro.local with PID 9021 (/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes started by shixiqiang in /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys)
2025-06-26 02:34:52.696 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-06-26 02:34:52.697 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-06-26 02:34:53.230 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 02:34:53.231 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-26 02:34:53.376 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 141 ms. Found 5 JPA repository interfaces.
2025-06-26 02:34:53.383 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 02:34:53.384 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 02:34:53.394 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.BookRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:34:53.394 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatMessageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:34:53.395 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatSessionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:34:53.395 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperConfigRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:34:53.399 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:34:53.399 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-06-26 02:34:53.464 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:34:53.465 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:34:53.465 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:34:53.465 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperConfigRepository' and 'com.edu.maizi_edu_sys.repository.PaperConfigRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:34:53.465 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:34:53.760 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-06-26 02:34:53.766 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-06-26 02:34:53.766 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 02:34:53.766 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-26 02:34:53.841 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 02:34:53.842 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1123 ms
2025-06-26 02:34:53.989 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-26 02:34:54.017 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-26 02:34:54.104 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-26 02:34:54.166 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-26 02:34:54.404 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-26 02:34:54.413 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-06-26 02:34:54.862 [main] INFO  org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-26 02:34:54.867 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-26 02:34:54.874 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-06-26 02:34:54.874 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-06-26 02:34:54.874 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-06-26 02:34:55.022 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Configuring primary RedisTemplate with connection factory: LettuceConnectionFactory
2025-06-26 02:34:55.066 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Primary RedisTemplate initialized successfully
2025-06-26 02:34:55.259 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-06-26 02:34:55.260 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-06-26 02:34:55.260 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-06-26 02:34:55.260 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-06-26 02:34:55.261 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-06-26 02:34:55.261 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-06-26 02:34:55.261 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-06-26 02:34:55.261 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-06-26 02:34:55.362 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads, avatar=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/avatars
2025-06-26 02:34:55.368 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Configuring Integer RedisTemplate
2025-06-26 02:34:55.368 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-06-26 02:34:55.505 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-06-26 02:34:55.508 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-06-26 02:34:55.869 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-06-26 02:34:56.093 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Configuring Byte Array RedisTemplate
2025-06-26 02:34:56.093 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Byte Array RedisTemplate initialized successfully
2025-06-26 02:34:56.230 [main] WARN  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'adminViewController' method 
com.edu.maizi_edu_sys.controller.AdminViewController#userManagement()
to {GET [/admin/users]}: There is already 'adminDashboardController' bean method
com.edu.maizi_edu_sys.controller.AdminDashboardController#userManagement(Model) mapped.
2025-06-26 02:34:56.232 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-06-26 02:34:56.242 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-26 02:34:56.243 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-26 02:34:56.246 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-26 02:34:56.248 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-26 02:34:56.255 [main] INFO  org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-26 02:34:56.264 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'adminViewController' method 
com.edu.maizi_edu_sys.controller.AdminViewController#userManagement()
to {GET [/admin/users]}: There is already 'adminDashboardController' bean method
com.edu.maizi_edu_sys.controller.AdminDashboardController#userManagement(Model) mapped.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918) ~[spring-context-5.3.23.jar:5.3.23]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.23.jar:5.3.23]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145) ~[spring-boot-2.6.13.jar:2.6.13]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:745) ~[spring-boot-2.6.13.jar:2.6.13]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:420) ~[spring-boot-2.6.13.jar:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) ~[spring-boot-2.6.13.jar:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1317) ~[spring-boot-2.6.13.jar:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306) ~[spring-boot-2.6.13.jar:2.6.13]
	at com.edu.maizi_edu_sys.Application.main(Application.java:17) ~[classes/:?]
Caused by: java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'adminViewController' method 
com.edu.maizi_edu_sys.controller.AdminViewController#userManagement()
to {GET [/admin/users]}: There is already 'adminDashboardController' bean method
com.edu.maizi_edu_sys.controller.AdminDashboardController#userManagement(Model) mapped.
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.validateMethodMapping(AbstractHandlerMethodMapping.java:669) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.register(AbstractHandlerMethodMapping.java:635) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.registerHandlerMethod(AbstractHandlerMethodMapping.java:332) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:420) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:76) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lambda$detectHandlerMethods$2(AbstractHandlerMethodMapping.java:299) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684) ~[?:1.8.0_452]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:297) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:266) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:225) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:213) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:205) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.23.jar:5.3.23]
	... 16 more
2025-06-26 02:36:53.271 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-26 02:36:53.316 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on shixiqiangdeMacBook-Pro.local with PID 9128 (/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes started by shixiqiang in /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys)
2025-06-26 02:36:53.317 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-06-26 02:36:53.318 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-06-26 02:36:53.894 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 02:36:53.895 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-26 02:36:54.045 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 146 ms. Found 5 JPA repository interfaces.
2025-06-26 02:36:54.051 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 02:36:54.051 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 02:36:54.059 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.BookRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:36:54.060 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatMessageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:36:54.060 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatSessionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:36:54.061 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperConfigRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:36:54.061 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:36:54.061 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 Redis repository interfaces.
2025-06-26 02:36:54.124 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:36:54.124 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:36:54.124 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:36:54.124 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperConfigRepository' and 'com.edu.maizi_edu_sys.repository.PaperConfigRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:36:54.124 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:36:54.421 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-06-26 02:36:54.426 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-06-26 02:36:54.426 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 02:36:54.426 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-26 02:36:54.502 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 02:36:54.502 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1163 ms
2025-06-26 02:36:54.646 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-26 02:36:54.677 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-26 02:36:54.777 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-26 02:36:54.825 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-26 02:36:55.075 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-26 02:36:55.085 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-06-26 02:36:55.550 [main] INFO  org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-26 02:36:55.555 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-26 02:36:55.563 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-06-26 02:36:55.563 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-06-26 02:36:55.563 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-06-26 02:36:55.709 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Configuring primary RedisTemplate with connection factory: LettuceConnectionFactory
2025-06-26 02:36:55.755 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Primary RedisTemplate initialized successfully
2025-06-26 02:36:55.934 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-06-26 02:36:55.934 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-06-26 02:36:55.935 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-06-26 02:36:55.935 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-06-26 02:36:55.935 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-06-26 02:36:55.936 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-06-26 02:36:55.936 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-06-26 02:36:55.936 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-06-26 02:36:56.029 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads, avatar=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/avatars
2025-06-26 02:36:56.033 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Configuring Integer RedisTemplate
2025-06-26 02:36:56.033 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-06-26 02:36:56.163 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-06-26 02:36:56.165 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-06-26 02:36:56.507 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-06-26 02:36:56.705 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Configuring Byte Array RedisTemplate
2025-06-26 02:36:56.705 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Byte Array RedisTemplate initialized successfully
2025-06-26 02:36:56.831 [main] WARN  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'adminViewController' method 
com.edu.maizi_edu_sys.controller.AdminViewController#userManagement()
to {GET [/admin/users]}: There is already 'adminDashboardController' bean method
com.edu.maizi_edu_sys.controller.AdminDashboardController#userManagement(Model) mapped.
2025-06-26 02:36:56.834 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-06-26 02:36:56.842 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-26 02:36:56.843 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-26 02:36:56.846 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-26 02:36:56.848 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-26 02:36:56.854 [main] INFO  org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-26 02:36:56.863 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'adminViewController' method 
com.edu.maizi_edu_sys.controller.AdminViewController#userManagement()
to {GET [/admin/users]}: There is already 'adminDashboardController' bean method
com.edu.maizi_edu_sys.controller.AdminDashboardController#userManagement(Model) mapped.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918) ~[spring-context-5.3.23.jar:5.3.23]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.23.jar:5.3.23]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145) ~[spring-boot-2.6.13.jar:2.6.13]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:745) ~[spring-boot-2.6.13.jar:2.6.13]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:420) ~[spring-boot-2.6.13.jar:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) ~[spring-boot-2.6.13.jar:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1317) ~[spring-boot-2.6.13.jar:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306) ~[spring-boot-2.6.13.jar:2.6.13]
	at com.edu.maizi_edu_sys.Application.main(Application.java:17) ~[classes/:?]
Caused by: java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'adminViewController' method 
com.edu.maizi_edu_sys.controller.AdminViewController#userManagement()
to {GET [/admin/users]}: There is already 'adminDashboardController' bean method
com.edu.maizi_edu_sys.controller.AdminDashboardController#userManagement(Model) mapped.
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.validateMethodMapping(AbstractHandlerMethodMapping.java:669) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.register(AbstractHandlerMethodMapping.java:635) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.registerHandlerMethod(AbstractHandlerMethodMapping.java:332) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:420) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:76) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lambda$detectHandlerMethods$2(AbstractHandlerMethodMapping.java:299) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684) ~[?:1.8.0_452]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:297) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:266) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:225) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:213) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:205) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.23.jar:5.3.23]
	... 16 more
2025-06-26 02:38:44.141 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-26 02:38:44.183 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on shixiqiangdeMacBook-Pro.local with PID 9191 (/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes started by shixiqiang in /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys)
2025-06-26 02:38:44.184 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-06-26 02:38:44.185 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-06-26 02:38:44.684 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 02:38:44.684 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-26 02:38:44.811 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 123 ms. Found 5 JPA repository interfaces.
2025-06-26 02:38:44.818 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 02:38:44.819 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 02:38:44.830 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.BookRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:38:44.831 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatMessageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:38:44.831 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatSessionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:38:44.831 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperConfigRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:38:44.832 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:38:44.832 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-06-26 02:38:44.888 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:38:44.888 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:38:44.888 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:38:44.888 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperConfigRepository' and 'com.edu.maizi_edu_sys.repository.PaperConfigRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:38:44.888 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:38:45.185 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-06-26 02:38:45.191 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-06-26 02:38:45.191 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 02:38:45.191 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-26 02:38:45.262 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 02:38:45.262 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1058 ms
2025-06-26 02:38:45.397 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-26 02:38:45.429 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-26 02:38:45.524 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-26 02:38:45.581 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-26 02:38:45.801 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-26 02:38:45.810 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-06-26 02:38:46.243 [main] INFO  org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-26 02:38:46.247 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-26 02:38:46.254 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-06-26 02:38:46.254 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-06-26 02:38:46.254 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-06-26 02:38:46.388 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Configuring primary RedisTemplate with connection factory: LettuceConnectionFactory
2025-06-26 02:38:46.421 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Primary RedisTemplate initialized successfully
2025-06-26 02:38:46.589 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-06-26 02:38:46.589 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-06-26 02:38:46.589 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-06-26 02:38:46.590 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-06-26 02:38:46.590 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-06-26 02:38:46.590 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-06-26 02:38:46.590 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-06-26 02:38:46.590 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-06-26 02:38:46.677 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads, avatar=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/avatars
2025-06-26 02:38:46.681 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Configuring Integer RedisTemplate
2025-06-26 02:38:46.682 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-06-26 02:38:46.804 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-06-26 02:38:46.806 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-06-26 02:38:47.152 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-06-26 02:38:47.360 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Configuring Byte Array RedisTemplate
2025-06-26 02:38:47.360 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Byte Array RedisTemplate initialized successfully
2025-06-26 02:38:47.492 [main] WARN  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'topicController' method 
com.edu.maizi_edu_sys.controller.TopicController#deleteTopic(Integer)
to {DELETE [/api/topics/{id}]}: There is already 'topicBankController' bean method
com.edu.maizi_edu_sys.controller.TopicBankController#deleteTopic(Long) mapped.
2025-06-26 02:38:47.495 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-06-26 02:38:47.502 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-26 02:38:47.503 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-26 02:38:47.506 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-26 02:38:47.508 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-26 02:38:47.516 [main] INFO  org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-26 02:38:47.524 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'topicController' method 
com.edu.maizi_edu_sys.controller.TopicController#deleteTopic(Integer)
to {DELETE [/api/topics/{id}]}: There is already 'topicBankController' bean method
com.edu.maizi_edu_sys.controller.TopicBankController#deleteTopic(Long) mapped.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918) ~[spring-context-5.3.23.jar:5.3.23]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.23.jar:5.3.23]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145) ~[spring-boot-2.6.13.jar:2.6.13]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:745) ~[spring-boot-2.6.13.jar:2.6.13]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:420) ~[spring-boot-2.6.13.jar:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) ~[spring-boot-2.6.13.jar:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1317) ~[spring-boot-2.6.13.jar:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306) ~[spring-boot-2.6.13.jar:2.6.13]
	at com.edu.maizi_edu_sys.Application.main(Application.java:17) ~[classes/:?]
Caused by: java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'topicController' method 
com.edu.maizi_edu_sys.controller.TopicController#deleteTopic(Integer)
to {DELETE [/api/topics/{id}]}: There is already 'topicBankController' bean method
com.edu.maizi_edu_sys.controller.TopicBankController#deleteTopic(Long) mapped.
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.validateMethodMapping(AbstractHandlerMethodMapping.java:669) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.register(AbstractHandlerMethodMapping.java:635) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.registerHandlerMethod(AbstractHandlerMethodMapping.java:332) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:420) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:76) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lambda$detectHandlerMethods$2(AbstractHandlerMethodMapping.java:299) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684) ~[?:1.8.0_452]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:297) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:266) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:225) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:213) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:205) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.23.jar:5.3.23]
	... 16 more
2025-06-26 02:38:51.302 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-26 02:38:51.345 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on shixiqiangdeMacBook-Pro.local with PID 9198 (/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes started by shixiqiang in /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys)
2025-06-26 02:38:51.346 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-06-26 02:38:51.347 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-06-26 02:38:51.854 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 02:38:51.855 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-26 02:38:51.984 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 125 ms. Found 5 JPA repository interfaces.
2025-06-26 02:38:51.992 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 02:38:51.993 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 02:38:52.001 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.BookRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:38:52.002 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatMessageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:38:52.002 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatSessionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:38:52.002 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperConfigRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:38:52.003 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:38:52.003 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 Redis repository interfaces.
2025-06-26 02:38:52.054 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:38:52.055 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:38:52.055 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:38:52.055 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperConfigRepository' and 'com.edu.maizi_edu_sys.repository.PaperConfigRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:38:52.055 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:38:52.343 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-06-26 02:38:52.348 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-06-26 02:38:52.348 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 02:38:52.348 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-26 02:38:52.418 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 02:38:52.418 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1052 ms
2025-06-26 02:38:52.552 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-26 02:38:52.582 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-26 02:38:52.677 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-26 02:38:52.731 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-26 02:38:52.957 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-26 02:38:52.966 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-06-26 02:38:53.373 [main] INFO  org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-26 02:38:53.377 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-26 02:38:53.385 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-06-26 02:38:53.385 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-06-26 02:38:53.385 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-06-26 02:38:53.513 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Configuring primary RedisTemplate with connection factory: LettuceConnectionFactory
2025-06-26 02:38:53.550 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Primary RedisTemplate initialized successfully
2025-06-26 02:38:53.715 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-06-26 02:38:53.716 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-06-26 02:38:53.716 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-06-26 02:38:53.716 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-06-26 02:38:53.716 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-06-26 02:38:53.716 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-06-26 02:38:53.717 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-06-26 02:38:53.717 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-06-26 02:38:53.802 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads, avatar=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/avatars
2025-06-26 02:38:53.805 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Configuring Integer RedisTemplate
2025-06-26 02:38:53.806 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-06-26 02:38:53.940 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-06-26 02:38:53.942 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-06-26 02:38:54.270 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-06-26 02:38:54.479 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Configuring Byte Array RedisTemplate
2025-06-26 02:38:54.479 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Byte Array RedisTemplate initialized successfully
2025-06-26 02:38:54.611 [main] WARN  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'topicController' method 
com.edu.maizi_edu_sys.controller.TopicController#deleteTopic(Integer)
to {DELETE [/api/topics/{id}]}: There is already 'topicBankController' bean method
com.edu.maizi_edu_sys.controller.TopicBankController#deleteTopic(Long) mapped.
2025-06-26 02:38:54.613 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-06-26 02:38:54.621 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-26 02:38:54.621 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-26 02:38:54.625 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-26 02:38:54.627 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-26 02:38:54.634 [main] INFO  org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-26 02:38:54.643 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'topicController' method 
com.edu.maizi_edu_sys.controller.TopicController#deleteTopic(Integer)
to {DELETE [/api/topics/{id}]}: There is already 'topicBankController' bean method
com.edu.maizi_edu_sys.controller.TopicBankController#deleteTopic(Long) mapped.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918) ~[spring-context-5.3.23.jar:5.3.23]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.23.jar:5.3.23]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145) ~[spring-boot-2.6.13.jar:2.6.13]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:745) ~[spring-boot-2.6.13.jar:2.6.13]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:420) ~[spring-boot-2.6.13.jar:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) ~[spring-boot-2.6.13.jar:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1317) ~[spring-boot-2.6.13.jar:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306) ~[spring-boot-2.6.13.jar:2.6.13]
	at com.edu.maizi_edu_sys.Application.main(Application.java:17) ~[classes/:?]
Caused by: java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'topicController' method 
com.edu.maizi_edu_sys.controller.TopicController#deleteTopic(Integer)
to {DELETE [/api/topics/{id}]}: There is already 'topicBankController' bean method
com.edu.maizi_edu_sys.controller.TopicBankController#deleteTopic(Long) mapped.
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.validateMethodMapping(AbstractHandlerMethodMapping.java:669) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.register(AbstractHandlerMethodMapping.java:635) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.registerHandlerMethod(AbstractHandlerMethodMapping.java:332) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:420) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:76) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lambda$detectHandlerMethods$2(AbstractHandlerMethodMapping.java:299) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684) ~[?:1.8.0_452]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:297) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:266) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:225) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:213) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:205) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.23.jar:5.3.23]
	... 16 more
2025-06-26 02:41:51.255 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-26 02:41:51.299 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on shixiqiangdeMacBook-Pro.local with PID 9290 (/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes started by shixiqiang in /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys)
2025-06-26 02:41:51.300 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-06-26 02:41:51.301 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-06-26 02:41:51.825 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 02:41:51.826 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-26 02:41:51.962 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 131 ms. Found 5 JPA repository interfaces.
2025-06-26 02:41:51.971 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 02:41:51.971 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 02:41:51.980 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.BookRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:41:51.980 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatMessageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:41:51.981 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatSessionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:41:51.981 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperConfigRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:41:51.982 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:41:51.982 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 Redis repository interfaces.
2025-06-26 02:41:52.035 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:41:52.036 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:41:52.036 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:41:52.036 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperConfigRepository' and 'com.edu.maizi_edu_sys.repository.PaperConfigRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:41:52.036 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:41:52.324 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-06-26 02:41:52.329 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-06-26 02:41:52.329 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 02:41:52.329 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-26 02:41:52.401 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 02:41:52.402 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1081 ms
2025-06-26 02:41:52.534 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-26 02:41:52.567 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-26 02:41:52.653 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-26 02:41:52.692 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-26 02:41:52.896 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-26 02:41:52.906 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-06-26 02:41:53.316 [main] INFO  org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-26 02:41:53.321 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-26 02:41:53.328 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-06-26 02:41:53.329 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-06-26 02:41:53.329 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-06-26 02:41:53.459 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Configuring primary RedisTemplate with connection factory: LettuceConnectionFactory
2025-06-26 02:41:53.494 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Primary RedisTemplate initialized successfully
2025-06-26 02:41:53.659 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-06-26 02:41:53.659 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-06-26 02:41:53.659 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-06-26 02:41:53.660 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-06-26 02:41:53.660 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-06-26 02:41:53.660 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-06-26 02:41:53.660 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-06-26 02:41:53.661 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-06-26 02:41:53.746 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads, avatar=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/avatars
2025-06-26 02:41:53.749 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Configuring Integer RedisTemplate
2025-06-26 02:41:53.749 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-06-26 02:41:53.878 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-06-26 02:41:53.879 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-06-26 02:41:54.222 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-06-26 02:41:54.449 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Configuring Byte Array RedisTemplate
2025-06-26 02:41:54.449 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Byte Array RedisTemplate initialized successfully
2025-06-26 02:41:54.615 [main] WARN  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'topicController' method 
com.edu.maizi_edu_sys.controller.TopicController#listTopics(TopicQueryDTO)
to {GET [/api/topics]}: There is already 'topicBankController' bean method
com.edu.maizi_edu_sys.controller.TopicBankController#getTopicList(TopicQueryDTO) mapped.
2025-06-26 02:41:54.617 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-06-26 02:41:54.627 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-26 02:41:54.628 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-26 02:41:54.631 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-26 02:41:54.633 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-26 02:41:54.641 [main] INFO  org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-26 02:41:54.653 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'topicController' method 
com.edu.maizi_edu_sys.controller.TopicController#listTopics(TopicQueryDTO)
to {GET [/api/topics]}: There is already 'topicBankController' bean method
com.edu.maizi_edu_sys.controller.TopicBankController#getTopicList(TopicQueryDTO) mapped.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918) ~[spring-context-5.3.23.jar:5.3.23]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.23.jar:5.3.23]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145) ~[spring-boot-2.6.13.jar:2.6.13]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:745) ~[spring-boot-2.6.13.jar:2.6.13]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:420) ~[spring-boot-2.6.13.jar:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) ~[spring-boot-2.6.13.jar:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1317) ~[spring-boot-2.6.13.jar:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306) ~[spring-boot-2.6.13.jar:2.6.13]
	at com.edu.maizi_edu_sys.Application.main(Application.java:17) ~[classes/:?]
Caused by: java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'topicController' method 
com.edu.maizi_edu_sys.controller.TopicController#listTopics(TopicQueryDTO)
to {GET [/api/topics]}: There is already 'topicBankController' bean method
com.edu.maizi_edu_sys.controller.TopicBankController#getTopicList(TopicQueryDTO) mapped.
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.validateMethodMapping(AbstractHandlerMethodMapping.java:669) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.register(AbstractHandlerMethodMapping.java:635) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.registerHandlerMethod(AbstractHandlerMethodMapping.java:332) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:420) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:76) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lambda$detectHandlerMethods$2(AbstractHandlerMethodMapping.java:299) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684) ~[?:1.8.0_452]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:297) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:266) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:225) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:213) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:205) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.23.jar:5.3.23]
	... 16 more
2025-06-26 02:43:20.821 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-26 02:43:20.864 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on shixiqiangdeMacBook-Pro.local with PID 9610 (/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes started by shixiqiang in /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys)
2025-06-26 02:43:20.865 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-06-26 02:43:20.866 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-06-26 02:43:21.390 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 02:43:21.391 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-26 02:43:21.523 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 127 ms. Found 5 JPA repository interfaces.
2025-06-26 02:43:21.531 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 02:43:21.532 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 02:43:21.540 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.BookRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:43:21.541 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatMessageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:43:21.541 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatSessionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:43:21.542 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperConfigRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:43:21.542 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:43:21.542 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 Redis repository interfaces.
2025-06-26 02:43:21.599 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:43:21.599 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:43:21.599 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:43:21.599 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperConfigRepository' and 'com.edu.maizi_edu_sys.repository.PaperConfigRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:43:21.599 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:43:21.899 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-06-26 02:43:21.904 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-06-26 02:43:21.904 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 02:43:21.904 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-26 02:43:21.975 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 02:43:21.975 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1090 ms
2025-06-26 02:43:22.114 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-26 02:43:22.146 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-26 02:43:22.233 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-26 02:43:22.284 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-26 02:43:22.509 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-26 02:43:22.518 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-06-26 02:43:22.935 [main] INFO  org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-26 02:43:22.939 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-26 02:43:22.947 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-06-26 02:43:22.947 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-06-26 02:43:22.947 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-06-26 02:43:23.090 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Configuring primary RedisTemplate with connection factory: LettuceConnectionFactory
2025-06-26 02:43:23.127 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Primary RedisTemplate initialized successfully
2025-06-26 02:43:23.306 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-06-26 02:43:23.306 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-06-26 02:43:23.306 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-06-26 02:43:23.306 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-06-26 02:43:23.306 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-06-26 02:43:23.307 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-06-26 02:43:23.307 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-06-26 02:43:23.307 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-06-26 02:43:23.413 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads, avatar=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/avatars
2025-06-26 02:43:23.416 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Configuring Integer RedisTemplate
2025-06-26 02:43:23.416 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-06-26 02:43:23.555 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-06-26 02:43:23.558 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-06-26 02:43:23.919 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-06-26 02:43:24.146 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Configuring Byte Array RedisTemplate
2025-06-26 02:43:24.146 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Byte Array RedisTemplate initialized successfully
2025-06-26 02:43:24.285 [main] WARN  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'topicController' method 
com.edu.maizi_edu_sys.controller.TopicController#getTopicById(Integer)
to {GET [/api/topics/{id}]}: There is already 'topicBankController' bean method
com.edu.maizi_edu_sys.controller.TopicBankController#getTopicDetail(Long) mapped.
2025-06-26 02:43:24.287 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-06-26 02:43:24.295 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-26 02:43:24.296 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-26 02:43:24.300 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-26 02:43:24.302 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-26 02:43:24.309 [main] INFO  org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-26 02:43:24.318 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'topicController' method 
com.edu.maizi_edu_sys.controller.TopicController#getTopicById(Integer)
to {GET [/api/topics/{id}]}: There is already 'topicBankController' bean method
com.edu.maizi_edu_sys.controller.TopicBankController#getTopicDetail(Long) mapped.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918) ~[spring-context-5.3.23.jar:5.3.23]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583) ~[spring-context-5.3.23.jar:5.3.23]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145) ~[spring-boot-2.6.13.jar:2.6.13]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:745) ~[spring-boot-2.6.13.jar:2.6.13]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:420) ~[spring-boot-2.6.13.jar:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) ~[spring-boot-2.6.13.jar:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1317) ~[spring-boot-2.6.13.jar:2.6.13]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306) ~[spring-boot-2.6.13.jar:2.6.13]
	at com.edu.maizi_edu_sys.Application.main(Application.java:17) ~[classes/:?]
Caused by: java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'topicController' method 
com.edu.maizi_edu_sys.controller.TopicController#getTopicById(Integer)
to {GET [/api/topics/{id}]}: There is already 'topicBankController' bean method
com.edu.maizi_edu_sys.controller.TopicBankController#getTopicDetail(Long) mapped.
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.validateMethodMapping(AbstractHandlerMethodMapping.java:669) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.register(AbstractHandlerMethodMapping.java:635) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.registerHandlerMethod(AbstractHandlerMethodMapping.java:332) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:420) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:76) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lambda$detectHandlerMethods$2(AbstractHandlerMethodMapping.java:299) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684) ~[?:1.8.0_452]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:297) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:266) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:225) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:213) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:205) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863) ~[spring-beans-5.3.23.jar:5.3.23]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800) ~[spring-beans-5.3.23.jar:5.3.23]
	... 16 more
2025-06-26 02:48:08.675 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-26 02:48:08.718 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on shixiqiangdeMacBook-Pro.local with PID 11307 (/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes started by shixiqiang in /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys)
2025-06-26 02:48:08.719 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-06-26 02:48:08.719 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-06-26 02:48:09.246 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 02:48:09.247 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-26 02:48:09.379 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 127 ms. Found 5 JPA repository interfaces.
2025-06-26 02:48:09.387 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 02:48:09.388 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 02:48:09.397 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.BookRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:48:09.398 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatMessageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:48:09.398 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatSessionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:48:09.399 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperConfigRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:48:09.399 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 02:48:09.399 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Redis repository interfaces.
2025-06-26 02:48:09.456 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:48:09.456 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:48:09.457 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:48:09.457 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperConfigRepository' and 'com.edu.maizi_edu_sys.repository.PaperConfigRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:48:09.457 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 02:48:09.738 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-06-26 02:48:09.744 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-06-26 02:48:09.744 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 02:48:09.744 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-26 02:48:09.820 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 02:48:09.820 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1080 ms
2025-06-26 02:48:09.974 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-26 02:48:10.003 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-26 02:48:10.088 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-26 02:48:10.137 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-26 02:48:10.352 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-26 02:48:10.361 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-06-26 02:48:10.779 [main] INFO  org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-26 02:48:10.783 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-26 02:48:10.791 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-06-26 02:48:10.791 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-06-26 02:48:10.791 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-06-26 02:48:10.923 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Configuring primary RedisTemplate with connection factory: LettuceConnectionFactory
2025-06-26 02:48:10.960 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Primary RedisTemplate initialized successfully
2025-06-26 02:48:11.131 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-06-26 02:48:11.131 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-06-26 02:48:11.131 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-06-26 02:48:11.132 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-06-26 02:48:11.132 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-06-26 02:48:11.132 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-06-26 02:48:11.132 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-06-26 02:48:11.132 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-06-26 02:48:11.221 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads, avatar=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/avatars
2025-06-26 02:48:11.225 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Configuring Integer RedisTemplate
2025-06-26 02:48:11.225 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-06-26 02:48:11.365 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-06-26 02:48:11.367 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-06-26 02:48:11.710 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-06-26 02:48:11.917 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Configuring Byte Array RedisTemplate
2025-06-26 02:48:11.917 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Byte Array RedisTemplate initialized successfully
2025-06-26 02:48:12.054 [main] INFO  org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-26 02:48:12.066 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/
2025-06-26 02:48:12.239 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-26 02:48:12.261 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-06-26 02:48:12.275 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-06-26 02:48:12.276 [main] INFO  org.springframework.web.SimpLogging - Starting...
2025-06-26 02:48:12.277 [main] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@72b689f6]]
2025-06-26 02:48:12.277 [main] INFO  org.springframework.web.SimpLogging - Started.
2025-06-26 02:48:12.285 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 3.866 seconds (JVM running for 4.261)
2025-06-26 02:48:12.288 [main] INFO  com.edu.maizi_edu_sys.config.ApplicationStartupListener - 
╔══════════════════════════════════════════════════════════════════════════════╗
║                           麦子教育系统启动成功                                ║
╠══════════════════════════════════════════════════════════════════════════════╣
║  应用访问地址:                                                                ║
║    本地访问: http://localhost:8081                                                             ║
║    网络访问: http://127.0.0.1:8081                                              ║
║                                                                              ║
║  🔐 管理员登录页面:                                                           ║
║    本地访问: http://localhost:8081/admin/login                                   ║
║    网络访问: http://127.0.0.1:8081/admin/login                       ║
║                                                                              ║
║  🔐 管理员后台地址 (登录后访问):                                              ║
║    本地访问: http://localhost:8081/admin/topics/audit                        ║
║    网络访问: http://127.0.0.1:8081/admin/topics/audit                 ║
║                                                                              ║
║  📋 功能模块:                                                                 ║
║    • 智能出题: http://localhost:8081                /main/chat                                    ║
║    • 题目上传: http://localhost:8081           /topics/upload-topics                         ║
║    • 题库管理: http://localhost:8081                /topics/bank                                  ║
║    • 智能组卷: http://localhost:8081                /paper/generate                               ║
║    • 用户登录: http://localhost:8081                /auth/login                                   ║
║                                                                              ║
║  ⚙️  管理员功能:                                                              ║
║    • 题目审核: http://localhost:8081           /admin/topics/audit                           ║
║    • 用户管理: http://localhost:8081                /admin/users                                  ║
║    • 系统统计: http://localhost:8081                /admin/stats                                  ║
║    • 权限管理: http://localhost:8081           /admin/permissions                            ║
║                                                                              ║
║  📊 API文档: http://localhost:8081           /swagger-ui.html (如果启用)                      ║
║                                                                              ║
║  ⚠️  安全提示:                                                               ║
║    • 管理员后台入口已隐藏，普通用户无法在前端页面看到                          ║
║    • 只有 role=1 的管理员用户才能访问后台管理功能                            ║
║    • 建议定期更改管理员密码，确保系统安全                                    ║
╚══════════════════════════════════════════════════════════════════════════════╝
2025-06-26 02:48:12.288 [main] WARN  com.edu.maizi_edu_sys.config.ApplicationStartupListener - 
🔒 管理员后台安全提示:
   • 登录页面: http://localhost:8081/admin/login
   • 后台地址: http://localhost:8081/admin/topics/audit
   • 访问权限: 仅限 role=1 的管理员用户
   • 默认账号: admin / admin123 (请立即修改密码)
   • 安全建议: 请妥善保管管理员账号信息
   • 功能说明: 题目审核、用户管理、系统统计、权限管理
2025-06-26 02:48:15.830 [http-nio-8081-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-26 02:48:15.830 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-26 02:48:15.831 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-26 02:48:15.843 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/login, Token: exists
2025-06-26 02:48:16.173 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求生成验证码
2025-06-26 02:48:16.623 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/login, Token: exists
2025-06-26 02:48:16.774 [http-nio-8081-exec-4] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /favicon.ico
2025-06-26 02:48:16.994 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: 704531d5-65b8-46d3-9858-083bf34d3eba
2025-06-26 02:48:18.509 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求刷新验证码，会话ID: null
2025-06-26 02:48:18.517 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: 858cceaa-43a4-4ac2-8bca-59aff8744770
2025-06-26 02:48:19.341 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求刷新验证码，会话ID: null
2025-06-26 02:48:19.352 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: 5ccf17ac-f4d5-4c60-837b-429efbab6fee
2025-06-26 02:48:22.852 [http-nio-8081-exec-7] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /.well-known/appspecific/com.chrome.devtools.json
2025-06-26 02:48:29.499 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求刷新验证码，会话ID: null
2025-06-26 02:48:29.508 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: 2f686652-e936-4803-8137-de78ea9a1a1e
2025-06-26 02:48:30.409 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求刷新验证码，会话ID: null
2025-06-26 02:48:30.418 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: f367b41d-422c-4b3a-9e8c-17396fcb49b3
2025-06-26 02:48:31.075 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求刷新验证码，会话ID: null
2025-06-26 02:48:31.085 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: 964a8bc8-72de-4183-8b5a-b55cc9c70101
2025-06-26 02:48:31.358 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求刷新验证码，会话ID: null
2025-06-26 02:48:31.368 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: 3a6b216e-731b-4d22-a91a-ac5b7425ecb7
2025-06-26 02:48:31.597 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求刷新验证码，会话ID: null
2025-06-26 02:48:31.607 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: 239d0ce6-b3b9-46d7-b21c-a9dd089fa19f
2025-06-26 02:48:41.271 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 20.51231161269105%, pool size: 50
2025-06-26 02:48:51.621 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.HomeController - 访问首页
2025-06-26 02:48:51.818 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/info, Token: exists
2025-06-26 02:48:51.818 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:48:51.832 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/activities/page, Token: exists
2025-06-26 02:48:51.832 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:48:51.848 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 02:48:51.848 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 02:48:51.848 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:48:51.848 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:48:51.849 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:48:51.849 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:48:51.925 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 02:48:51.925 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 02:48:51.929 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/activities/page
2025-06-26 02:48:51.930 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/user/info
2025-06-26 02:48:51.936 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/info called (redirecting to /current)
2025-06-26 02:48:51.936 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-26 02:48:51.936 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...YI-A
2025-06-26 02:48:51.936 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:48:51.937 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:48:51.938 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-26 02:48:51.941 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-26 02:48:51.941 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-26 02:48:52.016 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/activities/statistics, Token: exists
2025-06-26 02:48:52.016 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:48:52.027 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 02:48:52.028 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:48:52.029 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:48:52.032 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 02:48:52.034 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/activities/statistics
2025-06-26 02:48:52.047 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-06-26 02:48:52.048 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.controller.AvatarsController - 请求头像文件: 20250508090939_f9f90b54.jpg
2025-06-26 02:48:52.049 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.controller.AvatarsController - 头像文件不存在: 20250508090939_f9f90b54.jpg，返回默认头像
2025-06-26 02:48:52.165 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.HomeController - 访问首页
2025-06-26 02:48:54.059 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-06-26 02:48:54.060 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:48:54.062 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 02:48:54.063 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:48:54.064 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:48:54.072 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 02:48:54.075 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/user/current
2025-06-26 02:48:54.076 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-26 02:48:54.076 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...YI-A
2025-06-26 02:48:54.076 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:48:54.077 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:48:54.081 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-26 02:48:54.085 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-26 02:48:54.085 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-26 02:48:54.155 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-06-26 02:49:08.668 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-06-26 02:49:08.668 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:08.669 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 02:49:08.669 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:08.670 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:08.673 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 02:49:08.674 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/user/current
2025-06-26 02:49:08.674 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-26 02:49:08.674 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...YI-A
2025-06-26 02:49:08.674 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:08.675 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:08.675 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-26 02:49:08.677 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-26 02:49:08.677 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-26 02:49:08.742 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-06-26 02:49:09.189 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-06-26 02:49:09.189 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:09.191 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 02:49:09.191 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:09.193 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:09.194 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='null', type=null, sort='createTime,desc', page=0, size=5
2025-06-26 02:49:09.197 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 02:49:09.199 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/knowledge/groups
2025-06-26 02:49:09.199 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 获取所有知识点分类
2025-06-26 02:49:11.269 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 26.721459644328622%, pool size: 50
2025-06-26 02:49:17.113 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-06-26 02:49:17.113 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:17.114 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 02:49:17.114 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:17.115 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:17.119 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 02:49:17.121 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/user/current
2025-06-26 02:49:17.121 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-26 02:49:17.121 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...YI-A
2025-06-26 02:49:17.121 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:17.122 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:17.122 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-26 02:49:17.125 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-26 02:49:17.125 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-26 02:49:17.201 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-06-26 02:49:22.233 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-06-26 02:49:22.233 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:22.234 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 02:49:22.234 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:22.235 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:22.238 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 02:49:22.239 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/user/current
2025-06-26 02:49:22.240 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-26 02:49:22.240 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...YI-A
2025-06-26 02:49:22.240 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:22.240 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:22.241 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-26 02:49:22.242 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-26 02:49:22.242 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-26 02:49:22.308 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-06-26 02:49:41.266 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 27.023117549137698%, pool size: 50
2025-06-26 02:49:43.962 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-06-26 02:49:43.963 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:43.964 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 02:49:43.965 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:43.967 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:43.972 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 02:49:43.973 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/user/current
2025-06-26 02:49:43.973 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-26 02:49:43.973 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...YI-A
2025-06-26 02:49:43.973 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:43.974 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:43.974 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-26 02:49:43.977 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-26 02:49:43.977 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-26 02:49:44.056 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-06-26 02:49:45.034 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-06-26 02:49:45.034 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:45.035 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 02:49:45.035 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:45.035 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:45.038 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 02:49:45.039 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/user/current
2025-06-26 02:49:45.040 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-26 02:49:45.040 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...YI-A
2025-06-26 02:49:45.040 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:45.040 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:45.041 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-26 02:49:45.043 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-26 02:49:45.043 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-26 02:49:45.044 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-06-26 02:49:45.044 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:45.045 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 02:49:45.045 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:45.045 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:45.048 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 02:49:45.049 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/user/current
2025-06-26 02:49:45.049 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-26 02:49:45.049 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...YI-A
2025-06-26 02:49:45.049 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:45.050 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:45.051 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-26 02:49:45.054 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-26 02:49:45.054 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-26 02:49:45.094 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/chat/history, Token: exists
2025-06-26 02:49:45.094 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:45.094 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /main/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-06-26 02:49:45.094 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Template path access: /main/avatars/20250508090939_f9f90b54.jpg
2025-06-26 02:49:45.094 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-06-26 02:49:45.094 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 02:49:45.094 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:45.095 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:45.098 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 02:49:45.099 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/chat/history
2025-06-26 02:49:45.099 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.controller.ChatController - Getting chat history for token: eyJhbGciOi...
2025-06-26 02:49:45.101 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:45.145 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.controller.ChatController - Chat history response status: 200, size: not null
2025-06-26 02:49:45.155 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/chat/32, Token: exists
2025-06-26 02:49:45.155 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:45.156 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 02:49:45.156 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:45.157 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:45.160 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 02:49:45.161 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/chat/32
2025-06-26 02:49:45.161 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.controller.ChatController - Getting chat detail: chatId=32
2025-06-26 02:49:45.161 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:45.180 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.controller.ChatController - Chat detail response: ApiResponse(code=200, message=success, data=ChatSession(id=32, userId=1920280447393230850, title=思想政治, knowId=218, bookUrl=https://www.yuque.com/shi-xi-qiang/kb/ofwnlgwerdm6u49i?singleDoc# 《普通高中教科书·思想政治必修1 中国特色社会主义_1-24》, createdAt=2025-05-14T16:55:25, updatedAt=2025-05-20T15:33:12, deleted=false))
2025-06-26 02:49:45.191 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/chat/messages/32, Token: exists
2025-06-26 02:49:45.191 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:45.191 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 02:49:45.191 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:45.192 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:45.195 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 02:49:45.196 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/chat/messages/32
2025-06-26 02:49:45.197 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.controller.ChatController - Getting chat messages for chatId=32 (using /messages/{chatId} endpoint)
2025-06-26 02:49:45.204 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.controller.ChatController - Chat messages response status: 200, data null? no
2025-06-26 02:49:49.985 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-06-26 02:49:49.986 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:49.987 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 02:49:49.988 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:49.989 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:49.994 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 02:49:49.995 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/user/current
2025-06-26 02:49:49.995 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-26 02:49:49.995 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...YI-A
2025-06-26 02:49:49.995 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:49.996 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:49.997 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-26 02:49:49.999 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-26 02:49:50.000 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-26 02:49:50.058 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-06-26 02:49:50.506 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-06-26 02:49:50.507 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:50.507 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='null', type=null, sort='createTime,desc', page=0, size=5
2025-06-26 02:49:50.509 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 02:49:50.509 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:50.511 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:50.515 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 02:49:50.518 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/knowledge/groups
2025-06-26 02:49:50.518 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 获取所有知识点分类
2025-06-26 02:49:51.783 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/points, Token: exists
2025-06-26 02:49:51.784 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:51.786 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 02:49:51.787 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:51.788 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:51.796 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 02:49:51.798 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/knowledge/points
2025-06-26 02:49:51.814 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - API响应示例: id=25, knowledgeId=195, name= 集合考点 
2025-06-26 02:49:51.814 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 原始Map数据: id=25, knowledgeId=195, name= 集合考点 
2025-06-26 02:49:54.674 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/points, Token: exists
2025-06-26 02:49:54.674 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:54.677 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 02:49:54.677 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:54.679 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:54.686 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 02:49:54.688 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/knowledge/points
2025-06-26 02:49:54.700 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - API响应示例: id=52, knowledgeId=185, name= 学科常识 - 数学常识 
2025-06-26 02:49:54.700 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 原始Map数据: id=52, knowledgeId=185, name= 学科常识 - 数学常识 
2025-06-26 02:49:55.135 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/points, Token: exists
2025-06-26 02:49:55.135 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:55.136 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 02:49:55.136 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:55.137 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:55.143 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 02:49:55.145 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/knowledge/points
2025-06-26 02:49:55.159 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - API响应示例: id=39, knowledgeId=15, name= 思想道德 - 思想道德素质 
2025-06-26 02:49:55.159 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 原始Map数据: id=39, knowledgeId=15, name= 思想道德 - 思想道德素质 
2025-06-26 02:49:58.814 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/points, Token: exists
2025-06-26 02:49:58.815 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:58.817 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 02:49:58.817 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:58.818 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:49:58.825 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 02:49:58.826 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/knowledge/points
2025-06-26 02:49:58.836 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - API响应示例: id=52, knowledgeId=185, name= 学科常识 - 数学常识 
2025-06-26 02:49:58.837 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 原始Map数据: id=52, knowledgeId=185, name= 学科常识 - 数学常识 
2025-06-26 02:50:09.043 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/points, Token: exists
2025-06-26 02:50:09.043 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:50:09.045 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 02:50:09.045 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:50:09.047 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:50:09.052 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 02:50:09.054 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/knowledge/points
2025-06-26 02:50:09.067 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - API响应示例: id=39, knowledgeId=15, name= 思想道德 - 思想道德素质 
2025-06-26 02:50:09.067 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 原始Map数据: id=39, knowledgeId=15, name= 思想道德 - 思想道德素质 
2025-06-26 02:50:11.262 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 31.229770325888033%, pool size: 50
2025-06-26 02:50:19.313 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=1, 题型配置={SINGLE_CHOICE=60}
2025-06-26 02:50:21.243 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=1, 题型配置={SINGLE_CHOICE=60}
2025-06-26 02:50:22.982 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=1, 题型配置={SINGLE_CHOICE=60, MULTIPLE_CHOICE=20}
2025-06-26 02:50:23.991 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=1, 题型配置={SINGLE_CHOICE=60, MULTIPLE_CHOICE=20}
2025-06-26 02:50:25.400 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=1, 题型配置={SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20}
2025-06-26 02:50:26.797 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=1, 题型配置={SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20}
2025-06-26 02:50:27.819 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=1, 题型配置={SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20}
2025-06-26 02:50:29.338 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.PaperController - 接收到试卷预览请求: 知识点配置数量=1, 题型配置={SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20}
2025-06-26 02:50:29.511 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.controller.PaperController - Received request to generate paper: test3
2025-06-26 02:50:29.537 [http-nio-8081-exec-4] ERROR com.edu.maizi_edu_sys.controller.PaperController - Error generating paper: 
com.edu.maizi_edu_sys.exception.PaperGenerationException: 参数配置无效: 请求验证过程中发生错误: 参数配置无效: 请求参数验证失败: 权重总和必须等于1.0，当前为0.820
	at com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine.generatePaper(PaperGenerationEngine.java:118) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl.generatePaper(PaperGenerationServiceImpl.java:164) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl$$FastClassBySpringCGLIB$$e109e2fc.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl$$EnhancerBySpringCGLIB$$7165d09.generatePaper(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.controller.PaperController.generatePaper(PaperController.java:130) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: com.edu.maizi_edu_sys.exception.PaperGenerationException: 参数配置无效: 请求参数验证失败: 权重总和必须等于1.0，当前为0.820
	at com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine.generatePaper(PaperGenerationEngine.java:103) ~[classes/:?]
	... 68 more
2025-06-26 02:50:30.314 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='null', type=null, sort='createTime,desc', page=0, size=5
2025-06-26 02:50:41.265 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 47.60482869750926%, pool size: 50
2025-06-26 02:51:10.162 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-06-26 02:51:10.162 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:51:10.163 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 02:51:10.164 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:51:10.165 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:51:10.170 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 02:51:10.172 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/user/current
2025-06-26 02:51:10.172 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-26 02:51:10.173 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...YI-A
2025-06-26 02:51:10.173 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:51:10.173 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:51:10.174 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-26 02:51:10.176 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-26 02:51:10.176 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-26 02:51:10.261 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-06-26 02:51:10.681 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/knowledge/groups, Token: exists
2025-06-26 02:51:10.681 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:51:10.682 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.PaperController - Fetching papers with search='null', type=null, sort='createTime,desc', page=0, size=5
2025-06-26 02:51:10.683 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 02:51:10.683 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:51:10.684 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:51:10.690 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 02:51:10.692 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/knowledge/groups
2025-06-26 02:51:10.692 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.KnowledgeController - 获取所有知识点分类
2025-06-26 02:51:11.264 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 48.6936906081946%, pool size: 50
2025-06-26 02:51:41.263 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 48.6936906081946%, pool size: 50
2025-06-26 02:52:11.262 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 48.6936906081946%, pool size: 50
2025-06-26 02:52:41.261 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 49.93280667540172%, pool size: 50
2025-06-26 02:52:59.384 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.PaperController - Received request to generate paper: 权重配置测试试卷
2025-06-26 02:52:59.391 [http-nio-8081-exec-1] ERROR com.edu.maizi_edu_sys.controller.PaperController - Error generating paper: 
com.edu.maizi_edu_sys.exception.PaperGenerationException: 参数配置无效: 请求验证过程中发生错误: 参数配置无效: 请求参数验证失败: 权重总和必须等于1.0，当前为0.820
	at com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine.generatePaper(PaperGenerationEngine.java:118) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl.generatePaper(PaperGenerationServiceImpl.java:164) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl$$FastClassBySpringCGLIB$$e109e2fc.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl$$EnhancerBySpringCGLIB$$7165d09.generatePaper(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.controller.PaperController.generatePaper(PaperController.java:130) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: com.edu.maizi_edu_sys.exception.PaperGenerationException: 参数配置无效: 请求参数验证失败: 权重总和必须等于1.0，当前为0.820
	at com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine.generatePaper(PaperGenerationEngine.java:103) ~[classes/:?]
	... 68 more
2025-06-26 02:53:11.259 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 49.93280667540172%, pool size: 50
2025-06-26 02:53:26.727 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-06-26 02:53:26.727 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:53:26.730 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 02:53:26.730 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:53:26.732 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:53:26.738 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 02:53:26.739 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/user/current
2025-06-26 02:53:26.740 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-26 02:53:26.740 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...YI-A
2025-06-26 02:53:26.740 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:53:26.741 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:53:26.742 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-26 02:53:26.744 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-26 02:53:26.744 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-26 02:53:26.830 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-06-26 02:53:31.349 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/topics/upload, Token: exists
2025-06-26 02:53:31.349 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:53:31.351 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 02:53:31.351 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:53:31.353 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:53:31.359 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 02:53:31.360 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/topics/upload
2025-06-26 02:53:31.373 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.TopicController - Received topic upload request with 2 topics
2025-06-26 02:53:31.373 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 02:53:31.373 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 02:53:31.374 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 02:53:31.378 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 02:53:31.428 [http-nio-8081-exec-6] ERROR com.edu.maizi_edu_sys.controller.TopicController - Error uploading topics: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.topic_audit' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/mapper/TopicAuditMapper.java (best guess)
### The error may involve com.edu.maizi_edu_sys.mapper.TopicAuditMapper.countUserTodaySubmissions-Inline
### The error occurred while setting parameters
### SQL: SELECT COUNT(*) FROM topic_audit WHERE user_id = ? AND DATE(submit_time) = CURDATE()
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.topic_audit' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.topic_audit' doesn't exist
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.topic_audit' doesn't exist
### The error may exist in com/edu/maizi_edu_sys/mapper/TopicAuditMapper.java (best guess)
### The error may involve com.edu.maizi_edu_sys.mapper.TopicAuditMapper.countUserTodaySubmissions-Inline
### The error occurred while setting parameters
### SQL: SELECT COUNT(*) FROM topic_audit WHERE user_id = ? AND DATE(submit_time) = CURDATE()
### Cause: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.topic_audit' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.topic_audit' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy156.selectOne(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:160) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy162.countUserTodaySubmissions(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl.getUserTodaySubmissionCount(TopicAuditServiceImpl.java:160) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl$$FastClassBySpringCGLIB$$7efd711e.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl$$EnhancerBySpringCGLIB$$9141ecaf.getUserTodaySubmissionCount(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.controller.TopicController.uploadTopics(TopicController.java:166) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.sql.SQLSyntaxErrorException: Table 'maizipapergendb.topic_audit' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.GeneratedMethodAccessor131.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy230.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor142.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy228.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.GeneratedMethodAccessor183.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	... 68 more
2025-06-26 02:53:41.258 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 51.43471062551461%, pool size: 50
2025-06-26 02:54:11.256 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 51.43471062551461%, pool size: 50
2025-06-26 02:54:41.256 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 51.43471062551461%, pool size: 50
2025-06-26 02:55:11.251 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 51.43471062551461%, pool size: 50
2025-06-26 02:55:41.253 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 51.43471062551461%, pool size: 50
2025-06-26 02:56:11.248 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 51.43471062551461%, pool size: 50
2025-06-26 02:56:41.251 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 51.68502072632288%, pool size: 50
2025-06-26 02:57:11.249 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 51.68502072632288%, pool size: 50
2025-06-26 02:57:14.953 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/topics/upload, Token: null
2025-06-26 02:57:14.953 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - API request without token: /api/topics/upload
2025-06-26 02:57:41.248 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 51.68502072632288%, pool size: 50
2025-06-26 02:58:11.247 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 51.68502072632288%, pool size: 50
2025-06-26 02:58:41.247 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 51.68502072632288%, pool size: 50
2025-06-26 02:58:55.904 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 不存在
2025-06-26 02:58:55.904 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 未能获取当前用户信息 - token验证失败或用户不存在
2025-06-26 02:59:05.563 [http-nio-8081-exec-1] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /topics/upload
2025-06-26 02:59:11.241 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 51.68502072632288%, pool size: 50
2025-06-26 02:59:41.244 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 51.68502072632288%, pool size: 50
2025-06-26 03:00:11.243 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 51.68502072632288%, pool size: 50
2025-06-26 03:00:41.240 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 51.68502072632288%, pool size: 50
2025-06-26 03:01:11.240 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 51.68502072632288%, pool size: 50
2025-06-26 03:01:41.235 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 51.68502072632288%, pool size: 50
2025-06-26 03:02:11.238 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 51.68502072632288%, pool size: 50
2025-06-26 03:02:41.237 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 51.68502072632288%, pool size: 50
2025-06-26 03:03:08.334 [http-nio-8081-exec-10] ERROR com.edu.maizi_edu_sys.exception.GlobalExceptionHandler - Unexpected error in /api/papers/download: Method [getTopicsFromPaperContent] was discovered in the .class file but cannot be resolved in the class object
java.lang.IllegalStateException: Method [getTopicsFromPaperContent] was discovered in the .class file but cannot be resolved in the class object
	at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.resolveExecutable(LocalVariableTableParameterNameDiscoverer.java:244) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.visitEnd(LocalVariableTableParameterNameDiscoverer.java:227) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.asm.ClassReader.readMethod(ClassReader.java:1519) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.asm.ClassReader.accept(ClassReader.java:745) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.asm.ClassReader.accept(ClassReader.java:425) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.core.LocalVariableTableParameterNameDiscoverer.inspectClass(LocalVariableTableParameterNameDiscoverer.java:109) ~[spring-core-5.3.23.jar:5.3.23]
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660) ~[?:1.8.0_452]
	at org.springframework.core.LocalVariableTableParameterNameDiscoverer.doGetParameterNames(LocalVariableTableParameterNameDiscoverer.java:84) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.core.LocalVariableTableParameterNameDiscoverer.getParameterNames(LocalVariableTableParameterNameDiscoverer.java:72) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.core.PrioritizedParameterNameDiscoverer.getParameterNames(PrioritizedParameterNameDiscoverer.java:55) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.core.MethodParameter.getParameterName(MethodParameter.java:712) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.updateNamedValueInfo(AbstractNamedValueMethodArgumentResolver.java:174) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.getNamedValueInfo(AbstractNamedValueMethodArgumentResolver.java:154) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:99) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:122) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:179) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:146) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.lang.NoSuchMethodException: com.edu.maizi_edu_sys.controller.PaperDownloadController.getTopicsFromPaperContent(java.lang.String)
	at java.lang.Class.getDeclaredMethod(Class.java:2158) ~[?:1.8.0_452]
	at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.resolveExecutable(LocalVariableTableParameterNameDiscoverer.java:241) ~[spring-core-5.3.23.jar:5.3.23]
	... 64 more
2025-06-26 03:03:11.235 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 51.68502072632288%, pool size: 50
2025-06-26 03:03:41.532 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 51.68502072632288%, pool size: 50
2025-06-26 03:04:02.121 [http-nio-8081-exec-9] ERROR com.edu.maizi_edu_sys.exception.GlobalExceptionHandler - Unexpected error in /api/papers/download: Method [parseTopicsFromPaperContent] was discovered in the .class file but cannot be resolved in the class object
java.lang.IllegalStateException: Method [parseTopicsFromPaperContent] was discovered in the .class file but cannot be resolved in the class object
	at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.resolveExecutable(LocalVariableTableParameterNameDiscoverer.java:244) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.visitEnd(LocalVariableTableParameterNameDiscoverer.java:227) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.asm.ClassReader.readMethod(ClassReader.java:1519) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.asm.ClassReader.accept(ClassReader.java:745) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.asm.ClassReader.accept(ClassReader.java:425) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.core.LocalVariableTableParameterNameDiscoverer.inspectClass(LocalVariableTableParameterNameDiscoverer.java:109) ~[spring-core-5.3.23.jar:5.3.23]
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660) ~[?:1.8.0_452]
	at org.springframework.core.LocalVariableTableParameterNameDiscoverer.doGetParameterNames(LocalVariableTableParameterNameDiscoverer.java:84) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.core.LocalVariableTableParameterNameDiscoverer.getParameterNames(LocalVariableTableParameterNameDiscoverer.java:72) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.core.PrioritizedParameterNameDiscoverer.getParameterNames(PrioritizedParameterNameDiscoverer.java:55) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.core.MethodParameter.getParameterName(MethodParameter.java:712) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.updateNamedValueInfo(AbstractNamedValueMethodArgumentResolver.java:174) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.getNamedValueInfo(AbstractNamedValueMethodArgumentResolver.java:154) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:99) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:122) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:179) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:146) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.lang.NoSuchMethodException: com.edu.maizi_edu_sys.controller.PaperDownloadController.parseTopicsFromPaperContent(java.lang.String)
	at java.lang.Class.getDeclaredMethod(Class.java:2158) ~[?:1.8.0_452]
	at org.springframework.core.LocalVariableTableParameterNameDiscoverer$LocalVariableTableVisitor.resolveExecutable(LocalVariableTableParameterNameDiscoverer.java:241) ~[spring-core-5.3.23.jar:5.3.23]
	... 64 more
2025-06-26 03:04:11.529 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 51.68502072632288%, pool size: 50
2025-06-26 03:04:41.533 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 51.68502072632288%, pool size: 50
2025-06-26 03:04:55.820 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopping...
2025-06-26 03:04:55.820 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@72b689f6]]
2025-06-26 03:04:55.820 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopped.
2025-06-26 03:04:55.833 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-06-26 03:04:55.950 [SpringApplicationShutdownHook] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-26 03:04:55.951 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-26 03:04:55.955 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-26 03:04:58.568 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-26 03:04:58.613 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on shixiqiangdeMacBook-Pro.local with PID 15023 (/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes started by shixiqiang in /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys)
2025-06-26 03:04:58.614 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-06-26 03:04:58.615 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-06-26 03:04:59.187 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 03:04:59.187 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-26 03:04:59.346 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 153 ms. Found 5 JPA repository interfaces.
2025-06-26 03:04:59.353 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 03:04:59.354 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 03:04:59.368 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.BookRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 03:04:59.368 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatMessageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 03:04:59.369 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatSessionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 03:04:59.369 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperConfigRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 03:04:59.370 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 03:04:59.370 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-06-26 03:04:59.432 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 03:04:59.432 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 03:04:59.432 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 03:04:59.432 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperConfigRepository' and 'com.edu.maizi_edu_sys.repository.PaperConfigRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 03:04:59.432 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 03:04:59.714 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-06-26 03:04:59.719 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-06-26 03:04:59.719 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 03:04:59.719 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-26 03:04:59.786 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 03:04:59.786 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1151 ms
2025-06-26 03:04:59.923 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-26 03:04:59.952 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-26 03:05:00.043 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-26 03:05:00.097 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-26 03:05:00.344 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-26 03:05:00.352 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-06-26 03:05:00.806 [main] INFO  org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-26 03:05:00.810 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-26 03:05:00.817 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-06-26 03:05:00.818 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-06-26 03:05:00.818 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-06-26 03:05:00.943 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Configuring primary RedisTemplate with connection factory: LettuceConnectionFactory
2025-06-26 03:05:00.980 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Primary RedisTemplate initialized successfully
2025-06-26 03:05:01.156 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-06-26 03:05:01.156 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-06-26 03:05:01.157 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-06-26 03:05:01.157 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-06-26 03:05:01.157 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-06-26 03:05:01.157 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-06-26 03:05:01.158 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-06-26 03:05:01.158 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-06-26 03:05:01.245 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads, avatar=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/avatars
2025-06-26 03:05:01.248 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Configuring Integer RedisTemplate
2025-06-26 03:05:01.248 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-06-26 03:05:01.377 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-06-26 03:05:01.380 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-06-26 03:05:01.730 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-06-26 03:05:01.936 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Configuring Byte Array RedisTemplate
2025-06-26 03:05:01.936 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Byte Array RedisTemplate initialized successfully
2025-06-26 03:05:02.062 [main] INFO  org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-26 03:05:02.071 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/
2025-06-26 03:05:02.241 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-26 03:05:02.263 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-06-26 03:05:02.279 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-06-26 03:05:02.280 [main] INFO  org.springframework.web.SimpLogging - Starting...
2025-06-26 03:05:02.280 [main] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2286dfcd]]
2025-06-26 03:05:02.280 [main] INFO  org.springframework.web.SimpLogging - Started.
2025-06-26 03:05:02.288 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 3.947 seconds (JVM running for 4.345)
2025-06-26 03:05:02.292 [main] INFO  com.edu.maizi_edu_sys.config.ApplicationStartupListener - 
╔══════════════════════════════════════════════════════════════════════════════╗
║                           麦子教育系统启动成功                                ║
╠══════════════════════════════════════════════════════════════════════════════╣
║  应用访问地址:                                                                ║
║    本地访问: http://localhost:8081                                                             ║
║    网络访问: http://127.0.0.1:8081                                              ║
║                                                                              ║
║  🔐 管理员登录页面:                                                           ║
║    本地访问: http://localhost:8081/admin/login                                   ║
║    网络访问: http://127.0.0.1:8081/admin/login                       ║
║                                                                              ║
║  🔐 管理员后台地址 (登录后访问):                                              ║
║    本地访问: http://localhost:8081/admin/topics/audit                        ║
║    网络访问: http://127.0.0.1:8081/admin/topics/audit                 ║
║                                                                              ║
║  📋 功能模块:                                                                 ║
║    • 智能出题: http://localhost:8081                /main/chat                                    ║
║    • 题目上传: http://localhost:8081           /topics/upload-topics                         ║
║    • 题库管理: http://localhost:8081                /topics/bank                                  ║
║    • 智能组卷: http://localhost:8081                /paper/generate                               ║
║    • 用户登录: http://localhost:8081                /auth/login                                   ║
║                                                                              ║
║  ⚙️  管理员功能:                                                              ║
║    • 题目审核: http://localhost:8081           /admin/topics/audit                           ║
║    • 用户管理: http://localhost:8081                /admin/users                                  ║
║    • 系统统计: http://localhost:8081                /admin/stats                                  ║
║    • 权限管理: http://localhost:8081           /admin/permissions                            ║
║                                                                              ║
║  📊 API文档: http://localhost:8081           /swagger-ui.html (如果启用)                      ║
║                                                                              ║
║  ⚠️  安全提示:                                                               ║
║    • 管理员后台入口已隐藏，普通用户无法在前端页面看到                          ║
║    • 只有 role=1 的管理员用户才能访问后台管理功能                            ║
║    • 建议定期更改管理员密码，确保系统安全                                    ║
╚══════════════════════════════════════════════════════════════════════════════╝
2025-06-26 03:05:02.293 [main] WARN  com.edu.maizi_edu_sys.config.ApplicationStartupListener - 
🔒 管理员后台安全提示:
   • 登录页面: http://localhost:8081/admin/login
   • 后台地址: http://localhost:8081/admin/topics/audit
   • 访问权限: 仅限 role=1 的管理员用户
   • 默认账号: admin / admin123 (请立即修改密码)
   • 安全建议: 请妥善保管管理员账号信息
   • 功能说明: 题目审核、用户管理、系统统计、权限管理
2025-06-26 03:05:05.299 [http-nio-8081-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-26 03:05:05.299 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-26 03:05:05.300 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-26 03:05:05.311 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/login, Token: exists
2025-06-26 03:05:05.642 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求生成验证码
2025-06-26 03:05:05.996 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/login, Token: exists
2025-06-26 03:05:06.472 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: 8b49bb91-727a-4365-9029-9c689d113610
2025-06-26 03:05:07.788 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求刷新验证码，会话ID: null
2025-06-26 03:05:07.795 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: be3c1e2c-5330-47b2-9204-7e359e8de1e4
2025-06-26 03:05:08.640 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求刷新验证码，会话ID: null
2025-06-26 03:05:08.651 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: d04ba3e9-772d-461f-894f-ce92f737d4ac
2025-06-26 03:05:10.292 [http-nio-8081-exec-6] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /.well-known/appspecific/com.chrome.devtools.json
2025-06-26 03:05:22.474 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.HomeController - 访问首页
2025-06-26 03:05:22.652 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/info, Token: exists
2025-06-26 03:05:22.652 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:05:22.662 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/activities/page, Token: exists
2025-06-26 03:05:22.663 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:05:22.677 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:05:22.677 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:05:22.677 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:05:22.677 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:05:22.678 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:05:22.678 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:05:22.757 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:05:22.757 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:05:22.761 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/user/info
2025-06-26 03:05:22.761 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/activities/page
2025-06-26 03:05:22.768 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/info called (redirecting to /current)
2025-06-26 03:05:22.768 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-26 03:05:22.769 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...YI-A
2025-06-26 03:05:22.769 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:05:22.770 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:05:22.771 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-26 03:05:22.773 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-26 03:05:22.773 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-26 03:05:22.839 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/activities/statistics, Token: exists
2025-06-26 03:05:22.839 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:05:22.840 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:05:22.840 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:05:22.841 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:05:22.845 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:05:22.847 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/activities/statistics
2025-06-26 03:05:22.861 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-06-26 03:05:22.862 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.controller.AvatarsController - 请求头像文件: 20250508090939_f9f90b54.jpg
2025-06-26 03:05:22.863 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.controller.AvatarsController - 头像文件不存在: 20250508090939_f9f90b54.jpg，返回默认头像
2025-06-26 03:05:22.978 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.HomeController - 访问首页
2025-06-26 03:05:24.540 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-06-26 03:05:24.540 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:05:24.541 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:05:24.541 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:05:24.542 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:05:24.546 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:05:24.547 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/user/current
2025-06-26 03:05:24.548 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-26 03:05:24.548 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...YI-A
2025-06-26 03:05:24.548 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:05:24.549 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:05:24.549 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-26 03:05:24.552 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-26 03:05:24.552 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-26 03:05:24.620 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-06-26 03:05:31.386 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 21.852348590719288%, pool size: 50
2025-06-26 03:05:32.507 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/topics/upload, Token: exists
2025-06-26 03:05:32.508 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:05:32.511 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:05:32.512 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:05:32.513 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:05:32.519 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:05:32.521 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/topics/upload
2025-06-26 03:05:32.547 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.TopicController - Received topic upload request with 2 topics
2025-06-26 03:05:32.547 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:05:32.547 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:05:32.548 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:05:32.552 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:05:32.602 [http-nio-8081-exec-9] ERROR com.edu.maizi_edu_sys.controller.TopicController - Error uploading topics: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'created_at' cannot be null
### The error may exist in com/edu/maizi_edu_sys/mapper/TopicAuditMapper.java (best guess)
### The error may involve com.edu.maizi_edu_sys.mapper.TopicAuditMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO topic_audit  ( user_id, know_id, type, title, options,  answer, parse, score, source, difficulty, tags, audit_status,    auto_approved, submit_time, created_at, updated_at )  VALUES  ( ?, ?, ?, ?, ?,  ?, ?, ?, ?, ?, ?, ?,    ?, ?, ?, ? )
### Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'created_at' cannot be null
; Column 'created_at' cannot be null; nested exception is java.sql.SQLIntegrityConstraintViolationException: Column 'created_at' cannot be null
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'created_at' cannot be null
### The error may exist in com/edu/maizi_edu_sys/mapper/TopicAuditMapper.java (best guess)
### The error may involve com.edu.maizi_edu_sys.mapper.TopicAuditMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO topic_audit  ( user_id, know_id, type, title, options,  answer, parse, score, source, difficulty, tags, audit_status,    auto_approved, submit_time, created_at, updated_at )  VALUES  ( ?, ?, ?, ?, ?,  ?, ?, ?, ?, ?, ?, ?,    ?, ?, ?, ? )
### Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'created_at' cannot be null
; Column 'created_at' cannot be null; nested exception is java.sql.SQLIntegrityConstraintViolationException: Column 'created_at' cannot be null
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:87) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:79) ~[spring-jdbc-5.3.23.jar:5.3.23]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.sun.proxy.$Proxy156.insert(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272) ~[mybatis-spring-2.0.6.jar:2.0.6]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-*******.jar:*******]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-*******.jar:*******]
	at com.sun.proxy.$Proxy162.insert(Unknown Source) ~[?:?]
	at com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl.submitTopicForAudit(TopicAuditServiceImpl.java:61) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl.submitTopicsForAudit(TopicAuditServiceImpl.java:69) ~[classes/:?]
	at com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl$$FastClassBySpringCGLIB$$7efd711e.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) ~[spring-aop-5.3.23.jar:5.3.23]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) ~[spring-aop-5.3.23.jar:5.3.23]
	at com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl$$EnhancerBySpringCGLIB$$c67dc7f.submitTopicsForAudit(<generated>) ~[classes/:?]
	at com.edu.maizi_edu_sys.controller.TopicController.uploadTopics(TopicController.java:176) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.6.13.jar:2.6.13]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'created_at' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:117) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371) ~[mysql-connector-java-8.0.28.jar:8.0.28]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-4.0.3.jar:?]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-4.0.3.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy230.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy229.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.7.jar:3.5.7]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-*******.jar:*******]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62) ~[mybatis-3.5.7.jar:3.5.7]
	at com.sun.proxy.$Proxy228.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194) ~[mybatis-3.5.7.jar:3.5.7]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181) ~[mybatis-3.5.7.jar:3.5.7]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427) ~[mybatis-spring-2.0.6.jar:2.0.6]
	... 75 more
2025-06-26 03:05:50.677 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 不存在
2025-06-26 03:05:50.677 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 未能获取当前用户信息 - token验证失败或用户不存在
2025-06-26 03:06:01.381 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 24.**************%, pool size: 50
2025-06-26 03:06:28.037 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 不存在
2025-06-26 03:06:28.037 [http-nio-8081-exec-3] WARN  com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 未能获取当前用户信息 - token验证失败或用户不存在
2025-06-26 03:06:31.383 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 24.**************%, pool size: 50
2025-06-26 03:07:01.384 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 24.**************%, pool size: 50
2025-06-26 03:07:31.385 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 24.**************%, pool size: 50
2025-06-26 03:08:01.384 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 24.**************%, pool size: 50
2025-06-26 03:08:10.143 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/topics/upload, Token: null
2025-06-26 03:08:10.143 [http-nio-8081-exec-2] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - API request without token: /api/topics/upload
2025-06-26 03:08:31.386 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 24.**************%, pool size: 50
2025-06-26 03:08:46.311 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/bank/topics/upload-stats, Token: null
2025-06-26 03:08:46.311 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - API request without token: /api/bank/topics/upload-stats
2025-06-26 03:09:01.386 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 24.**************%, pool size: 50
2025-06-26 03:09:31.387 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 24.**************%, pool size: 50
2025-06-26 03:10:01.387 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 24.**************%, pool size: 50
2025-06-26 03:10:31.387 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 24.**************%, pool size: 50
2025-06-26 03:11:00.362 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-06-26 03:11:00.363 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:11:00.364 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:11:00.365 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:11:00.366 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:11:00.374 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:11:00.378 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/user/current
2025-06-26 03:11:00.378 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-26 03:11:00.378 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...YI-A
2025-06-26 03:11:00.378 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:11:00.379 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:11:00.379 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-26 03:11:00.382 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-26 03:11:00.382 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-26 03:11:00.576 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /uploads/avatars/20250508090939_f9f90b54.jpg, Token: exists
2025-06-26 03:11:01.388 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 24.**************%, pool size: 50
2025-06-26 03:11:02.997 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/logout, Token: exists
2025-06-26 03:11:02.998 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:11:03.002 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:11:03.002 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:11:03.004 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:11:03.012 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:11:03.014 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/user/logout
2025-06-26 03:11:03.016 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.UserController - Logout attempt with token: eyJh...YI-A
2025-06-26 03:11:04.589 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求生成验证码
2025-06-26 03:11:04.595 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: 5595ea90-b19a-485d-b5c1-845c834e069c
2025-06-26 03:11:06.366 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求刷新验证码，会话ID: null
2025-06-26 03:11:06.376 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: 3673b104-b135-4f54-abf3-c1b99d109a6e
2025-06-26 03:11:19.998 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求生成验证码
2025-06-26 03:11:20.008 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: 164f15b9-2d65-44b4-b6a2-8e407b18493e
2025-06-26 03:11:24.170 [http-nio-8081-exec-3] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /.well-known/appspecific/com.chrome.devtools.json
2025-06-26 03:11:26.712 [http-nio-8081-exec-4] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /.well-known/appspecific/com.chrome.devtools.json
2025-06-26 03:11:26.877 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求生成验证码
2025-06-26 03:11:26.886 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: 1177ba4a-0edb-4b51-94d7-5d316bcb5771
2025-06-26 03:11:31.389 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 26.580289643386312%, pool size: 50
2025-06-26 03:11:31.636 [http-nio-8081-exec-1] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /.well-known/appspecific/com.chrome.devtools.json
2025-06-26 03:11:31.797 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求生成验证码
2025-06-26 03:11:31.806 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: f46b3fe9-f5f0-4390-a338-67a32ea39ebb
2025-06-26 03:11:48.815 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求生成验证码
2025-06-26 03:11:48.825 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: c9400c4c-ed2c-411e-b333-a9b77b10ba79
2025-06-26 03:11:55.055 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求刷新验证码，会话ID: null
2025-06-26 03:11:55.064 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: 11690394-d524-4a78-aa33-241fc45c32c2
2025-06-26 03:11:56.031 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求刷新验证码，会话ID: null
2025-06-26 03:11:56.042 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: 590bc07b-9162-4ff1-bdf4-1fbe75474956
2025-06-26 03:11:56.672 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求刷新验证码，会话ID: null
2025-06-26 03:11:56.685 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: b37630d0-6884-4742-8fca-e34179734dec
2025-06-26 03:12:01.385 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 27.81074458155139%, pool size: 50
2025-06-26 03:12:31.387 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 27.81074458155139%, pool size: 50
2025-06-26 03:13:01.389 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 27.81074458155139%, pool size: 50
2025-06-26 03:13:31.392 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 27.99716883692248%, pool size: 50
2025-06-26 03:14:01.391 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 27.99716883692248%, pool size: 50
2025-06-26 03:14:31.392 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 27.99716883692248%, pool size: 50
2025-06-26 03:15:01.392 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 27.99716883692248%, pool size: 50
2025-06-26 03:15:30.105 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/login, Token: null
2025-06-26 03:15:30.262 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求生成验证码
2025-06-26 03:15:30.270 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: a3ae2608-922d-43af-8f0a-d7b81e2b2c32
2025-06-26 03:15:30.598 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/login, Token: null
2025-06-26 03:15:31.392 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 28.183591119174302%, pool size: 50
2025-06-26 03:15:45.365 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求生成验证码
2025-06-26 03:15:45.376 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: 645ddfa5-5d31-4d48-9a0f-72d46215b1fd
2025-06-26 03:15:58.529 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.RequestUtil - Client IP detected: 0:0:0:0:0:0:0:1
2025-06-26 03:15:58.529 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.AdminController - 管理员登录尝试，IP: 0:0:0:0:0:0:0:1, 用户名: admin
2025-06-26 03:15:58.541 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 验证码验证成功，会话ID: a3ae2608-922d-43af-8f0a-d7b81e2b2c32
2025-06-26 03:15:58.614 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Generating token for user: admin with secret (first 5): 'F9A8C...'
2025-06-26 03:15:58.618 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.AdminController - 管理员登录成功，用户名: admin
2025-06-26 03:15:59.636 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/topics/audit, Token: null
2025-06-26 03:15:59.636 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员拦截器检查访问: /admin/topics/audit
2025-06-26 03:15:59.636 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 不存在
2025-06-26 03:15:59.636 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 未能获取当前用户信息 - token验证失败或用户不存在
2025-06-26 03:15:59.636 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AdminInterceptor - 未登录用户尝试访问管理后台: /admin/topics/audit
2025-06-26 03:15:59.938 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/topics/audit, Token: null
2025-06-26 03:15:59.938 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员拦截器检查访问: /admin/topics/audit
2025-06-26 03:15:59.938 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 不存在
2025-06-26 03:15:59.938 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 未能获取当前用户信息 - token验证失败或用户不存在
2025-06-26 03:15:59.938 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.config.AdminInterceptor - 未登录用户尝试访问管理后台: /admin/topics/audit
2025-06-26 03:16:01.389 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 28.668293788515285%, pool size: 50
2025-06-26 03:16:23.538 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.UserController - User registration attempt for username: admin
2025-06-26 03:16:31.394 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 28.668293788515285%, pool size: 50
2025-06-26 03:17:01.392 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 28.668293788515285%, pool size: 50
2025-06-26 03:17:31.394 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 28.668293788515285%, pool size: 50
2025-06-26 03:18:01.392 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 28.668293788515285%, pool size: 50
2025-06-26 03:18:31.422 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 28.668293788515285%, pool size: 50
2025-06-26 03:19:01.438 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 28.668293788515285%, pool size: 50
2025-06-26 03:19:31.440 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 28.668293788515285%, pool size: 50
2025-06-26 03:20:01.438 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 28.668293788515285%, pool size: 50
2025-06-26 03:20:31.443 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 28.668293788515285%, pool size: 50
2025-06-26 03:20:43.155 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopping...
2025-06-26 03:20:43.155 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2286dfcd]]
2025-06-26 03:20:43.155 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopped.
2025-06-26 03:20:43.169 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-06-26 03:20:43.287 [SpringApplicationShutdownHook] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-26 03:20:43.288 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-26 03:20:43.294 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-26 03:20:45.802 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-26 03:20:45.846 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on shixiqiangdeMacBook-Pro.local with PID 18913 (/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes started by shixiqiang in /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys)
2025-06-26 03:20:45.847 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-06-26 03:20:45.847 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-06-26 03:20:46.365 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 03:20:46.367 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-26 03:20:46.495 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 123 ms. Found 5 JPA repository interfaces.
2025-06-26 03:20:46.501 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 03:20:46.502 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 03:20:46.512 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.BookRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 03:20:46.512 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatMessageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 03:20:46.512 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatSessionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 03:20:46.513 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperConfigRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 03:20:46.513 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 03:20:46.513 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-06-26 03:20:46.574 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 03:20:46.574 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 03:20:46.574 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 03:20:46.574 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperConfigRepository' and 'com.edu.maizi_edu_sys.repository.PaperConfigRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 03:20:46.575 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 03:20:46.854 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-06-26 03:20:46.859 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-06-26 03:20:46.859 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 03:20:46.859 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-26 03:20:46.925 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 03:20:46.925 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1056 ms
2025-06-26 03:20:47.061 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-26 03:20:47.089 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-26 03:20:47.190 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-26 03:20:47.237 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-26 03:20:47.461 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-26 03:20:47.471 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-06-26 03:20:47.891 [main] INFO  org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-26 03:20:47.895 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-26 03:20:47.903 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-06-26 03:20:47.903 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-06-26 03:20:47.903 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-06-26 03:20:48.030 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Configuring primary RedisTemplate with connection factory: LettuceConnectionFactory
2025-06-26 03:20:48.068 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Primary RedisTemplate initialized successfully
2025-06-26 03:20:48.241 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-06-26 03:20:48.241 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-06-26 03:20:48.242 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-06-26 03:20:48.242 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-06-26 03:20:48.242 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-06-26 03:20:48.242 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-06-26 03:20:48.242 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-06-26 03:20:48.243 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-06-26 03:20:48.332 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads, avatar=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/avatars
2025-06-26 03:20:48.337 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Configuring Integer RedisTemplate
2025-06-26 03:20:48.337 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-06-26 03:20:48.468 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-06-26 03:20:48.470 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-06-26 03:20:48.800 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-06-26 03:20:49.011 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Configuring Byte Array RedisTemplate
2025-06-26 03:20:49.011 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Byte Array RedisTemplate initialized successfully
2025-06-26 03:20:49.138 [main] INFO  org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-26 03:20:49.146 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/
2025-06-26 03:20:49.302 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-26 03:20:49.329 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-06-26 03:20:49.344 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-06-26 03:20:49.345 [main] INFO  org.springframework.web.SimpLogging - Starting...
2025-06-26 03:20:49.345 [main] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5f63a6f2]]
2025-06-26 03:20:49.345 [main] INFO  org.springframework.web.SimpLogging - Started.
2025-06-26 03:20:49.353 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 3.765 seconds (JVM running for 4.155)
2025-06-26 03:20:49.357 [main] INFO  com.edu.maizi_edu_sys.config.ApplicationStartupListener - 
╔══════════════════════════════════════════════════════════════════════════════╗
║                           麦子教育系统启动成功                                ║
╠══════════════════════════════════════════════════════════════════════════════╣
║  应用访问地址:                                                                ║
║    本地访问: http://localhost:8081                                                             ║
║    网络访问: http://127.0.0.1:8081                                              ║
║                                                                              ║
║  🔐 管理员登录页面:                                                           ║
║    本地访问: http://localhost:8081/admin/login                                   ║
║    网络访问: http://127.0.0.1:8081/admin/login                       ║
║                                                                              ║
║  🔐 管理员后台地址 (登录后访问):                                              ║
║    本地访问: http://localhost:8081/admin/topics/audit                        ║
║    网络访问: http://127.0.0.1:8081/admin/topics/audit                 ║
║                                                                              ║
║  📋 功能模块:                                                                 ║
║    • 智能出题: http://localhost:8081                /main/chat                                    ║
║    • 题目上传: http://localhost:8081           /topics/upload-topics                         ║
║    • 题库管理: http://localhost:8081                /topics/bank                                  ║
║    • 智能组卷: http://localhost:8081                /paper/generate                               ║
║    • 用户登录: http://localhost:8081                /auth/login                                   ║
║                                                                              ║
║  ⚙️  管理员功能:                                                              ║
║    • 题目审核: http://localhost:8081           /admin/topics/audit                           ║
║    • 用户管理: http://localhost:8081                /admin/users                                  ║
║    • 系统统计: http://localhost:8081                /admin/stats                                  ║
║    • 权限管理: http://localhost:8081           /admin/permissions                            ║
║                                                                              ║
║  📊 API文档: http://localhost:8081           /swagger-ui.html (如果启用)                      ║
║                                                                              ║
║  ⚠️  安全提示:                                                               ║
║    • 管理员后台入口已隐藏，普通用户无法在前端页面看到                          ║
║    • 只有 role=1 的管理员用户才能访问后台管理功能                            ║
║    • 建议定期更改管理员密码，确保系统安全                                    ║
╚══════════════════════════════════════════════════════════════════════════════╝
2025-06-26 03:20:49.357 [main] WARN  com.edu.maizi_edu_sys.config.ApplicationStartupListener - 
🔒 管理员后台安全提示:
   • 登录页面: http://localhost:8081/admin/login
   • 后台地址: http://localhost:8081/admin/topics/audit
   • 访问权限: 仅限 role=1 的管理员用户
   • 默认账号: admin / admin123 (请立即修改密码)
   • 安全建议: 请妥善保管管理员账号信息
   • 功能说明: 题目审核、用户管理、系统统计、权限管理
2025-06-26 03:20:52.650 [http-nio-8081-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-26 03:20:52.650 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-26 03:20:52.651 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-06-26 03:20:52.663 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/login, Token: null
2025-06-26 03:20:52.991 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-06-26 03:20:52.991 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求生成验证码
2025-06-26 03:20:52.992 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:20:53.031 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:20:53.031 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:20:53.032 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:20:53.097 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:20:53.101 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/user/current
2025-06-26 03:20:53.108 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-26 03:20:53.108 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...IYAQ
2025-06-26 03:20:53.108 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:20:53.109 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:20:53.109 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-26 03:20:53.112 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-26 03:20:53.112 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-26 03:20:53.337 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/login, Token: null
2025-06-26 03:20:53.910 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: 52553ee3-9e10-4b7d-9243-d8f4c52636d1
2025-06-26 03:20:54.221 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/topics/audit, Token: null
2025-06-26 03:20:54.221 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员拦截器检查访问: /admin/topics/audit
2025-06-26 03:20:54.221 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 不存在
2025-06-26 03:20:54.221 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 未能获取当前用户信息 - token验证失败或用户不存在
2025-06-26 03:20:54.222 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AdminInterceptor - 未登录用户尝试访问管理后台: /admin/topics/audit
2025-06-26 03:20:54.487 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/topics/audit, Token: null
2025-06-26 03:20:54.487 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员拦截器检查访问: /admin/topics/audit
2025-06-26 03:20:54.487 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 不存在
2025-06-26 03:20:54.487 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 未能获取当前用户信息 - token验证失败或用户不存在
2025-06-26 03:20:54.487 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.config.AdminInterceptor - 未登录用户尝试访问管理后台: /admin/topics/audit
2025-06-26 03:21:03.951 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.RequestUtil - Client IP detected: 0:0:0:0:0:0:0:1
2025-06-26 03:21:03.951 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.AdminController - 管理员登录尝试，IP: 0:0:0:0:0:0:0:1, 用户名: admin
2025-06-26 03:21:03.962 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 验证码验证成功，会话ID: 52553ee3-9e10-4b7d-9243-d8f4c52636d1
2025-06-26 03:21:04.032 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Generating token for user: admin with secret (first 5): 'F9A8C...'
2025-06-26 03:21:04.036 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.AdminController - 管理员登录成功，用户名: admin
2025-06-26 03:21:05.054 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/topics/audit, Token: null
2025-06-26 03:21:05.054 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员拦截器检查访问: /admin/topics/audit
2025-06-26 03:21:05.054 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 不存在
2025-06-26 03:21:05.054 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 未能获取当前用户信息 - token验证失败或用户不存在
2025-06-26 03:21:05.055 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.config.AdminInterceptor - 未登录用户尝试访问管理后台: /admin/topics/audit
2025-06-26 03:21:05.378 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/topics/audit, Token: null
2025-06-26 03:21:05.379 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员拦截器检查访问: /admin/topics/audit
2025-06-26 03:21:05.379 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 不存在
2025-06-26 03:21:05.379 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 未能获取当前用户信息 - token验证失败或用户不存在
2025-06-26 03:21:05.379 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AdminInterceptor - 未登录用户尝试访问管理后台: /admin/topics/audit
2025-06-26 03:21:07.604 [http-nio-8081-exec-10] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /.well-known/appspecific/com.chrome.devtools.json
2025-06-26 03:21:10.194 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/topics/audit, Token: null
2025-06-26 03:21:10.196 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员拦截器检查访问: /admin/topics/audit
2025-06-26 03:21:10.196 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 不存在
2025-06-26 03:21:10.196 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 未能获取当前用户信息 - token验证失败或用户不存在
2025-06-26 03:21:10.196 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AdminInterceptor - 未登录用户尝试访问管理后台: /admin/topics/audit
2025-06-26 03:21:10.216 [http-nio-8081-exec-2] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /.well-known/appspecific/com.chrome.devtools.json
2025-06-26 03:21:11.113 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/topics/audit, Token: null
2025-06-26 03:21:11.114 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员拦截器检查访问: /admin/topics/audit
2025-06-26 03:21:11.114 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 不存在
2025-06-26 03:21:11.114 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 未能获取当前用户信息 - token验证失败或用户不存在
2025-06-26 03:21:11.114 [http-nio-8081-exec-4] WARN  com.edu.maizi_edu_sys.config.AdminInterceptor - 未登录用户尝试访问管理后台: /admin/topics/audit
2025-06-26 03:21:17.379 [http-nio-8081-exec-3] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /.well-known/appspecific/com.chrome.devtools.json
2025-06-26 03:21:17.427 [http-nio-8081-exec-5] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /.well-known/appspecific/com.chrome.devtools.json
2025-06-26 03:21:18.475 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 22.60327175325234%, pool size: 50
2025-06-26 03:21:19.234 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.RequestUtil - Client IP detected: 0:0:0:0:0:0:0:1
2025-06-26 03:21:19.234 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.AdminController - 管理员登录尝试，IP: 0:0:0:0:0:0:0:1, 用户名: admin
2025-06-26 03:21:19.236 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 验证码验证失败：验证码已过期或不存在，会话ID: 52553ee3-9e10-4b7d-9243-d8f4c52636d1
2025-06-26 03:21:19.236 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.service.impl.UserServiceImpl - 验证码验证失败，用户: admin, IP: 0:0:0:0:0:0:0:1
2025-06-26 03:21:19.236 [http-nio-8081-exec-6] WARN  com.edu.maizi_edu_sys.controller.AdminController - 管理员登录失败，用户名: admin, 原因: 验证码错误或已过期
2025-06-26 03:21:19.245 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求刷新验证码，会话ID: 52553ee3-9e10-4b7d-9243-d8f4c52636d1
2025-06-26 03:21:19.257 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: 91ff27db-120d-49c1-a1e4-6cf3847cb66b
2025-06-26 03:21:27.080 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.RequestUtil - Client IP detected: 0:0:0:0:0:0:0:1
2025-06-26 03:21:27.081 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.AdminController - 管理员登录尝试，IP: 0:0:0:0:0:0:0:1, 用户名: admin
2025-06-26 03:21:27.085 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 验证码验证成功，会话ID: 91ff27db-120d-49c1-a1e4-6cf3847cb66b
2025-06-26 03:21:27.166 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Generating token for user: admin with secret (first 5): 'F9A8C...'
2025-06-26 03:21:27.167 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.AdminController - 管理员登录成功，用户名: admin
2025-06-26 03:21:28.178 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/topics/audit, Token: null
2025-06-26 03:21:28.178 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员拦截器检查访问: /admin/topics/audit
2025-06-26 03:21:28.178 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 不存在
2025-06-26 03:21:28.178 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 未能获取当前用户信息 - token验证失败或用户不存在
2025-06-26 03:21:28.179 [http-nio-8081-exec-9] WARN  com.edu.maizi_edu_sys.config.AdminInterceptor - 未登录用户尝试访问管理后台: /admin/topics/audit
2025-06-26 03:21:28.201 [http-nio-8081-exec-10] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /.well-known/appspecific/com.chrome.devtools.json
2025-06-26 03:21:29.101 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/topics/audit, Token: null
2025-06-26 03:21:29.102 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员拦截器检查访问: /admin/topics/audit
2025-06-26 03:21:29.102 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 不存在
2025-06-26 03:21:29.102 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 未能获取当前用户信息 - token验证失败或用户不存在
2025-06-26 03:21:29.102 [http-nio-8081-exec-1] WARN  com.edu.maizi_edu_sys.config.AdminInterceptor - 未登录用户尝试访问管理后台: /admin/topics/audit
2025-06-26 03:21:48.472 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 24.00266067572102%, pool size: 50
2025-06-26 03:22:18.476 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 24.00266067572102%, pool size: 50
2025-06-26 03:22:48.474 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 24.00266067572102%, pool size: 50
2025-06-26 03:23:18.478 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 24.00266067572102%, pool size: 50
2025-06-26 03:23:22.267 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/login, Token: null
2025-06-26 03:23:22.437 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-06-26 03:23:22.437 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求生成验证码
2025-06-26 03:23:22.437 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:22.438 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:23:22.438 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:22.439 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:22.443 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: 865a07e3-a649-4be5-9292-bf00e4ac69e0
2025-06-26 03:23:22.445 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:23:22.447 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/user/current
2025-06-26 03:23:22.447 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-26 03:23:22.447 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...ohBA
2025-06-26 03:23:22.447 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:22.448 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:22.448 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-26 03:23:22.451 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-26 03:23:22.451 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-26 03:23:23.043 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/login, Token: exists
2025-06-26 03:23:23.644 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/topics/audit, Token: exists
2025-06-26 03:23:23.644 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员拦截器检查访问: /admin/topics/audit
2025-06-26 03:23:23.644 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:23:23.644 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:23.647 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:23.653 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:23:23.660 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.AdminSecurityServiceImpl - 管理员操作日志: 用户[admin(1920280447393230850)] 执行操作[ACCESS] 访问资源[/admin/topics/audit]
2025-06-26 03:23:23.665 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员访问后台: userId=1920280447393230850, username=admin, uri=/admin/topics/audit
2025-06-26 03:23:24.119 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求生成验证码
2025-06-26 03:23:24.129 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: 4ba99bc8-8880-48c1-b634-5ef76e666f4a
2025-06-26 03:23:30.501 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/login, Token: exists
2025-06-26 03:23:30.554 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求生成验证码
2025-06-26 03:23:30.554 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-06-26 03:23:30.555 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:30.557 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:23:30.557 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:30.559 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:30.564 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: 3d6c0f42-42e8-48ca-aef0-3bbf4eed038b
2025-06-26 03:23:30.567 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:23:30.570 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/user/current
2025-06-26 03:23:30.570 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-26 03:23:30.570 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...ohBA
2025-06-26 03:23:30.570 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:30.571 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:30.572 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-26 03:23:30.576 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-26 03:23:30.576 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-26 03:23:31.185 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/login, Token: exists
2025-06-26 03:23:31.619 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/topics/audit, Token: exists
2025-06-26 03:23:31.619 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员拦截器检查访问: /admin/topics/audit
2025-06-26 03:23:31.619 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:23:31.619 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:31.621 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:31.628 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:23:31.634 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.AdminSecurityServiceImpl - 管理员操作日志: 用户[admin(1920280447393230850)] 执行操作[ACCESS] 访问资源[/admin/topics/audit]
2025-06-26 03:23:31.635 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员访问后台: userId=1920280447393230850, username=admin, uri=/admin/topics/audit
2025-06-26 03:23:31.783 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求生成验证码
2025-06-26 03:23:31.788 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: 83b84056-0193-498d-9746-fab20caab8ae
2025-06-26 03:23:36.280 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/login, Token: exists
2025-06-26 03:23:36.337 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-06-26 03:23:36.338 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求生成验证码
2025-06-26 03:23:36.338 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:36.341 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:23:36.342 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:36.343 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:36.348 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: f3cf8a8f-59c0-4c5e-a73e-32e0eabda6a0
2025-06-26 03:23:36.349 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:23:36.350 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/user/current
2025-06-26 03:23:36.350 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-26 03:23:36.351 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...ohBA
2025-06-26 03:23:36.351 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:36.352 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:36.352 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-26 03:23:36.354 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-26 03:23:36.354 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-26 03:23:36.907 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/login, Token: exists
2025-06-26 03:23:37.401 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/topics/audit, Token: exists
2025-06-26 03:23:37.402 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员拦截器检查访问: /admin/topics/audit
2025-06-26 03:23:37.402 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:23:37.402 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:37.403 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:37.409 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:23:37.415 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.AdminSecurityServiceImpl - 管理员操作日志: 用户[admin(1920280447393230850)] 执行操作[ACCESS] 访问资源[/admin/topics/audit]
2025-06-26 03:23:37.417 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员访问后台: userId=1920280447393230850, username=admin, uri=/admin/topics/audit
2025-06-26 03:23:37.560 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求生成验证码
2025-06-26 03:23:37.570 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: 738fdbbd-13ea-4a8a-9147-b404060fbd81
2025-06-26 03:23:40.405 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopping...
2025-06-26 03:23:40.405 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5f63a6f2]]
2025-06-26 03:23:40.406 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopped.
2025-06-26 03:23:40.418 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-06-26 03:23:40.533 [SpringApplicationShutdownHook] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-26 03:23:40.535 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-26 03:23:40.540 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-26 03:23:41.995 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-26 03:23:42.051 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on shixiqiangdeMacBook-Pro.local with PID 19545 (/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes started by shixiqiang in /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys)
2025-06-26 03:23:42.053 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-06-26 03:23:42.053 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-06-26 03:23:42.653 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 03:23:42.654 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-26 03:23:42.796 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 137 ms. Found 5 JPA repository interfaces.
2025-06-26 03:23:42.806 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 03:23:42.807 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 03:23:42.819 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.BookRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 03:23:42.820 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatMessageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 03:23:42.820 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatSessionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 03:23:42.820 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperConfigRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 03:23:42.821 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 03:23:42.822 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-06-26 03:23:42.890 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 03:23:42.891 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 03:23:42.891 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 03:23:42.891 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperConfigRepository' and 'com.edu.maizi_edu_sys.repository.PaperConfigRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 03:23:42.891 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 03:23:43.165 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-06-26 03:23:43.169 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-06-26 03:23:43.170 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 03:23:43.170 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-26 03:23:43.249 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 03:23:43.250 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1172 ms
2025-06-26 03:23:43.384 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-26 03:23:43.413 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-26 03:23:43.503 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-26 03:23:43.558 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-26 03:23:43.724 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-26 03:23:43.732 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-06-26 03:23:44.226 [main] INFO  org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-26 03:23:44.230 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-26 03:23:44.237 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-06-26 03:23:44.237 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-06-26 03:23:44.237 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-06-26 03:23:44.363 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Configuring primary RedisTemplate with connection factory: LettuceConnectionFactory
2025-06-26 03:23:44.401 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Primary RedisTemplate initialized successfully
2025-06-26 03:23:44.568 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-06-26 03:23:44.568 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-06-26 03:23:44.568 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-06-26 03:23:44.569 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-06-26 03:23:44.569 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-06-26 03:23:44.569 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-06-26 03:23:44.569 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-06-26 03:23:44.569 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-06-26 03:23:44.649 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads, avatar=/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/avatars
2025-06-26 03:23:44.654 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Configuring Integer RedisTemplate
2025-06-26 03:23:44.655 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-06-26 03:23:44.777 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-06-26 03:23:44.779 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-06-26 03:23:45.112 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-06-26 03:23:45.344 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Configuring Byte Array RedisTemplate
2025-06-26 03:23:45.345 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Byte Array RedisTemplate initialized successfully
2025-06-26 03:23:45.490 [main] INFO  org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-26 03:23:45.501 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/././uploads/
2025-06-26 03:23:45.662 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-26 03:23:45.685 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-06-26 03:23:45.702 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-06-26 03:23:45.703 [main] INFO  org.springframework.web.SimpLogging - Starting...
2025-06-26 03:23:45.703 [main] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@67915454]]
2025-06-26 03:23:45.703 [main] INFO  org.springframework.web.SimpLogging - Started.
2025-06-26 03:23:45.715 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 3.959 seconds (JVM running for 4.373)
2025-06-26 03:23:45.719 [main] INFO  com.edu.maizi_edu_sys.config.ApplicationStartupListener - 
╔══════════════════════════════════════════════════════════════════════════════╗
║                           麦子教育系统启动成功                                ║
╠══════════════════════════════════════════════════════════════════════════════╣
║  应用访问地址:                                                                ║
║    本地访问: http://localhost:8081                                                             ║
║    网络访问: http://127.0.0.1:8081                                              ║
║                                                                              ║
║  🔐 管理员登录页面:                                                           ║
║    本地访问: http://localhost:8081/admin/login                                   ║
║    网络访问: http://127.0.0.1:8081/admin/login                       ║
║                                                                              ║
║  🔐 管理员后台地址 (登录后访问):                                              ║
║    本地访问: http://localhost:8081/admin/topics/audit                        ║
║    网络访问: http://127.0.0.1:8081/admin/topics/audit                 ║
║                                                                              ║
║  📋 功能模块:                                                                 ║
║    • 智能出题: http://localhost:8081                /main/chat                                    ║
║    • 题目上传: http://localhost:8081           /topics/upload-topics                         ║
║    • 题库管理: http://localhost:8081                /topics/bank                                  ║
║    • 智能组卷: http://localhost:8081                /paper/generate                               ║
║    • 用户登录: http://localhost:8081                /auth/login                                   ║
║                                                                              ║
║  ⚙️  管理员功能:                                                              ║
║    • 题目审核: http://localhost:8081           /admin/topics/audit                           ║
║    • 用户管理: http://localhost:8081                /admin/users                                  ║
║    • 系统统计: http://localhost:8081                /admin/stats                                  ║
║    • 权限管理: http://localhost:8081           /admin/permissions                            ║
║                                                                              ║
║  📊 API文档: http://localhost:8081           /swagger-ui.html (如果启用)                      ║
║                                                                              ║
║  ⚠️  安全提示:                                                               ║
║    • 管理员后台入口已隐藏，普通用户无法在前端页面看到                          ║
║    • 只有 role=1 的管理员用户才能访问后台管理功能                            ║
║    • 建议定期更改管理员密码，确保系统安全                                    ║
╚══════════════════════════════════════════════════════════════════════════════╝
2025-06-26 03:23:45.719 [main] WARN  com.edu.maizi_edu_sys.config.ApplicationStartupListener - 
🔒 管理员后台安全提示:
   • 登录页面: http://localhost:8081/admin/login
   • 后台地址: http://localhost:8081/admin/topics/audit
   • 访问权限: 仅限 role=1 的管理员用户
   • 默认账号: admin / admin123 (请立即修改密码)
   • 安全建议: 请妥善保管管理员账号信息
   • 功能说明: 题目审核、用户管理、系统统计、权限管理
2025-06-26 03:23:50.909 [http-nio-8081-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-26 03:23:50.909 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-26 03:23:50.911 [http-nio-8081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-26 03:23:50.925 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/login, Token: exists
2025-06-26 03:23:51.195 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-06-26 03:23:51.195 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求生成验证码
2025-06-26 03:23:51.196 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:51.310 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:23:51.310 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:51.311 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:51.378 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:23:51.382 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/user/current
2025-06-26 03:23:51.396 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-26 03:23:51.400 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...ohBA
2025-06-26 03:23:51.400 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:51.402 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:51.403 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-26 03:23:51.407 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-26 03:23:51.407 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-26 03:23:51.786 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/login, Token: exists
2025-06-26 03:23:52.114 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: 8633a144-ed21-409c-85a1-c1370fc0d2bd
2025-06-26 03:23:52.506 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/topics/audit, Token: exists
2025-06-26 03:23:52.506 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员拦截器检查访问: /admin/topics/audit
2025-06-26 03:23:52.506 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:23:52.506 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:52.510 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:52.521 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:23:52.527 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.AdminSecurityServiceImpl - 管理员操作日志: 用户[admin(1920280447393230850)] 执行操作[ACCESS] 访问资源[/admin/topics/audit]
2025-06-26 03:23:52.530 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员访问后台: userId=1920280447393230850, username=admin, uri=/admin/topics/audit
2025-06-26 03:23:52.664 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求生成验证码
2025-06-26 03:23:52.669 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: a227262d-9164-4d91-a5f2-4d487a9655c1
2025-06-26 03:23:56.385 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/login, Token: exists
2025-06-26 03:23:56.439 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-06-26 03:23:56.439 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:56.439 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求生成验证码
2025-06-26 03:23:56.443 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:23:56.443 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:56.445 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:56.449 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: ccef5c59-725a-436c-a4ce-1eb6b7cf6cb9
2025-06-26 03:23:56.451 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:23:56.453 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/user/current
2025-06-26 03:23:56.453 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-26 03:23:56.453 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...ohBA
2025-06-26 03:23:56.453 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:56.454 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:56.455 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-26 03:23:56.457 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-26 03:23:56.457 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-26 03:23:56.952 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/login, Token: exists
2025-06-26 03:23:57.520 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/topics/audit, Token: exists
2025-06-26 03:23:57.521 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员拦截器检查访问: /admin/topics/audit
2025-06-26 03:23:57.521 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:23:57.521 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:57.523 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:23:57.529 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:23:57.538 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.service.impl.AdminSecurityServiceImpl - 管理员操作日志: 用户[admin(1920280447393230850)] 执行操作[ACCESS] 访问资源[/admin/topics/audit]
2025-06-26 03:23:57.547 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员访问后台: userId=1920280447393230850, username=admin, uri=/admin/topics/audit
2025-06-26 03:23:57.654 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求生成验证码
2025-06-26 03:23:57.658 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: fe709280-7611-4119-87e5-458a9a4bc2ee
2025-06-26 03:24:06.004 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/login, Token: exists
2025-06-26 03:24:06.066 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-06-26 03:24:06.066 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求生成验证码
2025-06-26 03:24:06.066 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:24:06.067 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:24:06.067 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:24:06.070 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:24:06.072 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: bcb8d7d8-7042-4d84-aca8-b590fabce37c
2025-06-26 03:24:06.075 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:24:06.077 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/user/current
2025-06-26 03:24:06.077 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-26 03:24:06.077 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...ohBA
2025-06-26 03:24:06.077 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:24:06.078 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:24:06.079 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-26 03:24:06.081 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-26 03:24:06.081 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-26 03:24:06.778 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/login, Token: exists
2025-06-26 03:24:07.134 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/topics/audit, Token: exists
2025-06-26 03:24:07.135 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员拦截器检查访问: /admin/topics/audit
2025-06-26 03:24:07.135 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:24:07.135 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:24:07.136 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:24:07.141 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:24:07.148 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.impl.AdminSecurityServiceImpl - 管理员操作日志: 用户[admin(1920280447393230850)] 执行操作[ACCESS] 访问资源[/admin/topics/audit]
2025-06-26 03:24:07.151 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员访问后台: userId=1920280447393230850, username=admin, uri=/admin/topics/audit
2025-06-26 03:24:07.308 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求生成验证码
2025-06-26 03:24:07.313 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: d6c1179a-93c8-4973-ac23-3d609623b9e0
2025-06-26 03:24:14.786 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 20.52735344368153%, pool size: 50
2025-06-26 03:24:18.029 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/login, Token: exists
2025-06-26 03:24:18.081 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求生成验证码
2025-06-26 03:24:18.081 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-06-26 03:24:18.081 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:24:18.084 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:24:18.084 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:24:18.086 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:24:18.091 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: 0c8a8884-a787-4320-88c6-0f469396cfae
2025-06-26 03:24:18.091 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:24:18.093 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/user/current
2025-06-26 03:24:18.093 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-26 03:24:18.093 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...ohBA
2025-06-26 03:24:18.093 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:24:18.095 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:24:18.096 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-26 03:24:18.098 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-26 03:24:18.098 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-26 03:24:18.681 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/login, Token: exists
2025-06-26 03:24:19.136 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/topics/audit, Token: exists
2025-06-26 03:24:19.137 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员拦截器检查访问: /admin/topics/audit
2025-06-26 03:24:19.137 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:24:19.137 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:24:19.138 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:24:19.144 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:24:19.150 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.service.impl.AdminSecurityServiceImpl - 管理员操作日志: 用户[admin(1920280447393230850)] 执行操作[ACCESS] 访问资源[/admin/topics/audit]
2025-06-26 03:24:19.153 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员访问后台: userId=1920280447393230850, username=admin, uri=/admin/topics/audit
2025-06-26 03:24:19.253 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求生成验证码
2025-06-26 03:24:19.261 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: 9fd811b6-527e-45be-832b-427521b71c23
2025-06-26 03:24:20.565 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/login, Token: exists
2025-06-26 03:24:20.626 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-06-26 03:24:20.626 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求生成验证码
2025-06-26 03:24:20.627 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:24:20.629 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:24:20.630 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:24:20.631 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:24:20.636 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: c0ee8b90-e4ab-43d7-bf85-1333f657cd03
2025-06-26 03:24:20.639 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:24:20.641 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/user/current
2025-06-26 03:24:20.641 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-26 03:24:20.641 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...ohBA
2025-06-26 03:24:20.641 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:24:20.642 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:24:20.643 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-26 03:24:20.644 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-26 03:24:20.645 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-26 03:24:21.142 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/login, Token: exists
2025-06-26 03:24:21.721 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/topics/audit, Token: exists
2025-06-26 03:24:21.722 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员拦截器检查访问: /admin/topics/audit
2025-06-26 03:24:21.722 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:24:21.722 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:24:21.724 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:24:21.731 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:24:21.736 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.AdminSecurityServiceImpl - 管理员操作日志: 用户[admin(1920280447393230850)] 执行操作[ACCESS] 访问资源[/admin/topics/audit]
2025-06-26 03:24:21.739 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员访问后台: userId=1920280447393230850, username=admin, uri=/admin/topics/audit
2025-06-26 03:24:21.841 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求生成验证码
2025-06-26 03:24:21.845 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: 28facf1a-0c83-4f6d-b5d3-574e3bc81f62
2025-06-26 03:24:23.677 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/login, Token: exists
2025-06-26 03:24:23.732 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-06-26 03:24:23.732 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求生成验证码
2025-06-26 03:24:23.733 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:24:23.735 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:24:23.735 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:24:23.736 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:24:23.740 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: da161293-2b64-4d48-96cd-6d7b3dd26307
2025-06-26 03:24:23.744 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:24:23.745 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/user/current
2025-06-26 03:24:23.745 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-26 03:24:23.745 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...ohBA
2025-06-26 03:24:23.745 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:24:23.746 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:24:23.746 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-26 03:24:23.749 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-26 03:24:23.749 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-26 03:24:24.340 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/login, Token: exists
2025-06-26 03:24:24.812 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/topics/audit, Token: exists
2025-06-26 03:24:24.812 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员拦截器检查访问: /admin/topics/audit
2025-06-26 03:24:24.812 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:24:24.812 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:24:24.813 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:24:24.818 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:24:24.824 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.service.impl.AdminSecurityServiceImpl - 管理员操作日志: 用户[admin(1920280447393230850)] 执行操作[ACCESS] 访问资源[/admin/topics/audit]
2025-06-26 03:24:24.826 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员访问后台: userId=1920280447393230850, username=admin, uri=/admin/topics/audit
2025-06-26 03:24:24.982 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求生成验证码
2025-06-26 03:24:24.989 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: 86cf71cd-b65e-4340-b43b-758a5996ef8e
2025-06-26 03:24:44.781 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 22.84688617459577%, pool size: 50
2025-06-26 03:25:04.712 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/login, Token: exists
2025-06-26 03:25:04.888 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/user/current, Token: exists
2025-06-26 03:25:04.888 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求生成验证码
2025-06-26 03:25:04.888 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:25:04.891 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:25:04.893 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:25:04.894 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:25:04.897 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: fe8eea21-a071-4b86-836f-6524545c7074
2025-06-26 03:25:04.901 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:25:04.902 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/user/current
2025-06-26 03:25:04.902 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.UserController - Endpoint /api/user/current called
2025-06-26 03:25:04.902 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.controller.UserController - Processing token: eyJh...ohBA
2025-06-26 03:25:04.902 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:25:04.903 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:25:04.904 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.UserController - Username 'admin' extracted from valid token
2025-06-26 03:25:04.906 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.controller.UserController - Successfully retrieved current user: admin
2025-06-26 03:25:04.906 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.controller.UserController - User avatar path: avatars/20250508090939_f9f90b54.jpg
2025-06-26 03:25:05.487 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/login, Token: exists
2025-06-26 03:25:06.110 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/topics/audit, Token: exists
2025-06-26 03:25:06.110 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员拦截器检查访问: /admin/topics/audit
2025-06-26 03:25:06.111 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:25:06.111 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:25:06.113 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:25:06.119 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:25:06.124 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.AdminSecurityServiceImpl - 管理员操作日志: 用户[admin(1920280447393230850)] 执行操作[ACCESS] 访问资源[/admin/topics/audit]
2025-06-26 03:25:06.126 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员访问后台: userId=1920280447393230850, username=admin, uri=/admin/topics/audit
2025-06-26 03:25:06.178 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/admin/topics/audit, Token: exists
2025-06-26 03:25:06.178 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/admin/stats/audit/trend, Token: exists
2025-06-26 03:25:06.178 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:25:06.178 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:25:06.178 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/admin/stats/audit, Token: exists
2025-06-26 03:25:06.178 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:25:06.179 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:25:06.179 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:25:06.179 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:25:06.179 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:25:06.179 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:25:06.179 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:25:06.180 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:25:06.180 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:25:06.180 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:25:06.183 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:25:06.183 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:25:06.183 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:25:06.184 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/admin/stats/audit/trend
2025-06-26 03:25:06.184 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/admin/topics/audit
2025-06-26 03:25:06.184 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/admin/stats/audit
2025-06-26 03:25:06.184 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:25:06.184 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:25:06.185 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:25:06.188 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:25:06.188 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:25:06.188 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:25:06.188 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:25:06.188 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:25:06.189 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.PermissionServiceImpl - 管理员用户 1920280447393230850 拥有所有权限: SYSTEM_STATS
2025-06-26 03:25:06.189 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:25:06.189 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:25:06.191 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:25:06.191 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:25:06.192 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.service.impl.PermissionServiceImpl - 管理员用户 1920280447393230850 拥有所有权限: TOPIC_AUDIT
2025-06-26 03:25:06.192 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.service.impl.PermissionServiceImpl - 管理员用户 1920280447393230850 拥有所有权限: SYSTEM_STATS
2025-06-26 03:25:06.737 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/topics/audit, Token: exists
2025-06-26 03:25:06.737 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员拦截器检查访问: /admin/topics/audit
2025-06-26 03:25:06.737 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:25:06.737 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:25:06.739 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:25:06.748 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:25:06.752 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.service.impl.AdminSecurityServiceImpl - 管理员操作日志: 用户[admin(1920280447393230850)] 执行操作[ACCESS] 访问资源[/admin/topics/audit]
2025-06-26 03:25:06.755 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员访问后台: userId=1920280447393230850, username=admin, uri=/admin/topics/audit
2025-06-26 03:25:14.783 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 25.76945699406364%, pool size: 50
2025-06-26 03:25:15.210 [http-nio-8081-exec-5] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /.well-known/appspecific/com.chrome.devtools.json
2025-06-26 03:25:44.783 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 25.76945699406364%, pool size: 50
2025-06-26 03:26:07.160 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求生成验证码
2025-06-26 03:26:07.167 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: 6fd85d2e-ee64-4026-b59c-7fa8ae24cbc7
2025-06-26 03:26:09.053 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求刷新验证码，会话ID: null
2025-06-26 03:26:09.058 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: 1e7362fc-382a-4246-820f-95a223cc0518
2025-06-26 03:26:09.669 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求刷新验证码，会话ID: null
2025-06-26 03:26:09.681 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: 7cd7eac9-e9b7-4943-82d3-3092da07dc8a
2025-06-26 03:26:10.146 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求刷新验证码，会话ID: null
2025-06-26 03:26:10.156 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: 0aa68138-d73d-4dd5-90d8-72302448dfe6
2025-06-26 03:26:10.625 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.controller.CaptchaController - 请求刷新验证码，会话ID: null
2025-06-26 03:26:10.635 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 生成验证码成功，会话ID: ae011761-4737-40fe-b7be-e58615dc5743
2025-06-26 03:26:14.783 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 25.939291462421966%, pool size: 50
2025-06-26 03:26:15.028 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/topics/audit, Token: exists
2025-06-26 03:26:15.028 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员拦截器检查访问: /admin/topics/audit
2025-06-26 03:26:15.028 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:26:15.028 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:26:15.031 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:26:15.036 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:26:15.041 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.AdminSecurityServiceImpl - 管理员操作日志: 用户[admin(1920280447393230850)] 执行操作[ACCESS] 访问资源[/admin/topics/audit]
2025-06-26 03:26:15.044 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员访问后台: userId=1920280447393230850, username=admin, uri=/admin/topics/audit
2025-06-26 03:26:15.239 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/admin/stats/audit/trend, Token: exists
2025-06-26 03:26:15.239 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:26:15.238 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/admin/stats/audit, Token: exists
2025-06-26 03:26:15.239 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:26:15.238 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/admin/topics/audit, Token: exists
2025-06-26 03:26:15.239 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:26:15.241 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:26:15.241 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:26:15.241 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:26:15.242 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:26:15.243 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:26:15.244 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:26:15.244 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:26:15.245 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:26:15.245 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:26:15.247 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:26:15.247 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:26:15.248 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:26:15.248 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/admin/stats/audit/trend
2025-06-26 03:26:15.248 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/admin/topics/audit
2025-06-26 03:26:15.249 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:26:15.249 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:26:15.249 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/admin/stats/audit
2025-06-26 03:26:15.249 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:26:15.249 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:26:15.249 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:26:15.249 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:26:15.250 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:26:15.250 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:26:15.250 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:26:15.252 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:26:15.253 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:26:15.253 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:26:15.254 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.PermissionServiceImpl - 管理员用户 1920280447393230850 拥有所有权限: SYSTEM_STATS
2025-06-26 03:26:15.255 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.PermissionServiceImpl - 管理员用户 1920280447393230850 拥有所有权限: TOPIC_AUDIT
2025-06-26 03:26:15.256 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.PermissionServiceImpl - 管理员用户 1920280447393230850 拥有所有权限: SYSTEM_STATS
2025-06-26 03:26:15.769 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/topics/audit, Token: exists
2025-06-26 03:26:15.769 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员拦截器检查访问: /admin/topics/audit
2025-06-26 03:26:15.769 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:26:15.769 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:26:15.770 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:26:15.779 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:26:15.785 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.impl.AdminSecurityServiceImpl - 管理员操作日志: 用户[admin(1920280447393230850)] 执行操作[ACCESS] 访问资源[/admin/topics/audit]
2025-06-26 03:26:15.787 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员访问后台: userId=1920280447393230850, username=admin, uri=/admin/topics/audit
2025-06-26 03:26:44.787 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 27.162142380233746%, pool size: 50
2025-06-26 03:27:14.784 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 27.162142380233746%, pool size: 50
2025-06-26 03:27:44.789 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 27.162142380233746%, pool size: 50
2025-06-26 03:28:14.786 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 27.162142380233746%, pool size: 50
2025-06-26 03:28:44.787 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 27.162142380233746%, pool size: 50
2025-06-26 03:29:14.789 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 27.467853065838177%, pool size: 50
2025-06-26 03:29:44.788 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 27.467853065838177%, pool size: 50
2025-06-26 03:30:14.791 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 27.467853065838177%, pool size: 50
2025-06-26 03:30:44.789 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 27.467853065838177%, pool size: 50
2025-06-26 03:31:14.793 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 27.467853065838177%, pool size: 50
2025-06-26 03:31:44.793 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 27.467853065838177%, pool size: 50
2025-06-26 03:32:14.792 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 27.658918448622273%, pool size: 50
2025-06-26 03:32:44.792 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 27.658918448622273%, pool size: 50
2025-06-26 03:32:58.319 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/topics/audit, Token: exists
2025-06-26 03:32:58.320 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员拦截器检查访问: /admin/topics/audit
2025-06-26 03:32:58.320 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:32:58.320 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:32:58.326 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:32:58.340 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:32:58.344 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.impl.AdminSecurityServiceImpl - 管理员操作日志: 用户[admin(1920280447393230850)] 执行操作[ACCESS] 访问资源[/admin/topics/audit]
2025-06-26 03:32:58.348 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员访问后台: userId=1920280447393230850, username=admin, uri=/admin/topics/audit
2025-06-26 03:32:58.591 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/admin/topics/audit, Token: exists
2025-06-26 03:32:58.592 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/admin/stats/audit/trend, Token: exists
2025-06-26 03:32:58.592 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/admin/stats/audit, Token: exists
2025-06-26 03:32:58.592 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:32:58.592 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:32:58.592 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:32:58.594 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:32:58.595 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:32:58.595 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:32:58.596 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:32:58.596 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:32:58.596 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:32:58.598 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:32:58.598 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:32:58.598 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:32:58.606 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:32:58.607 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:32:58.608 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:32:58.608 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/admin/stats/audit
2025-06-26 03:32:58.608 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/admin/topics/audit
2025-06-26 03:32:58.610 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:32:58.610 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:32:58.610 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:32:58.610 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:32:58.612 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/admin/stats/audit/trend
2025-06-26 03:32:58.612 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:32:58.612 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:32:58.612 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:32:58.613 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:32:58.616 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:32:58.619 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:32:58.619 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:32:58.620 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:32:58.620 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.PermissionServiceImpl - 管理员用户 1920280447393230850 拥有所有权限: TOPIC_AUDIT
2025-06-26 03:32:58.621 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.PermissionServiceImpl - 管理员用户 1920280447393230850 拥有所有权限: SYSTEM_STATS
2025-06-26 03:32:58.621 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.PermissionServiceImpl - 管理员用户 1920280447393230850 拥有所有权限: SYSTEM_STATS
2025-06-26 03:32:59.170 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/topics/audit, Token: exists
2025-06-26 03:32:59.171 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员拦截器检查访问: /admin/topics/audit
2025-06-26 03:32:59.171 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:32:59.171 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:32:59.172 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:32:59.178 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:32:59.182 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.impl.AdminSecurityServiceImpl - 管理员操作日志: 用户[admin(1920280447393230850)] 执行操作[ACCESS] 访问资源[/admin/topics/audit]
2025-06-26 03:32:59.183 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员访问后台: userId=1920280447393230850, username=admin, uri=/admin/topics/audit
2025-06-26 03:33:14.794 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 28.155685757660017%, pool size: 50
2025-06-26 03:33:17.894 [http-nio-8081-exec-10] DEBUG com.edu.maizi_edu_sys.util.RequestUtil - Client IP detected: 0:0:0:0:0:0:0:1
2025-06-26 03:33:17.894 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.controller.AdminController - 管理员登录尝试，IP: 0:0:0:0:0:0:0:1, 用户名: admin
2025-06-26 03:33:17.900 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.service.impl.CaptchaServiceImpl - 验证码验证失败：验证码已过期或不存在，会话ID: test
2025-06-26 03:33:17.900 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.service.impl.UserServiceImpl - 验证码验证失败，用户: admin, IP: 0:0:0:0:0:0:0:1
2025-06-26 03:33:17.900 [http-nio-8081-exec-10] WARN  com.edu.maizi_edu_sys.controller.AdminController - 管理员登录失败，用户名: admin, 原因: 验证码错误或已过期
2025-06-26 03:33:17.911 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/admin/stats/audit, Token: exists
2025-06-26 03:33:17.911 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:17.912 [http-nio-8081-exec-5] ERROR com.edu.maizi_edu_sys.util.JwtUtil - Malformed JWT: JWT strings must contain exactly 2 period characters. Found: 0. Token: [Bearer]
2025-06-26 03:33:17.913 [http-nio-8081-exec-5] WARN  com.edu.maizi_edu_sys.config.AuthInterceptor - Invalid token for URI: /api/admin/stats/audit
2025-06-26 03:33:25.455 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/topics/audit, Token: exists
2025-06-26 03:33:25.457 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员拦截器检查访问: /admin/topics/audit
2025-06-26 03:33:25.457 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:33:25.457 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:25.460 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:25.467 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:33:25.472 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.AdminSecurityServiceImpl - 管理员操作日志: 用户[admin(1920280447393230850)] 执行操作[ACCESS] 访问资源[/admin/topics/audit]
2025-06-26 03:33:25.474 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员访问后台: userId=1920280447393230850, username=admin, uri=/admin/topics/audit
2025-06-26 03:33:25.528 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/admin/topics/audit, Token: exists
2025-06-26 03:33:25.528 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/admin/stats/audit/trend, Token: exists
2025-06-26 03:33:25.528 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:25.528 [http-nio-8081-exec-9] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/admin/stats/audit, Token: exists
2025-06-26 03:33:25.528 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:25.528 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:25.530 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:33:25.530 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:33:25.530 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:25.530 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:25.530 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:33:25.530 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:25.530 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:25.530 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:25.530 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:25.533 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:33:25.533 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:33:25.534 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:33:25.534 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/admin/stats/audit
2025-06-26 03:33:25.534 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/admin/stats/audit/trend
2025-06-26 03:33:25.534 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:33:25.534 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:25.534 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:33:25.534 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:25.534 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/admin/topics/audit
2025-06-26 03:33:25.535 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:33:25.535 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:25.535 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:25.535 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:25.535 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:25.537 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:33:25.537 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:33:25.537 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:33:25.538 [http-nio-8081-exec-1] DEBUG com.edu.maizi_edu_sys.service.impl.PermissionServiceImpl - 管理员用户 1920280447393230850 拥有所有权限: SYSTEM_STATS
2025-06-26 03:33:25.538 [http-nio-8081-exec-9] DEBUG com.edu.maizi_edu_sys.service.impl.PermissionServiceImpl - 管理员用户 1920280447393230850 拥有所有权限: SYSTEM_STATS
2025-06-26 03:33:25.538 [http-nio-8081-exec-8] DEBUG com.edu.maizi_edu_sys.service.impl.PermissionServiceImpl - 管理员用户 1920280447393230850 拥有所有权限: TOPIC_AUDIT
2025-06-26 03:33:26.093 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/topics/audit, Token: exists
2025-06-26 03:33:26.093 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员拦截器检查访问: /admin/topics/audit
2025-06-26 03:33:26.093 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:33:26.093 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:26.095 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:26.103 [http-nio-8081-exec-4] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:33:26.107 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.service.impl.AdminSecurityServiceImpl - 管理员操作日志: 用户[admin(1920280447393230850)] 执行操作[ACCESS] 访问资源[/admin/topics/audit]
2025-06-26 03:33:26.108 [http-nio-8081-exec-4] INFO  com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员访问后台: userId=1920280447393230850, username=admin, uri=/admin/topics/audit
2025-06-26 03:33:29.815 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/topics/audit, Token: exists
2025-06-26 03:33:29.816 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员拦截器检查访问: /admin/topics/audit
2025-06-26 03:33:29.816 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:33:29.816 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:29.819 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:29.825 [http-nio-8081-exec-3] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:33:29.829 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.service.impl.AdminSecurityServiceImpl - 管理员操作日志: 用户[admin(1920280447393230850)] 执行操作[ACCESS] 访问资源[/admin/topics/audit]
2025-06-26 03:33:29.830 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员访问后台: userId=1920280447393230850, username=admin, uri=/admin/topics/audit
2025-06-26 03:33:29.891 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/admin/stats/audit, Token: exists
2025-06-26 03:33:29.891 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/admin/stats/audit/trend, Token: exists
2025-06-26 03:33:29.891 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /api/admin/topics/audit, Token: exists
2025-06-26 03:33:29.891 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:29.891 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:29.891 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:29.892 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:33:29.892 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:33:29.892 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:29.892 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:29.892 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:33:29.892 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:29.892 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:29.892 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:29.893 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:29.895 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:33:29.895 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:33:29.895 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:33:29.896 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/admin/stats/audit
2025-06-26 03:33:29.896 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/admin/topics/audit
2025-06-26 03:33:29.896 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.config.AuthInterceptor - 管理员用户 1920280447393230850 访问API: /api/admin/stats/audit/trend
2025-06-26 03:33:29.896 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:33:29.896 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:29.896 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:33:29.896 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:29.897 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:33:29.897 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:29.897 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:29.897 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:29.897 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:29.900 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:33:29.900 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:33:29.900 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:33:29.901 [http-nio-8081-exec-6] DEBUG com.edu.maizi_edu_sys.service.impl.PermissionServiceImpl - 管理员用户 1920280447393230850 拥有所有权限: SYSTEM_STATS
2025-06-26 03:33:29.901 [http-nio-8081-exec-5] DEBUG com.edu.maizi_edu_sys.service.impl.PermissionServiceImpl - 管理员用户 1920280447393230850 拥有所有权限: TOPIC_AUDIT
2025-06-26 03:33:29.901 [http-nio-8081-exec-2] DEBUG com.edu.maizi_edu_sys.service.impl.PermissionServiceImpl - 管理员用户 1920280447393230850 拥有所有权限: SYSTEM_STATS
2025-06-26 03:33:30.379 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AuthInterceptor - Request URI: /admin/topics/audit, Token: exists
2025-06-26 03:33:30.379 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员拦截器检查访问: /admin/topics/audit
2025-06-26 03:33:30.379 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 获取当前用户ID - token: 存在
2025-06-26 03:33:30.379 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Validating token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:30.381 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.util.JwtUtil - Attempting to get username from token using secret (first 5): 'F9A8C...'
2025-06-26 03:33:30.385 [http-nio-8081-exec-7] DEBUG com.edu.maizi_edu_sys.service.impl.AuthServiceImpl - 成功获取当前用户ID: 1920280447393230850, 用户名: admin
2025-06-26 03:33:30.390 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.AdminSecurityServiceImpl - 管理员操作日志: 用户[admin(1920280447393230850)] 执行操作[ACCESS] 访问资源[/admin/topics/audit]
2025-06-26 03:33:30.392 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.config.AdminInterceptor - 管理员访问后台: userId=1920280447393230850, username=admin, uri=/admin/topics/audit
2025-06-26 03:33:44.798 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 30.907041051129568%, pool size: 50
2025-06-26 03:33:48.905 [http-nio-8081-exec-1] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /.well-known/appspecific/com.chrome.devtools.json
2025-06-26 03:34:14.797 [pool-3-thread-1] DEBUG com.edu.maizi_edu_sys.service.memory.MemoryManager - Memory usage: 30.907041051129568%, pool size: 50
2025-06-26 03:34:26.096 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopping...
2025-06-26 03:34:26.096 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@67915454]]
2025-06-26 03:34:26.096 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopped.
2025-06-26 03:34:26.110 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-06-26 03:34:26.227 [SpringApplicationShutdownHook] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-26 03:34:26.229 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-26 03:34:26.234 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-26 03:34:27.742 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-26 03:34:27.801 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_452 on shixiqiangdeMacBook-Pro.local with PID 21892 (/Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys/target/classes started by shixiqiang in /Users/<USER>/Application Files/IdeaProjects/maizi_edu_sys)
2025-06-26 03:34:27.802 [main] DEBUG com.edu.maizi_edu_sys.Application - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-06-26 03:34:27.803 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-06-26 03:34:28.397 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 03:34:28.398 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-26 03:34:28.533 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 131 ms. Found 5 JPA repository interfaces.
2025-06-26 03:34:28.539 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 03:34:28.540 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 03:34:28.549 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.BookRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 03:34:28.549 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatMessageRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 03:34:28.550 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.ChatSessionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 03:34:28.550 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperConfigRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 03:34:28.551 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-26 03:34:28.551 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Redis repository interfaces.
2025-06-26 03:34:28.605 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.edu.maizi_edu_sys.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 03:34:28.605 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatMessageRepository' and 'com.edu.maizi_edu_sys.repository.ChatMessageRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 03:34:28.605 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'chatSessionRepository' and 'com.edu.maizi_edu_sys.repository.ChatSessionRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 03:34:28.606 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperConfigRepository' and 'com.edu.maizi_edu_sys.repository.PaperConfigRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 03:34:28.606 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'paperDownloadRepository' and 'com.edu.maizi_edu_sys.repository.PaperDownloadRepository' mapperInterface. Bean already defined with the same name!
2025-06-26 03:34:28.883 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-06-26 03:34:28.888 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-06-26 03:34:28.888 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 03:34:28.888 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-26 03:34:28.963 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 03:34:28.964 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1139 ms
2025-06-26 03:34:29.107 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-26 03:34:29.136 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
