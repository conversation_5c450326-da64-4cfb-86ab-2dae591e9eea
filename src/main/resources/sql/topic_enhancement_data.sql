SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for topic_enhancement_data
-- ----------------------------
DROP TABLE IF EXISTS `topic_enhancement_data`;
CREATE TABLE `topic_enhancement_data`  (
  `topic_id` int(0) UNSIGNED NOT NULL COMMENT '题目ID (外键关联 topic_bak.id)',
  `submission_count` int(0) UNSIGNED NOT NULL DEFAULT 0 COMMENT '总提交/使用次数 (用于计算错误率和新鲜度)',
  `error_submission_count` int(0) UNSIGNED NOT NULL DEFAULT 0 COMMENT '错误提交次数 (用于计算错误率)',
  `quality_score` decimal(5,4) NULL DEFAULT NULL COMMENT '综合质量评分 (根据错误率和新鲜度)',
  `embedding` blob NULL COMMENT '题目语义向量 (用于相似度计算)',
  `cognitive_level` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '认知层次 (如: 记忆, 理解, 应用, 分析, 评价, 创造)',
  `last_used_timestamp` timestamp(0) NULL DEFAULT NULL COMMENT '上次使用时间 (用于曝光控制)',
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最后更新时间',
  PRIMARY KEY (`topic_id`) USING BTREE,
  CONSTRAINT `fk_topic_enhancement_topic_id` FOREIGN KEY (`topic_id`) REFERENCES `topic_bak` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '题目增强数据表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1; 