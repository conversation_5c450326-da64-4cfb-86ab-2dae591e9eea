package com.edu.maizi_edu_sys.utils;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

public class MysqlSleepKiller {

    // 配置你的数据库信息
    private static final String JDBC_URL = "***************************************************************************************************************";
    private static final String USER = "root";
    private static final String PASSWORD = "Hilury157195!";
    private static final int SLEEP_THRESHOLD_SECONDS = 300; // 超过300秒的Sleep连接会被kill

    public static void main(String[] args) {
        try (Connection conn = DriverManager.getConnection(JDBC_URL, USER, PASSWORD);
             Statement stmt = conn.createStatement()) {

            // 查询所有Sleep且超时的进程
            String sql = "SELECT id FROM information_schema.processlist WHERE command='Sleep' AND time > " + SLEEP_THRESHOLD_SECONDS;
            ResultSet rs = stmt.executeQuery(sql);

            List<Integer> idsToKill = new ArrayList<>();
            while (rs.next()) {
                idsToKill.add(rs.getInt("id"));
            }

            for (Integer id : idsToKill) {
                try (Statement killStmt = conn.createStatement()) {
                    System.out.println("Killing MySQL process id: " + id);
                    killStmt.execute("KILL " + id);
                } catch (Exception e) {
                    System.err.println("Failed to kill process id " + id + ": " + e.getMessage());
                }
            }

            if (idsToKill.isEmpty()) {
                System.out.println("No long sleep connections found.");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}