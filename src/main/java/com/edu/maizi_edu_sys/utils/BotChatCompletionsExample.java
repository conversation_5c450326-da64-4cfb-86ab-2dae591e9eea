package com.edu.maizi_edu_sys.utils;


import com.volcengine.ark.runtime.model.bot.completion.chat.BotChatCompletionRequest;
import com.volcengine.ark.runtime.model.bot.completion.chat.BotChatCompletionResult;
import com.volcengine.ark.runtime.model.completion.chat.*;
import com.volcengine.ark.runtime.service.ArkService;

import java.util.*;

public class BotChatCompletionsExample {
    public static void main(String[] args) {

        String apiKey = "71c264ae-dc70-42b6-84c1-72b055ecfb8c";
        ArkService service = ArkService.builder().apiKey(apiKey).build();

        System.out.println("\n----- multiple rounds request  -----");
        final List<ChatMessage> messages = Arrays.asList(
                ChatMessage.builder().role(ChatMessageRole.SYSTEM).content("你是豆包，是由字节跳动开发的 AI 人工智能助手").build(),
                ChatMessage.builder().role(ChatMessageRole.USER).content("花椰菜是什么？").build(),
                ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content("花椰菜又称菜花、花菜，是一种常见的蔬菜。").build(),
                ChatMessage.builder().role(ChatMessageRole.USER).content("再详细点").build()
        );

        BotChatCompletionRequest chatCompletionRequest = BotChatCompletionRequest.builder()
                // 将<YOUR_BOT_ID>替换为您的应用ID 
                .model("bot-20250507182807-dbmrx")
                .messages(messages)
                .build();

        BotChatCompletionResult chatCompletionResult =  service.createBotChatCompletion(chatCompletionRequest);
        chatCompletionResult.getChoices().forEach(
                choice -> System.out.println(choice.getMessage().getContent())
        );
        // the references example
        chatCompletionResult.getReferences().forEach(
                ref -> System.out.println(ref.getUrl())
        );
    }

}