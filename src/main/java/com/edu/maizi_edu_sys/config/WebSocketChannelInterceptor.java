package com.edu.maizi_edu_sys.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.simp.stomp.StompCommand;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.messaging.support.ChannelInterceptor;
import org.springframework.messaging.support.MessageHeaderAccessor;
import org.springframework.stereotype.Component;
import org.springframework.lang.NonNull;

/**
 * Intercepts WebSocket messages for monitoring, logging, and security purposes
 */
@Slf4j
@Component
public class WebSocketChannelInterceptor implements ChannelInterceptor {

    @Override
    public Message<?> preSend(@NonNull Message<?> message, @NonNull MessageChannel channel) {
        StompHeaderAccessor accessor = MessageHeaderAccessor.getAccessor(message, StompHeaderAccessor.class);
        
        if (accessor != null) {
            String sessionId = accessor.getSessionId();
            
            // Log different events based on the STOMP command
            if (StompCommand.CONNECT.equals(accessor.getCommand())) {
                log.info("WebSocket Connection established: {}", sessionId);
            } else if (StompCommand.DISCONNECT.equals(accessor.getCommand())) {
                log.info("WebSocket Connection disconnected: {}", sessionId);
            } else if (StompCommand.SUBSCRIBE.equals(accessor.getCommand())) {
                String destination = accessor.getDestination();
                log.info("WebSocket Subscription to '{}' from session: {}", destination, sessionId);
            } else if (StompCommand.SEND.equals(accessor.getCommand())) {
                String destination = accessor.getDestination();
                log.debug("WebSocket Message sent to '{}' from session: {}", destination, sessionId);
            } else if (StompCommand.UNSUBSCRIBE.equals(accessor.getCommand())) {
                log.info("WebSocket Unsubscribe from session: {}", sessionId);
            }
            
            // You could add authentication verification here
            // For example:
            // if (StompCommand.CONNECT.equals(accessor.getCommand())) {
            //     String token = accessor.getFirstNativeHeader("token");
            //     // validate token
            // }
        }
        
        return message;
    }

    @Override
    public void postSend(@NonNull Message<?> message, @NonNull MessageChannel channel, boolean sent) {
        if (!sent) {
            StompHeaderAccessor accessor = MessageHeaderAccessor.getAccessor(message, StompHeaderAccessor.class);
            if (accessor != null) {
                log.error("Failed to send WebSocket message: {}", accessor.getDestination());
            }
        }
    }
} 