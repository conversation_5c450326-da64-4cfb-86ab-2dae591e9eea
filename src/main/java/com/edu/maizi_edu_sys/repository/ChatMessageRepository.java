package com.edu.maizi_edu_sys.repository;

import com.edu.maizi_edu_sys.entity.ChatMessageEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface ChatMessageRepository extends JpaRepository<ChatMessageEntity, Long> {
    List<ChatMessageEntity> findBySessionIdOrderByCreatedAt(Long sessionId);
    void deleteAllBySessionId(Long sessionId);
} 