package com.edu.maizi_edu_sys.repository;

import com.edu.maizi_edu_sys.entity.PaperConfig;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 试卷配置Repository
 */
@Repository
public interface PaperConfigRepository extends JpaRepository<PaperConfig, Long> {

    /**
     * 根据用户ID查找配置列表
     */
    List<PaperConfig> findByUserIdOrderByLastUsedAtDescCreatedAtDesc(Long userId);

    /**
     * 根据用户ID分页查找配置
     */
    Page<PaperConfig> findByUserIdOrderByLastUsedAtDescCreatedAtDesc(Long userId, Pageable pageable);

    /**
     * 查找用户的配置和公共配置
     */
    @Query("SELECT pc FROM PaperConfig pc WHERE pc.userId = :userId OR pc.isPublic = true ORDER BY pc.lastUsedAt DESC, pc.createdAt DESC")
    List<PaperConfig> findByUserIdOrPublicOrderByLastUsedAtDescCreatedAtDesc(@Param("userId") Long userId);

    /**
     * 分页查找用户的配置和公共配置
     */
    @Query("SELECT pc FROM PaperConfig pc WHERE pc.userId = :userId OR pc.isPublic = true ORDER BY pc.lastUsedAt DESC, pc.createdAt DESC")
    Page<PaperConfig> findByUserIdOrPublicOrderByLastUsedAtDescCreatedAtDesc(@Param("userId") Long userId, Pageable pageable);

    /**
     * 根据配置名称和用户ID查找（用于检查重名）
     */
    Optional<PaperConfig> findByConfigNameAndUserId(String configName, Long userId);

    /**
     * 查找用户的默认配置
     */
    Optional<PaperConfig> findByUserIdAndIsDefaultTrue(Long userId);

    /**
     * 查找用户的所有默认配置（用于清理重复默认配置）
     */
    List<PaperConfig> findAllByUserIdAndIsDefaultTrue(Long userId);

    /**
     * 查找公共配置
     */
    List<PaperConfig> findByIsPublicTrueOrderByUsageCountDescCreatedAtDesc();

    /**
     * 分页查找公共配置
     */
    Page<PaperConfig> findByIsPublicTrueOrderByUsageCountDescCreatedAtDesc(Pageable pageable);

    /**
     * 根据配置名称模糊搜索用户配置
     */
    @Query("SELECT pc FROM PaperConfig pc WHERE pc.userId = :userId AND pc.configName LIKE %:keyword% ORDER BY pc.lastUsedAt DESC, pc.createdAt DESC")
    List<PaperConfig> findByUserIdAndConfigNameContainingOrderByLastUsedAtDescCreatedAtDesc(@Param("userId") Long userId, @Param("keyword") String keyword);

    /**
     * 根据配置名称模糊搜索用户配置和公共配置
     */
    @Query("SELECT pc FROM PaperConfig pc WHERE (pc.userId = :userId OR pc.isPublic = true) AND pc.configName LIKE %:keyword% ORDER BY pc.lastUsedAt DESC, pc.createdAt DESC")
    List<PaperConfig> findByUserIdOrPublicAndConfigNameContainingOrderByLastUsedAtDescCreatedAtDesc(@Param("userId") Long userId, @Param("keyword") String keyword);

    /**
     * 统计用户配置数量
     */
    long countByUserId(Long userId);

    /**
     * 统计公共配置数量
     */
    long countByIsPublicTrue();

    /**
     * 查找最近使用的配置
     */
    @Query("SELECT pc FROM PaperConfig pc WHERE pc.userId = :userId AND pc.lastUsedAt IS NOT NULL ORDER BY pc.lastUsedAt DESC")
    List<PaperConfig> findRecentlyUsedByUserId(@Param("userId") Long userId, Pageable pageable);

    /**
     * 查找最常用的配置
     */
    @Query("SELECT pc FROM PaperConfig pc WHERE pc.userId = :userId ORDER BY pc.usageCount DESC, pc.lastUsedAt DESC")
    List<PaperConfig> findMostUsedByUserId(@Param("userId") Long userId, Pageable pageable);

    /**
     * 删除用户的所有配置
     */
    void deleteByUserId(Long userId);

    /**
     * 检查配置名称是否存在（排除指定ID）
     */
    @Query("SELECT COUNT(pc) > 0 FROM PaperConfig pc WHERE pc.configName = :configName AND pc.userId = :userId AND pc.id != :excludeId")
    boolean existsByConfigNameAndUserIdAndIdNot(@Param("configName") String configName, @Param("userId") Long userId, @Param("excludeId") Long excludeId);
}
