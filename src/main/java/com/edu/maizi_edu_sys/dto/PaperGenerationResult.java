package com.edu.maizi_edu_sys.dto;

import com.edu.maizi_edu_sys.entity.Topic;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 试卷生成结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaperGenerationResult {
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 结果消息
     */
    private String message;
    
    /**
     * 警告消息
     */
    private String warningMessage;
    
    /**
     * 选中的题目
     */
    private List<Topic> selectedTopics;
    
    /**
     * 总分
     */
    private Integer totalScore;
    
    /**
     * 原始题目数量（算法输出）
     */
    private Integer originalSize;
    
    /**
     * 最终题目数量（过滤后）
     */
    private Integer finalSize;
    
    /**
     * 每种题型的实际数量
     */
    private Map<String, Integer> typeCountMap;
    
    /**
     * 难度分布
     */
    private Map<String, Float> difficultyDistribution;
    
    /**
     * 知识点覆盖情况
     */
    private Map<Long, Integer> knowledgePointCoverage;
    
    /**
     * 是否有警告
     */
    public boolean hasWarning() {
        return warningMessage != null && !warningMessage.isEmpty();
    }
} 