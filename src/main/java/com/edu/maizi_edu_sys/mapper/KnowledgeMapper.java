package com.edu.maizi_edu_sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.edu.maizi_edu_sys.dto.KnowledgeDTO;
import com.edu.maizi_edu_sys.dto.KnowledgeGroupDTO;
import com.edu.maizi_edu_sys.entity.Knowledge;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 知识点Mapper接口
 */
@Mapper
public interface KnowledgeMapper extends BaseMapper<Knowledge> {

    /**
     * 获取所有知识点分类及其知识点数量
     */
    @Select("SELECT MIN(id) as id, group_name as groupName, " +
            "(SELECT COUNT(*) FROM wm_knowledge k2 WHERE k2.group_name = k1.group_name AND k2.is_deleted = 0) as count, " +
            "MIN(sort) as sort FROM wm_knowledge k1 " +
            "WHERE is_deleted = 0 GROUP BY group_name ORDER BY MIN(sort) ASC")
    List<KnowledgeGroupDTO> selectKnowledgeGroups();

    /**
     * 根据分类名获取知识点列表
     */
    @Select("SELECT id, knowledge_id as knowledgeId, knowledge_name as knowledgeName, " +
            "group_name as groupName, is_free as isFree, sort, " +
            "DATE_FORMAT(created_at, '%Y-%m-%d %H:%i:%s') as createdAt, " +
            "(SELECT COUNT(*) FROM topic_bak t WHERE t.know_id = wm_knowledge.knowledge_id) as topicCount " +
            "FROM wm_knowledge WHERE group_name = #{groupName} AND is_deleted = 0 ORDER BY sort ASC")
    List<KnowledgeDTO> selectKnowledgePointsByGroup(@Param("groupName") String groupName);

    /**
     * 获取最大的knowledge_id
     */
    @Select("SELECT MAX(knowledge_id) FROM wm_knowledge")
    Integer selectMaxKnowledgeId();

    /**
     * 统计每个分组的知识点数量
     */
    @Select("SELECT group_name as groupName, COUNT(*) as count " +
            "FROM wm_knowledge WHERE is_deleted = 0 GROUP BY group_name")
    List<KnowledgeGroupDTO> countByGroups();

    /**
     * 获取知识点的题型统计
     */
    @Select("SELECT " +
            "SUM(CASE WHEN type = 'choice' THEN 1 ELSE 0 END) as singleChoice, " +
            "SUM(CASE WHEN type = 'multiple' THEN 1 ELSE 0 END) as multipleChoice, " +
            "SUM(CASE WHEN type = 'judge' THEN 1 ELSE 0 END) as judgment, " +
            "SUM(CASE WHEN type = 'fill' THEN 1 ELSE 0 END) as fillBlank, " +
            "SUM(CASE WHEN type = 'short' THEN 1 ELSE 0 END) as shortAnswer, " +
            "SUM(CASE WHEN type = 'subjective' THEN 1 ELSE 0 END) as subjective, " +
            "SUM(CASE WHEN type = 'group' THEN 1 ELSE 0 END) as groupQuestion " +
            "FROM topic_bak WHERE know_id = #{knowledgeId}")
    java.util.Map<String, Integer> selectKnowledgePointTypeStats(@Param("knowledgeId") Integer knowledgeId);

    /**
     * 根据知识点ID获取题目数量
     */
    @Select("SELECT COUNT(*) FROM topic_bak WHERE know_id = #{knowledgeId}")
    Integer selectTopicCountByKnowledgeId(@Param("knowledgeId") Integer knowledgeId);
}