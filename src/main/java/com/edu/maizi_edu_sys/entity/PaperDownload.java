package com.edu.maizi_edu_sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 试卷下载记录实体类
 */
@Data
@Entity
@Table(name = "paper_downloads")
@TableName("paper_downloads")
public class PaperDownload {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID - 对应数据库的bigint unsigned
     */
    @Column(name = "user_id", nullable = false, columnDefinition = "bigint unsigned")
    @TableField("user_id")
    private Long userId;

    /**
     * 试卷ID
     */
    @Column(name = "paper_id", nullable = false)
    @TableField("paper_id")
    private Long paperId;

    /**
     * 下载时间
     */
    @Column(name = "download_time", nullable = false)
    @TableField("download_time")
    private LocalDateTime downloadTime;

    /**
     * 下载IP地址
     */
    @Column(name = "ip_address")
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 下载格式：pdf, docx
     */
    @Column(name = "file_format", nullable = false)
    @TableField("file_format")
    private String fileFormat;
}