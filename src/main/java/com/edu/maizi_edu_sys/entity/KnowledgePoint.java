package com.edu.maizi_edu_sys.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
// import lombok.experimental.Accessors; // Uncomment if you use chaining setters

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
// @Accessors(chain = true) // Uncomment if you use chaining setters
@TableName("wm_knowledge") // 修正表名为实际的数据库表名
public class KnowledgePoint implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO) // Assuming auto-increment ID
    private Long id;

    @TableField("knowledge_id")
    private Integer knowledgeId;

    @TableField("knowledge_name")
    private String name;

    @TableField("group_name")
    private String groupName;

    @TableField("is_free")
    private Integer isFree;

    @TableField("sort")
    private Integer sort;

    @TableField("created_at")
    private LocalDateTime createTime;

    @TableLogic // For logical delete
    @TableField("is_deleted")
    private Boolean isDeleted;

    // Default constructor (Lombok @Data handles this if no other constructor is defined)
    // public KnowledgePoint() {}

    // Getters and setters are handled by Lombok @Data
}