package com.edu.maizi_edu_sys.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 试卷配置实体类
 */
@Entity
@Table(name = "paper_configs")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaperConfig {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", columnDefinition = "BIGINT UNSIGNED")
    private Long id;

    /**
     * 配置名称（用户自定义）
     */
    @Column(name = "config_name", nullable = false, length = 100)
    private String configName;

    /**
     * 配置描述
     */
    @Column(name = "description", length = 500)
    private String description;

    /**
     * 用户ID（配置所有者）
     */
    @Column(name = "user_id", nullable = false, columnDefinition = "BIGINT UNSIGNED")
    private Long userId;

    /**
     * 试卷标题模板
     */
    @Column(name = "title_template", length = 200)
    private String titleTemplate;

    /**
     * 试卷类型
     */
    @Column(name = "paper_type", length = 20)
    private String paperType = "standard";

    /**
     * 生成套数
     */
    @Column(name = "paper_count")
    private Integer paperCount = 1;

    /**
     * 单选题配置
     */
    @Column(name = "single_choice_count")
    private Integer singleChoiceCount = 0;

    @Column(name = "single_choice_score")
    private Integer singleChoiceScore = 0;

    /**
     * 多选题配置
     */
    @Column(name = "multiple_choice_count")
    private Integer multipleChoiceCount = 0;

    @Column(name = "multiple_choice_score")
    private Integer multipleChoiceScore = 0;

    /**
     * 判断题配置
     */
    @Column(name = "judgment_count")
    private Integer judgmentCount = 0;

    @Column(name = "judgment_score")
    private Integer judgmentScore = 0;

    /**
     * 填空题配置
     */
    @Column(name = "fill_count")
    private Integer fillCount = 0;

    @Column(name = "fill_score")
    private Integer fillScore = 0;

    /**
     * 简答题配置
     */
    @Column(name = "short_answer_count")
    private Integer shortAnswerCount = 0;

    @Column(name = "short_answer_score")
    private Integer shortAnswerScore = 0;

    /**
     * 难度分布配置（JSON格式）
     */
    @Column(name = "difficulty_distribution", columnDefinition = "TEXT")
    private String difficultyDistribution;

    /**
     * 知识点配置（JSON格式）
     */
    @Column(name = "knowledge_point_configs", columnDefinition = "TEXT")
    private String knowledgePointConfigs;

    /**
     * 是否为默认配置
     */
    @Column(name = "is_default")
    private Boolean isDefault = false;

    /**
     * 是否为公共配置（所有用户可见）
     */
    @Column(name = "is_public")
    private Boolean isPublic = false;

    /**
     * 使用次数
     */
    @Column(name = "usage_count")
    private Integer usageCount = 0;

    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 最后使用时间
     */
    @Column(name = "last_used_at")
    private LocalDateTime lastUsedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    /**
     * 计算总分
     */
    public Integer getTotalScore() {
        return (singleChoiceCount * singleChoiceScore) +
               (multipleChoiceCount * multipleChoiceScore) +
               (judgmentCount * judgmentScore) +
               (fillCount * fillScore) +
               (shortAnswerCount * shortAnswerScore);
    }

    /**
     * 计算总题数
     */
    public Integer getTotalQuestions() {
        return singleChoiceCount + multipleChoiceCount + judgmentCount + fillCount + shortAnswerCount;
    }
}
