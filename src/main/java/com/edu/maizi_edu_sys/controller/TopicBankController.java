package com.edu.maizi_edu_sys.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.edu.maizi_edu_sys.dto.TopicDTO;
import com.edu.maizi_edu_sys.dto.TopicQueryDTO;
import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.entity.dto.ApiResponse;
import com.edu.maizi_edu_sys.service.AuthService;
import com.edu.maizi_edu_sys.service.KnowledgePointService;
import com.edu.maizi_edu_sys.service.PermissionService;
import com.edu.maizi_edu_sys.service.TopicService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 题库管理控制器
 */
@Controller
@RequestMapping
@RequiredArgsConstructor
@Slf4j
public class TopicBankController {

    private final TopicService topicService;
    private final KnowledgePointService knowledgePointService;
    private final AuthService authService;
    private final PermissionService permissionService;

    /**
     * 题库管理页面
     */
    @GetMapping("/bank")
    public String topicBank(Model model) {
        log.info("访问题库管理页面");
        
        // 获取所有知识点用于筛选
        try {
            List<?> knowledgePoints = knowledgePointService.getAllSimpleKnowledgePoints();
            model.addAttribute("knowledgePoints", knowledgePoints);
        } catch (Exception e) {
            log.error("获取知识点列表失败", e);
            model.addAttribute("knowledgePoints", Collections.emptyList());
        }
        
        return "topics/topic-bank";
    }

    /**
     * 题目编辑页面
     */
    @GetMapping("/edit/{id}")
    public String editTopic(@PathVariable Long id, Model model) {
        log.info("访问题目编辑页面，题目ID: {}", id);
        
        try {
            Topic topic = topicService.getById(id);
            if (topic == null) {
                model.addAttribute("error", "题目不存在");
                return "error/404";
            }
            
            model.addAttribute("topic", topic);
            
            // 获取所有知识点用于选择
            List<?> knowledgePoints = knowledgePointService.getAllSimpleKnowledgePoints();
            model.addAttribute("knowledgePoints", knowledgePoints);
            
        } catch (Exception e) {
            log.error("获取题目信息失败", e);
            model.addAttribute("error", "获取题目信息失败");
            return "error/500";
        }
        
        return "topics/topic-edit";
    }

    /**
     * 新增题目页面
     */
    @GetMapping("/add")
    public String addTopic(Model model) {
        log.info("访问新增题目页面");
        
        try {
            // 获取所有知识点用于选择
            List<?> knowledgePoints = knowledgePointService.getAllSimpleKnowledgePoints();
            model.addAttribute("knowledgePoints", knowledgePoints);
        } catch (Exception e) {
            log.error("获取知识点列表失败", e);
            model.addAttribute("knowledgePoints", Collections.emptyList());
        }
        
        return "topics/topic-add";
    }

    /**
     * 分页查询题目列表
     */
    @GetMapping("/api/topics")
    @ResponseBody
    public ResponseEntity<ApiResponse<IPage<Topic>>> getTopicList(TopicQueryDTO queryDTO) {
        try {
            log.debug("查询题目列表，参数: {}", queryDTO);
            IPage<Topic> topicPage = topicService.listTopics(queryDTO);
            return ResponseEntity.ok(ApiResponse.success(topicPage));
        } catch (Exception e) {
            log.error("查询题目列表失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("查询题目列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取题目详情
     */
    @GetMapping("/api/topics/{id}")
    @ResponseBody
    public ResponseEntity<ApiResponse<Topic>> getTopicDetail(@PathVariable Long id) {
        try {
            Topic topic = topicService.getById(id);
            if (topic == null) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("题目不存在"));
            }
            return ResponseEntity.ok(ApiResponse.success(topic));
        } catch (Exception e) {
            log.error("获取题目详情失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取题目详情失败: " + e.getMessage()));
        }
    }

    /**
     * 保存题目（新增或更新）
     */
    @PostMapping("/api/topics/save")
    @ResponseBody
    public ResponseEntity<ApiResponse<String>> saveTopic(@Valid @RequestBody TopicDTO topicDTO) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("用户未登录"));
            }

            // 检查权限
            if (topicDTO.getId() != null) {
                // 更新操作，检查编辑权限
                if (!permissionService.canEditTopic(currentUserId)) {
                    return ResponseEntity.badRequest()
                            .body(ApiResponse.error("您没有编辑题目的权限"));
                }
            } else {
                // 新增操作，检查每日上传限制
                if (!permissionService.isAdmin(currentUserId)) {
                    // 非管理员检查每日上传限制
                    long todayUploadCount = topicService.getTodayUploadCount(currentUserId);
                    if (todayUploadCount >= 5000) {
                        return ResponseEntity.badRequest()
                                .body(ApiResponse.error("您今日上传题目数量已达上限（5000道）"));
                    }
                }
            }

            topicService.saveOrUpdateTopic(topicDTO, currentUserId);
            
            String message = topicDTO.getId() != null ? "题目更新成功" : "题目添加成功";
            return ResponseEntity.ok(ApiResponse.success(message));
            
        } catch (Exception e) {
            log.error("保存题目失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("保存题目失败: " + e.getMessage()));
        }
    }

    /**
     * 删除题目
     */
    @DeleteMapping("/api/topics/{id}")
    @ResponseBody
    public ResponseEntity<ApiResponse<String>> deleteTopic(@PathVariable Long id) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("用户未登录"));
            }

            // 检查删除权限（只有管理员可以删除）
            if (!permissionService.isAdmin(currentUserId)) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("您没有删除题目的权限"));
            }

            boolean success = topicService.removeById(id);
            if (success) {
                return ResponseEntity.ok(ApiResponse.success("题目删除成功"));
            } else {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("题目删除失败"));
            }
            
        } catch (Exception e) {
            log.error("删除题目失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("删除题目失败: " + e.getMessage()));
        }
    }

    /**
     * 批量删除题目
     */
    @DeleteMapping("/api/topics/batch")
    @ResponseBody
    public ResponseEntity<ApiResponse<String>> batchDeleteTopics(@RequestBody List<Long> ids) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("用户未登录"));
            }

            // 检查删除权限（只有管理员可以删除）
            if (!permissionService.isAdmin(currentUserId)) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("您没有删除题目的权限"));
            }

            boolean success = topicService.removeByIds(ids);
            if (success) {
                return ResponseEntity.ok(ApiResponse.success("批量删除成功"));
            } else {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("批量删除失败"));
            }
            
        } catch (Exception e) {
            log.error("批量删除题目失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("批量删除失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户今日上传统计
     */
    @GetMapping("/api/topics/upload-stats")
    @ResponseBody
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUploadStats() {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("用户未登录"));
            }

            Map<String, Object> stats = topicService.getUserUploadStats(currentUserId);
            return ResponseEntity.ok(ApiResponse.success(stats));
            
        } catch (Exception e) {
            log.error("获取上传统计失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取上传统计失败: " + e.getMessage()));
        }
    }
}