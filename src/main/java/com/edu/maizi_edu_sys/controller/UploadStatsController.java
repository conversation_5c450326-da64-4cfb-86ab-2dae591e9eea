package com.edu.maizi_edu_sys.controller;

import com.edu.maizi_edu_sys.entity.dto.ApiResponse;
import com.edu.maizi_edu_sys.service.AuthService;
import com.edu.maizi_edu_sys.service.PermissionService;
import com.edu.maizi_edu_sys.service.UserUploadStatsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 上传统计控制器
 */
@RestController
@RequestMapping("/api/upload-stats")
@RequiredArgsConstructor
@Slf4j
public class UploadStatsController {

    private final UserUploadStatsService userUploadStatsService;
    private final AuthService authService;
    private final PermissionService permissionService;

    /**
     * 获取当前用户上传统计概览
     */
    @GetMapping("/overview")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUserUploadOverview() {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("用户未登录"));
            }

            Map<String, Object> overview = userUploadStatsService.getUserUploadOverview(currentUserId);
            return ResponseEntity.ok(ApiResponse.success(overview));
            
        } catch (Exception e) {
            log.error("获取用户上传统计概览失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取统计数据失败: " + e.getMessage()));
        }
    }

    /**
     * 获取当前用户上传趋势
     */
    @GetMapping("/trend")
    public ResponseEntity<ApiResponse<?>> getUserUploadTrend(@RequestParam(defaultValue = "7") Integer days) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("用户未登录"));
            }

            if (days <= 0 || days > 365) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("天数参数无效，应在1-365之间"));
            }

            List<Map<String, Object>> trend = userUploadStatsService.getUserUploadTrend(currentUserId, days);
            return ResponseEntity.ok(ApiResponse.success(trend));
            
        } catch (Exception e) {
            log.error("获取用户上传趋势失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取趋势数据失败: " + e.getMessage()));
        }
    }

    /**
     * 获取当前用户指定日期范围的上传统计
     */
    @GetMapping("/range")
    public ResponseEntity<ApiResponse<?>> getUserUploadStatsByRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("用户未登录"));
            }

            if (startDate.isAfter(endDate)) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("开始日期不能晚于结束日期"));
            }

            if (startDate.isBefore(LocalDate.now().minusYears(1))) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("查询范围不能超过一年"));
            }

            List<Map<String, Object>> stats = userUploadStatsService.getUserUploadStatsByDateRange(currentUserId, startDate, endDate);
            return ResponseEntity.ok(ApiResponse.success(stats));
            
        } catch (Exception e) {
            log.error("获取用户日期范围上传统计失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取统计数据失败: " + e.getMessage()));
        }
    }

    /**
     * 检查用户是否超过每日上传限制
     */
    @GetMapping("/check-limit")
    public ResponseEntity<ApiResponse<Map<String, Object>>> checkDailyLimit(@RequestParam Integer count) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("用户未登录"));
            }

            if (count <= 0) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("数量参数无效"));
            }

            // 检查是否为管理员
            boolean isAdmin = permissionService.isAdmin(currentUserId);
            if (isAdmin) {
                Map<String, Object> result = new HashMap<>();
                result.put("canUpload", true);
                result.put("isAdmin", true);
                result.put("message", "管理员无法上传");
                return ResponseEntity.ok(ApiResponse.success(result));
            }

            boolean exceedLimit = userUploadStatsService.isExceedDailyLimit(currentUserId, count);
            Integer todayCount = userUploadStatsService.getUserTodayUploadCount(currentUserId);

            Map<String, Object> result = new HashMap<>();
                result.put("canUpload", !exceedLimit);
                result.put("isAdmin", false);
                result.put("todayCount", todayCount);
                result.put("requestCount", count);
                result.put("dailyLimit", 5000);
                result.put("remaining", Math.max(0, 5000 - todayCount));
                result.put("message", exceedLimit ? "超过每日上传限制" : "可以上传");


            return ResponseEntity.ok(ApiResponse.success(result));
            
        } catch (Exception e) {
            log.error("检查每日上传限制失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("检查限制失败: " + e.getMessage()));
        }
    }

    /**
     * 获取管理员统计概览（仅管理员可访问）
     */
    @GetMapping("/admin/overview")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getAdminUploadOverview() {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("用户未登录"));
            }

            if (!permissionService.isAdmin(currentUserId)) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("权限不足，仅管理员可访问"));
            }

            Map<String, Object> overview = userUploadStatsService.getAdminUploadOverview();
            return ResponseEntity.ok(ApiResponse.success(overview));
            
        } catch (Exception e) {
            log.error("获取管理员上传统计概览失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取统计数据失败: " + e.getMessage()));
        }
    }

    /**
     * 获取全站上传趋势（仅管理员可访问）
     */
    @GetMapping("/admin/trend")
    public ResponseEntity<ApiResponse<?>> getTotalUploadTrend(@RequestParam(defaultValue = "30") Integer days) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("用户未登录"));
            }

            if (!permissionService.isAdmin(currentUserId)) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("权限不足，仅管理员可访问"));
            }

            if (days <= 0 || days > 365) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("天数参数无效，应在1-365之间"));
            }

            List<Map<String, Object>> trend = userUploadStatsService.getTotalUploadTrend(days);
            return ResponseEntity.ok(ApiResponse.success(trend));
            
        } catch (Exception e) {
            log.error("获取全站上传趋势失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取趋势数据失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户上传排行榜（仅管理员可访问）
     */
    @GetMapping("/admin/ranking")
    public ResponseEntity<ApiResponse<?>> getUserUploadRanking(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(defaultValue = "20") Integer limit) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("用户未登录"));
            }

            if (!permissionService.isAdmin(currentUserId)) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("权限不足，仅管理员可访问"));
            }

            if (startDate.isAfter(endDate)) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("开始日期不能晚于结束日期"));
            }

            if (limit <= 0 || limit > 100) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("限制数量参数无效，应在1-100之间"));
            }

            List<Map<String, Object>> ranking = userUploadStatsService.getUserUploadRanking(startDate, endDate, limit);
            return ResponseEntity.ok(ApiResponse.success(ranking));
            
        } catch (Exception e) {
            log.error("获取用户上传排行榜失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取排行榜数据失败: " + e.getMessage()));
        }
    }

    /**
     * 获取指定日期范围的全站上传统计（仅管理员可访问）
     */
    @GetMapping("/admin/range")
    public ResponseEntity<ApiResponse<?>> getTotalUploadStatsByRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("用户未登录"));
            }

            if (!permissionService.isAdmin(currentUserId)) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("权限不足，仅管理员可访问"));
            }

            if (startDate.isAfter(endDate)) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("开始日期不能晚于结束日期"));
            }

            if (startDate.isBefore(LocalDate.now().minusYears(1))) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("查询范围不能超过一年"));
            }

            List<Map<String, Object>> stats = userUploadStatsService.getTotalUploadStatsByDateRange(startDate, endDate);
            return ResponseEntity.ok(ApiResponse.success(stats));
            
        } catch (Exception e) {
            log.error("获取全站日期范围上传统计失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取统计数据失败: " + e.getMessage()));
        }
    }
}
