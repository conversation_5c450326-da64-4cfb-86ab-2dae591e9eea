package com.edu.maizi_edu_sys.controller;

import com.edu.maizi_edu_sys.entity.User;
import com.edu.maizi_edu_sys.entity.dto.ApiResponse;
import com.edu.maizi_edu_sys.entity.dto.LoginRequest;
import com.edu.maizi_edu_sys.entity.dto.RegisterRequest;
import com.edu.maizi_edu_sys.service.UserService;
import com.edu.maizi_edu_sys.util.JwtUtil;
import com.edu.maizi_edu_sys.util.RequestUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.*;

@Validated
@RestController
@RequestMapping("/api/user")
@RequiredArgsConstructor
@Slf4j
public class UserController {

    private final UserService userService;
    private final HttpServletRequest request;
    private final JwtUtil jwtUtil;

    @PostMapping("/login")
    public ApiResponse<?> login(@Valid @RequestBody LoginRequest loginRequest) {
        // 获取客户端IP
        String clientIp = RequestUtil.getClientIp(request);
        loginRequest.setIp(clientIp);
        log.info("Login attempt from IP: {}, username: {}", clientIp, loginRequest.getUsername());
        return userService.login(loginRequest);
    }

    @PostMapping("/register")
    public ApiResponse<String> register(@Valid @RequestBody RegisterRequest request) {
        log.info("User registration attempt for username: {}", request.getUsername());
        return userService.register(request);
    }

    @PostMapping("/logout")
    public ApiResponse<String> logout(@NotBlank(message = "Token不能为空")
                                    @RequestHeader("Authorization") String token) {
        try {
            String extractedToken = RequestUtil.extractTokenFromHeader(token);
            log.info("Logout attempt with token: {}", RequestUtil.maskToken(extractedToken));
            return userService.logout(extractedToken);
        } catch (Exception e) {
            log.error("Logout failed: {}", e.getMessage());
            return ApiResponse.error("退出失败：" + e.getMessage());
        }
    }

    @GetMapping("/current")
    public ApiResponse<?> getCurrentUser(@RequestHeader(value = "Authorization", required = false) String tokenHeader) {
        log.info("Endpoint /api/user/current called");

        String token = RequestUtil.extractTokenFromHeader(tokenHeader);
        if (token.isEmpty()) {
            log.warn("Authorization header is missing or empty");
            return ApiResponse.error("未提供认证信息");
        }

        log.debug("Processing token: {}", RequestUtil.maskToken(token));

        try {
            // Validate the token first (this will throw exceptions if invalid)
            if (!jwtUtil.validateToken(token)) {
                 // jwtUtil.validateToken already logs the specific error
                log.warn("Token validation failed");
                return ApiResponse.error("token无效或已过期");
            }

            String username = jwtUtil.getUsernameFromToken(token);
            log.info("Username '{}' extracted from valid token", username);
            User user = userService.getByUsername(username);

            if (user == null) {
                log.warn("User not found in database for username: {}", username);
                return ApiResponse.error("用户不存在");
            }
            log.info("Successfully retrieved current user: {}", username);

            // Add debug logging for avatar path
            if (user.getAvatar() != null) {
                log.debug("User avatar path: {}", user.getAvatar());
            }

            return ApiResponse.success(user);

        } catch (io.jsonwebtoken.security.SignatureException se) {
            log.error("TOKEN SIGNATURE EXCEPTION: {}", se.getMessage());
            return ApiResponse.error("token签名无效");
        } catch (io.jsonwebtoken.ExpiredJwtException eje) {
            log.error("TOKEN EXPIRED EXCEPTION: {}", eje.getMessage());
            return ApiResponse.error("token已过期");
        } catch (Exception e) {
            log.error("Unexpected error processing token: {}", e.getMessage(), e);
            return ApiResponse.error("token处理错误");
        }
    }

    @PostMapping("/avatar")
    public ApiResponse<String> updateAvatar(@RequestParam("file") MultipartFile file,
                                          @RequestHeader("Authorization") String tokenHeader) {
        String token = RequestUtil.extractTokenFromHeader(tokenHeader);
        log.info("Avatar update requested for token: {}", RequestUtil.maskToken(token));
        return userService.updateAvatar(file, token);
    }

    /**
     * 验证token有效性（专用接口）
     *
     * @return Token验证结果
     */
    @GetMapping("/validate")
    public ApiResponse<?> validateToken(@RequestHeader(value = "Authorization", required = false) String tokenHeader) {
        try {
            log.info("Token验证请求 - 验证接口(/validate)被调用");

            String token = RequestUtil.extractTokenFromHeader(tokenHeader);
            // 检查token是否存在
            if (token.isEmpty()) {
                log.warn("Token验证失败 - token为空");
                return ApiResponse.error("未提供token");
            }

            // 验证token
            try {
                if (jwtUtil.validateToken(token)) {
                    // 获取用户名
                    String username = jwtUtil.getUsernameFromToken(token);
                    log.info("Token验证成功 - 用户: {}", username);

                    // 获取用户信息（可选，验证用户是否存在）
                    User user = userService.getByUsername(username);
                    if (user == null) {
                        log.warn("Token验证失败 - 用户不存在: {}", username);
                        return ApiResponse.error("用户不存在");
                    }

                    return ApiResponse.success("Token有效");
                } else {
                    log.warn("Token验证失败 - JWT验证未通过");
                    return ApiResponse.error("Token无效");
                }
            } catch (io.jsonwebtoken.ExpiredJwtException e) {
                log.warn("Token验证失败 - JWT已过期: {}", e.getMessage());
                return ApiResponse.error("Token已过期");
            } catch (io.jsonwebtoken.security.SignatureException e) {
                log.warn("Token验证失败 - JWT签名无效: {}", e.getMessage());
                return ApiResponse.error("Token签名无效");
            } catch (Exception e) {
                log.error("Token验证失败 - 处理过程中发生错误: {}", e.getMessage(), e);
                return ApiResponse.error("Token验证失败: " + e.getMessage());
            }
        } catch (Exception e) {
            log.error("验证token时发生未预期错误", e);
            return ApiResponse.error("Token验证过程中发生错误");
        }
    }

    /**
     * 获取用户信息 - 兼容前端调用 /api/user/info
     * @param tokenHeader 认证token
     * @return 用户信息
     */
    @GetMapping("/info")
    public ApiResponse<?> getUserInfo(@RequestHeader(value = "Authorization", required = false) String tokenHeader) {
        log.info("Endpoint /api/user/info called (redirecting to /current)");
        // 直接调用现有的 getCurrentUser 方法
        return getCurrentUser(tokenHeader);
    }

    /**
     * 更新用户信息
     * @param updateRequest 更新请求
     * @param tokenHeader 认证token
     * @return 更新结果
     */
    @PutMapping("/profile")
    public ApiResponse<?> updateProfile(@RequestBody UserUpdateRequest updateRequest,
                                       @RequestHeader("Authorization") String tokenHeader) {
        String token = RequestUtil.extractTokenFromHeader(tokenHeader);
        log.info("用户信息更新请求: {}", updateRequest);
        return userService.updateProfile(updateRequest, token);
    }

    /**
     * 用户信息更新请求DTO
     */
    public static class UserUpdateRequest {
        private String email;
        private String phone;
        private String bio;

        // Getters and Setters
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
        public String getBio() { return bio; }
        public void setBio(String bio) { this.bio = bio; }

        @Override
        public String toString() {
            return "UserUpdateRequest{" +
                    "email='" + email + '\'' +
                    ", phone='" + phone + '\'' +
                    ", bio='" + bio + '\'' +
                    '}';
        }
    }

    /**
     * 检查用户数据 - 调试用
     */
    @GetMapping("/debug/users")
    public ApiResponse<?> debugUsers() {
        try {
            List<User> users = userService.list();
            Map<String, Object> result = new HashMap<>();
            result.put("totalUsers", users.size());
            result.put("users", users.stream().map(user -> {
                Map<String, Object> userInfo = new HashMap<>();
                userInfo.put("id", user.getId());
                userInfo.put("username", user.getUsername());
                userInfo.put("email", user.getEmail());
                userInfo.put("role", user.getRole());
                userInfo.put("status", user.getStatus());
                userInfo.put("deleted", user.getDeleted());
                return userInfo;
            }).collect(java.util.stream.Collectors.toList()));

            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取用户调试信息失败", e);
            return ApiResponse.error("获取用户信息失败: " + e.getMessage());
        }
    }
}
