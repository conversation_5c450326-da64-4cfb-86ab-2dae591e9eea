package com.edu.maizi_edu_sys.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.edu.maizi_edu_sys.entity.dto.ApiResponse;
import com.edu.maizi_edu_sys.dto.TopicDTO;
import com.edu.maizi_edu_sys.dto.TopicQueryDTO;
import com.edu.maizi_edu_sys.entity.Topic;

import org.springframework.beans.BeanUtils;
import com.edu.maizi_edu_sys.service.TopicService;
import com.edu.maizi_edu_sys.service.TopicAuditService;
import com.edu.maizi_edu_sys.service.AuthService;
import com.edu.maizi_edu_sys.entity.TopicAudit;
import com.edu.maizi_edu_sys.mapper.TopicAuditMapper;
import com.edu.maizi_edu_sys.util.RequestUtil;
import com.edu.maizi_edu_sys.util.TopicTypeMapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/api/topics")
@Slf4j
public class TopicController {

    private static final int MAX_PAGE_SIZE = 100;
    private static final int DEFAULT_PAGE_SIZE = 10;

    @Autowired
    private TopicService topicService;

    @Autowired
    private TopicAuditService topicAuditService;

    @Autowired
    private AuthService authService;

    @Autowired
    private ObjectMapper objectMapper;  // Spring Boot auto-configures this bean

    /**
     * 获取指定知识点题型可用性信息
     * 
     * @param request 包含知识点IDs的请求体
     * @return 各题型的数量统计信息
     */
    @PostMapping("/availability")
    public ApiResponse<?> getTopicTypeAvailability(@RequestBody Map<String, List<Long>> request) {
        try {
            log.info("Topic availability API called with request: {}", request);
            
            List<Long> knowledgePointIds = request.get("knowledgePointIds");
            if (knowledgePointIds == null || knowledgePointIds.isEmpty()) {
                log.warn("Empty or null knowledgePointIds in request");
                return ApiResponse.error("知识点ID不能为空");
            }
            
            log.info("Fetching topic type availability for {} knowledge points: {}", 
                     knowledgePointIds.size(), knowledgePointIds);
            
            // 将Long类型转换为Integer类型
            List<Integer> knowledgeIds = knowledgePointIds.stream()
                    .map(Long::intValue)
                    .collect(Collectors.toList());
            
            // 添加边界条件检查
            if (knowledgeIds.isEmpty()) {
                log.warn("Empty knowledge IDs after conversion");
                return ApiResponse.error("转换后的知识点ID列表为空");
            }
            
            log.debug("Querying database for topic counts by type for IDs: {}", knowledgeIds);
            List<Map<String, Object>> rawCounts;
            try {
                rawCounts = topicService.countTopicsByTypeForKnowledgePoints(knowledgeIds);
                log.debug("Service call result for topic counts: {}", rawCounts);
            } catch (Exception e) {
                log.error("Database error while counting topics: {}", e.getMessage(), e);
                return ApiResponse.error("数据库查询失败: " + e.getMessage());
            }
            
            if (rawCounts == null || rawCounts.isEmpty()) {
                log.info("No topics found for the given knowledge points");
                // 返回空数据而不是错误
                Map<String, Object> emptyResult = new HashMap<>();
                emptyResult.put("typeAvailability", new HashMap<>());
                emptyResult.put("totalAvailableTopics", 0);
                return ApiResponse.success(emptyResult);
            }
            
            // 转换为前端友好的格式
            Map<String, Integer> typeAvailability = new HashMap<>();
            int totalAvailableTopics = 0;
            
            for (Map<String, Object> rawCount : rawCounts) {
                String type = (String) rawCount.get("type");
                if (type == null || type.trim().isEmpty()) {
                    log.warn("Null or empty type in database result: {}", rawCount);
                    continue;
                }
                
                Number countObj = (Number) rawCount.get("count");
                if (countObj == null) {
                    log.warn("Null count for type {}", type);
                    continue;
                }
                
                long count = countObj.longValue();
                
                // 标准化题型键名 - 使用统一的TopicTypeMapper
                String normalizedType = TopicTypeMapper.toDbFormat(type);
                log.debug("Type mapping: '{}' -> '{}'", type, normalizedType);
                
                // 合并相同标准化类型的计数
                typeAvailability.put(normalizedType, 
                                     typeAvailability.getOrDefault(normalizedType, 0) + (int)count);
                                     
                totalAvailableTopics += (int)count;
            }
            
            log.info("Topic type availability result: {}, total available topics: {}", 
                     typeAvailability, totalAvailableTopics);
            
            // 构建响应数据
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("typeAvailability", typeAvailability);
            responseData.put("totalAvailableTopics", totalAvailableTopics);
            
            return ApiResponse.success(responseData);
        } catch (Exception e) {
            log.error("获取题型可用性时出错: {}", e.getMessage(), e);
            return ApiResponse.error("获取题型可用性失败：" + e.getMessage());
        }
    }

    @PostMapping("/upload")
    public ApiResponse<?> uploadTopics(@RequestBody List<TopicDTO> topics,
                                     @RequestHeader(value = "Authorization", required = false) String tokenHeader) {
        try {
            if (topics == null || topics.isEmpty()) {
                log.warn("Empty topic list submitted for upload");
                return ApiResponse.error("题目列表不能为空");
            }

            log.info("Received topic upload request with {} topics", topics.size());

            // 获取当前用户ID
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                log.warn("User not authenticated for topic upload");
                return ApiResponse.error("用户未登录，请先登录");
            }

            // 检查用户今日提交数量限制（可选）
            Long todaySubmissionCount = topicAuditService.getUserTodaySubmissionCount(currentUserId);
            final int DAILY_LIMIT = 50; // 每日提交限制
            if (todaySubmissionCount + topics.size() > DAILY_LIMIT) {
                log.warn("User {} exceeded daily submission limit: current={}, attempting={}, limit={}",
                        currentUserId, todaySubmissionCount, topics.size(), DAILY_LIMIT);
                return ApiResponse.error(String.format("超出每日提交限制，今日已提交%d道题目，限制%d道",
                        todaySubmissionCount, DAILY_LIMIT));
            }

            // 提交到审核队列而不是直接保存到题库
            topicAuditService.submitTopicsForAudit(topics, currentUserId);
            log.info("Successfully submitted {} topics for audit by user {}", topics.size(), currentUserId);

            return ApiResponse.success(String.format("成功提交%d道题目到审核队列，请等待管理员审核", topics.size()));
        } catch (IllegalArgumentException e) {
            log.warn("Invalid topic data: {}", e.getMessage());
            return ApiResponse.error(e.getMessage());
        } catch (Exception e) {
            log.error("Error uploading topics: {}", e.getMessage(), e);
            return ApiResponse.error("服务器内部错误：" + e.getMessage());
        }
    }

    // 题目列表查询功能已移至 TopicBankController.getTopicList()
    // 该方法包含完整的权限验证和更好的错误处理

    @GetMapping("/{id}")
    public ApiResponse<?> getTopicById(@PathVariable Integer id) {
        try {
            if (id == null || id <= 0) {
                log.warn("Invalid topic ID requested: {}", id);
                return ApiResponse.error("无效的题目ID");
            }
            
            log.info("Topic details requested for ID: {}", id);
            Topic topic = topicService.getById(id);

            if (topic == null) {
                log.warn("Topic not found for ID: {}", id);
                return ApiResponse.error("题目不存在");
            }
            
            TopicDTO dto = convertToDTO(topic);
            // Ensure convertToDTO is properly implemented
            log.info("Successfully retrieved topic with ID: {}", id);
            return ApiResponse.success(dto);
        } catch (Exception e) {
            log.error("Error getting topic with id {}: {}", id, e.getMessage(), e);
            return ApiResponse.error("查询失败：" + e.getMessage());
        }
    }

    // 删除题目功能已移至 TopicBankController.deleteTopic()
    // 该方法包含完整的权限验证和安全检查

    @PutMapping("/{id}")
    public ApiResponse<?> updateTopic(@PathVariable Integer id, @RequestBody TopicDTO topicDTO) {
        try {
            if (id == null || id <= 0) {
                log.warn("Invalid topic ID for update: {}", id);
                return ApiResponse.error("无效的题目ID");
            }
            
            if (topicDTO == null) {
                log.warn("Null topic data submitted for update to ID: {}", id);
                return ApiResponse.error("题目数据不能为空");
            }
            
            log.info("Update topic requested for ID: {}", id);
            
            Topic existingTopic = topicService.getById(id);
            if (existingTopic == null) {
                log.warn("Topic not found for update with ID: {}", id);
                return ApiResponse.error("题目不存在");
            }
            
            // 验证题目数据
            validateTopicDTO(topicDTO);
            convertToEntity(topicDTO, existingTopic); // Now this method modifies existingTopic directly
            
            boolean success = topicService.updateById(existingTopic);
            if (success) {
                log.info("Successfully updated topic with ID: {}", id);
                return ApiResponse.success("更新成功");
            } else {
                log.warn("Failed to update topic with ID: {}", id);
                return ApiResponse.error("更新失败");
            }
        } catch (IllegalArgumentException e) {
            log.warn("Invalid topic data for update to ID {}: {}", id, e.getMessage());
            return ApiResponse.error(e.getMessage());
        } catch (Exception e) {
            log.error("Error updating topic with id {}: {}", id, e.getMessage(), e);
            return ApiResponse.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 获取题目上传统计数据
     *
     * @param type      统计类型 ('day', 'week', 'month')
     * @param startDate 开始日期 (对于 'day' 类型是必需的)
     * @param endDate   结束日期 (对于 'day' 类型是必需的)
     * @return 包含日期/周期和对应题目数量的列表
     */
    @GetMapping("/statistics")
    public ApiResponse<List<Map<String, Object>>> getTopicStatistics(
            @RequestParam(defaultValue = "day") String type,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {

        if (!java.util.Arrays.asList("day", "week", "month").contains(type)) {
            return ApiResponse.error("无效的统计类型。有效值为 'day', 'week', 'month'。");
        }

        // 如果按天查询但未提供日期，则默认为最近7天
        if ("day".equals(type) && (startDate == null || endDate == null)) {
            endDate = LocalDate.now();
            startDate = endDate.minusDays(6);
        }

        try {
            List<Map<String, Object>> stats = topicService.getTopicStatistics(type, startDate, endDate);
            return ApiResponse.success(stats);
        } catch (Exception e) {
            log.error("获取题目统计数据时出错: type={}, startDate={}, endDate={}", type, startDate, endDate, e);
            return ApiResponse.error("获取统计数据失败: " + e.getMessage());
        }
    }

    // 将DTO转换为实体
    private void validateTopicDTO(TopicDTO topic) {
        if (topic == null) {
            throw new IllegalArgumentException("题目数据不能为空");
        }
        if (topic.getKnowId() == null) {
            throw new IllegalArgumentException("知识点ID不能为空");
        }
        if (topic.getTitle() == null || topic.getTitle().trim().isEmpty()) {
            throw new IllegalArgumentException("题目标题不能为空");
        }
        if (topic.getType() == null || topic.getType().trim().isEmpty()) {
            throw new IllegalArgumentException("题目类型不能为空");
        }
        if (("choice".equals(topic.getType()) || "multiple".equals(topic.getType()))
                && (topic.getOptions() == null || topic.getOptions().isEmpty())) {
            throw new IllegalArgumentException("选择题必须包含选项");
        }
        if ("judge".equals(topic.getType()) && topic.getAnswer() != null
                && !("是".equals(topic.getAnswer()) || "否".equals(topic.getAnswer()))) {
            throw new IllegalArgumentException("判断题答案必须是'是'或'否'");
        }
        if (("choice".equals(topic.getType()) || "multiple".equals(topic.getType()))
                && topic.getAnswer() != null) {
            if (!topic.getAnswer().matches("^[A-Z]+$")) {
                throw new IllegalArgumentException("选择题答案必须是大写字母");
            }
            String sortedAnswer = topic.getAnswer().chars()
                    .mapToObj(ch -> String.valueOf((char) ch))
                    .sorted()
                    .reduce("", String::concat);
            if (!topic.getAnswer().equals(sortedAnswer)) {
                throw new IllegalArgumentException("选择题答案必须按字母顺序排列");
            }
            for (char answerChar : topic.getAnswer().toCharArray()) {
                boolean found = false;
                if (topic.getOptions() != null) {
                    for (TopicDTO.OptionDTO option : topic.getOptions()) {
                        if (option.getKey().equals(String.valueOf(answerChar))) {
                            found = true;
                            break;
                        }
                    }
                }
                if (!found) {
                    throw new IllegalArgumentException("答案中的选项 " + answerChar + " 在选项列表中不存在");
                }
            }
        }
    }

    // 将DTO转换为实体 (更新现有实体)
    private void convertToEntity(TopicDTO dto, Topic topicToUpdate) {
        if (dto == null || topicToUpdate == null) return;

        topicToUpdate.setKnowId(dto.getKnowId());
        topicToUpdate.setType(dto.getType());
        topicToUpdate.setTitle(dto.getTitle());
        topicToUpdate.setTags(dto.getTags());
        topicToUpdate.setSubs(dto.getSubs());
        topicToUpdate.setAnswer(dto.getAnswer());
        topicToUpdate.setParse(dto.getParse());
        topicToUpdate.setScore(dto.getScore());
        topicToUpdate.setSource(dto.getSource());
        topicToUpdate.setSort(dto.getSort());
        topicToUpdate.setDifficulty(dto.getDifficulty());

        if (dto.getOptions() != null && !dto.getOptions().isEmpty()) {
            try {
                topicToUpdate.setOptions(objectMapper.writeValueAsString(dto.getOptions()));
            } catch (Exception e) {
                log.error("Error serializing options to JSON: {}", e.getMessage());
                // Decide if this should throw an error or just log
            }
        } else {
            topicToUpdate.setOptions(null); // Or "[]" if preferred for empty options
        }
        // createdAt is usually handled by @TableField(fill = FieldFill.INSERT)
    }

    private TopicDTO convertToDTO(Topic topic) {
        if (topic == null) return null;
        TopicDTO dto = new TopicDTO();
        BeanUtils.copyProperties(topic, dto); // Basic properties

        if (topic.getOptions() != null && !topic.getOptions().isEmpty()) {
            try {
                dto.setOptions(objectMapper.readValue(topic.getOptions(), new com.fasterxml.jackson.core.type.TypeReference<List<TopicDTO.OptionDTO>>() {}));
            } catch (Exception e) {
                log.error("Error deserializing options from JSON for topic ID {}: {}", topic.getId(), e.getMessage());
                dto.setOptions(new java.util.ArrayList<>());
            }
        }
        return dto;
    }

    /**
     * 获取用户的审核记录
     */
    @GetMapping("/audit/my")
    public ApiResponse<IPage<TopicAudit>> getMyAuditList(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "20") int pageSize,
            @RequestParam(required = false) Integer auditStatus) {

        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            IPage<TopicAudit> auditList = topicAuditService.getUserAuditList(currentUserId, pageNum, pageSize, auditStatus);
            return ApiResponse.success(auditList);
        } catch (Exception e) {
            log.error("获取用户审核记录失败", e);
            return ApiResponse.error("获取审核记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户审核统计
     */
    @GetMapping("/audit/my/stats")
    public ApiResponse<TopicAuditMapper.TopicAuditStats> getMyAuditStats() {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            TopicAuditMapper.TopicAuditStats stats = topicAuditService.getUserAuditStats(currentUserId);
            return ApiResponse.success(stats);
        } catch (Exception e) {
            log.error("获取用户审核统计失败", e);
            return ApiResponse.error("获取审核统计失败: " + e.getMessage());
        }
    }
}