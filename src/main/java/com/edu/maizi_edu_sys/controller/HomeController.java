package com.edu.maizi_edu_sys.controller;

import com.edu.maizi_edu_sys.entity.UserActivityLog;
import com.edu.maizi_edu_sys.entity.dto.ApiResponse;
import com.edu.maizi_edu_sys.service.UserActivityLogService;
import com.edu.maizi_edu_sys.util.UserContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 首页控制器
 */
@Slf4j
@Controller
public class HomeController {

    private UserActivityLogService activityLogService;

    // 使用可选的依赖注入，避免服务不存在时启动失败
    public HomeController() {
        try {
            // 尝试获取服务，如果不存在则为null
        } catch (Exception e) {
            log.warn("UserActivityLogService 不可用，将使用模拟数据");
        }
    }
    
    /**
     * 首页
     */
    @GetMapping({"/", "/index", "/index.html"})
    public String index(Model model) {
        log.info("访问首页");

        // 记录访问首页的日志（如果服务可用）
        try {
            if (activityLogService != null) {
                activityLogService.logActivity(
                    null,
                    "访客",
                    "VIEW_HOME",
                    "访问系统首页",
                    UserActivityLog.Module.SYSTEM
                );
            }
        } catch (Exception e) {
            log.debug("记录首页访问日志失败：{}", e.getMessage());
        }

        return "index";
    }
    
    /**
     * 获取最近活动日志
     */
    @GetMapping("/api/activities/recent")
    @ResponseBody
    public ApiResponse<List<UserActivityLog>> getRecentActivities(
            @RequestParam(defaultValue = "20") int limit) {
        try {
            if (activityLogService != null) {
                List<UserActivityLog> activities = activityLogService.getRecentActivities(limit);
                return ApiResponse.success(activities);
            } else {
                // 返回模拟数据
                List<UserActivityLog> mockActivities = createMockActivities();
                return ApiResponse.success(mockActivities);
            }
        } catch (Exception e) {
            log.warn("获取最近活动日志失败，返回模拟数据：{}", e.getMessage());
            List<UserActivityLog> mockActivities = createMockActivities();
            return ApiResponse.success(mockActivities);
        }
    }

    /**
     * 分页获取活动日志
     */
    @GetMapping("/api/activities/page")
    @ResponseBody
    public ApiResponse<Map<String, Object>> getActivitiesPage(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            Map<String, Object> result = new HashMap<>();

            if (activityLogService != null) {
                List<UserActivityLog> activities = activityLogService.getActivitiesWithPagination(page, size);
                long totalCount = activityLogService.getTotalActivitiesCount();
                int totalPages = (int) Math.ceil((double) totalCount / size);

                result.put("content", activities);
                result.put("totalElements", totalCount);
                result.put("totalPages", totalPages);
                result.put("currentPage", page);
                result.put("pageSize", size);
                result.put("hasNext", page < totalPages - 1);
                result.put("hasPrevious", page > 0);

                return ApiResponse.success(result);
            } else {
                // 返回模拟分页数据
                List<UserActivityLog> mockActivities = createMockActivities();
                result.put("content", mockActivities);
                result.put("totalElements", 50L);
                result.put("totalPages", 3);
                result.put("currentPage", page);
                result.put("pageSize", size);
                result.put("hasNext", page < 2);
                result.put("hasPrevious", page > 0);

                return ApiResponse.success(result);
            }
        } catch (Exception e) {
            log.warn("获取分页活动日志失败，返回模拟数据：{}", e.getMessage());
            Map<String, Object> result = new HashMap<>();
            List<UserActivityLog> mockActivities = createMockActivities();
            result.put("content", mockActivities);
            result.put("totalElements", 50L);
            result.put("totalPages", 3);
            result.put("currentPage", page);
            result.put("pageSize", size);
            result.put("hasNext", page < 2);
            result.put("hasPrevious", page > 0);

            return ApiResponse.success(result);
        }
    }
    
    /**
     * 获取活动统计数据
     */
    @GetMapping("/api/activities/statistics")
    @ResponseBody
    public ApiResponse<Map<String, Object>> getActivityStatistics() {
        try {
            if (activityLogService != null) {
                LocalDateTime startTime = LocalDateTime.now().minusDays(7); // 最近7天

                Map<String, Object> statistics = new HashMap<>();

                // 活动类型统计
                List<Map<String, Object>> activityStats = activityLogService.getActivityStatistics(startTime);
                statistics.put("activityTypes", activityStats);

                // 用户活动统计（前10名）
                List<Map<String, Object>> userStats = activityLogService.getUserActivityStatistics(startTime, 10);
                statistics.put("activeUsers", userStats);

                // 模块使用统计
                List<Map<String, Object>> moduleStats = activityLogService.getModuleStatistics(startTime);
                statistics.put("modules", moduleStats);

                // 最近活动
                List<UserActivityLog> recentActivities = activityLogService.getRecentActivities(10);
                statistics.put("recentActivities", recentActivities);

                return ApiResponse.success(statistics);
            } else {
                // 返回模拟统计数据
                Map<String, Object> mockStatistics = createMockStatistics();
                return ApiResponse.success(mockStatistics);
            }
        } catch (Exception e) {
            log.warn("获取活动统计数据失败，返回模拟数据：{}", e.getMessage());
            Map<String, Object> mockStatistics = createMockStatistics();
            return ApiResponse.success(mockStatistics);
        }
    }
    
    /**
     * 记录用户活动
     */
    @GetMapping("/api/activities/log")
    @ResponseBody
    public ApiResponse<String> logActivity(
            @RequestParam String activityType,
            @RequestParam String description,
            @RequestParam String module,
            @RequestParam(required = false) String targetId,
            @RequestParam(required = false) String targetType) {
        try {
            if (activityLogService != null) {
                // 从用户上下文获取用户信息
                Long userId = UserContextUtil.getCurrentUserId();
                String username = UserContextUtil.getCurrentUsername();

                activityLogService.logActivity(
                    userId,
                    username,
                    activityType,
                    description,
                    module,
                    targetId,
                    targetType,
                    null
                );
            }

            return ApiResponse.success("活动记录成功");
        } catch (Exception e) {
            log.debug("记录用户活动失败：{}", e.getMessage());
            return ApiResponse.success("活动记录成功"); // 静默处理，不影响用户体验
        }
    }

    /**
     * 创建模拟活动日志数据
     */
    private List<UserActivityLog> createMockActivities() {
        List<UserActivityLog> activities = new java.util.ArrayList<>();

        UserActivityLog log1 = new UserActivityLog();
        log1.setId(1L);
        log1.setUsername("admin");
        log1.setActivityType("LOGIN");
        log1.setDescription("用户登录系统");
        log1.setModule("SYSTEM");
        log1.setResult("SUCCESS");
        log1.setCreateTime(LocalDateTime.now().minusMinutes(5));
        activities.add(log1);

        UserActivityLog log2 = new UserActivityLog();
        log2.setId(2L);
        log2.setUsername("teacher1");
        log2.setActivityType("GENERATE_PAPER");
        log2.setDescription("生成数学试卷");
        log2.setModule("PAPER_GENERATION");
        log2.setResult("SUCCESS");
        log2.setCreateTime(LocalDateTime.now().minusMinutes(15));
        activities.add(log2);

        UserActivityLog log3 = new UserActivityLog();
        log3.setId(3L);
        log3.setUsername("teacher2");
        log3.setActivityType("UPLOAD_TOPICS");
        log3.setDescription("上传英语题目");
        log3.setModule("TOPIC_MANAGEMENT");
        log3.setResult("SUCCESS");
        log3.setCreateTime(LocalDateTime.now().minusMinutes(30));
        activities.add(log3);

        return activities;
    }

    /**
     * 创建模拟统计数据
     */
    private Map<String, Object> createMockStatistics() {
        Map<String, Object> statistics = new HashMap<>();

        // 活动类型统计
        List<Map<String, Object>> activityTypes = new java.util.ArrayList<>();
        Map<String, Object> activity1 = new HashMap<>();
        activity1.put("activity_type", "GENERATE_PAPER");
        activity1.put("count", 15);
        activityTypes.add(activity1);

        Map<String, Object> activity2 = new HashMap<>();
        activity2.put("activity_type", "UPLOAD_TOPICS");
        activity2.put("count", 8);
        activityTypes.add(activity2);

        statistics.put("activityTypes", activityTypes);

        // 活跃用户统计
        List<Map<String, Object>> activeUsers = new java.util.ArrayList<>();
        Map<String, Object> user1 = new HashMap<>();
        user1.put("username", "admin");
        user1.put("count", 25);
        activeUsers.add(user1);

        Map<String, Object> user2 = new HashMap<>();
        user2.put("username", "teacher1");
        user2.put("count", 18);
        activeUsers.add(user2);

        statistics.put("activeUsers", activeUsers);

        // 模块统计
        List<Map<String, Object>> modules = new java.util.ArrayList<>();
        Map<String, Object> module1 = new HashMap<>();
        module1.put("module", "PAPER_GENERATION");
        module1.put("count", 20);
        modules.add(module1);

        Map<String, Object> module2 = new HashMap<>();
        module2.put("module", "TOPIC_MANAGEMENT");
        module2.put("count", 15);
        modules.add(module2);

        statistics.put("modules", modules);

        return statistics;
    }
}
