package com.edu.maizi_edu_sys.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.edu.maizi_edu_sys.controller.UserController;
import com.edu.maizi_edu_sys.entity.User;
import com.edu.maizi_edu_sys.entity.dto.ApiResponse;
import com.edu.maizi_edu_sys.entity.dto.LoginRequest;
import com.edu.maizi_edu_sys.entity.dto.RegisterRequest;
import org.springframework.web.multipart.MultipartFile;

public interface UserService extends IService<User> {
    ApiResponse<?> login(LoginRequest request);
    ApiResponse<String> register(RegisterRequest request);
    ApiResponse<String> logout(String token);
    ApiResponse<User> getCurrentUser(String token);
    ApiResponse<String> updateAvatar(MultipartFile file, String token);
    ApiResponse<?> updateProfile(UserController.UserUpdateRequest updateRequest, String token);
    User getByUsername(String username);
}