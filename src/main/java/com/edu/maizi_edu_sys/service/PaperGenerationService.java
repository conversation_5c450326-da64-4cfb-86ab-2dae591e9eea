package com.edu.maizi_edu_sys.service;

import com.edu.maizi_edu_sys.dto.PaperGenerationRequest;
import com.edu.maizi_edu_sys.dto.PaperGenerationResponse;
import com.edu.maizi_edu_sys.dto.PaperDetailDTO;
import com.edu.maizi_edu_sys.dto.PaginationResponse;
import com.edu.maizi_edu_sys.dto.CustomPaperRequest;
import com.edu.maizi_edu_sys.dto.PaperPreviewRequest;
import com.edu.maizi_edu_sys.dto.PaperPreviewResponse;
import com.edu.maizi_edu_sys.dto.BatchPaperGenerationRequest;
import com.edu.maizi_edu_sys.dto.BatchPaperGenerationResponse;
import com.edu.maizi_edu_sys.entity.Paper;
import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.entity.PaperDownload;
import org.springframework.core.io.Resource;

import java.util.List;

/**
 * 试卷生成服务接口
 */
public interface PaperGenerationService {

    /**
     * 根据请求生成试卷
     */
    PaperGenerationResponse generatePaper(PaperGenerationRequest request);

    /**
     * 保存试卷
     */
    Paper savePaper(Paper paper);

    /**
     * 获取所有试卷
     */
    List<Paper> getAllPapers();

    /**
     * 获取试卷详情
     */
    PaperDetailDTO getPaperDetail(Long id);

    /**
     * 获取试卷实体
     */
    Paper getPaperById(Long id);

    /**
     * 删除试卷
     */
    boolean deletePaper(Long id);

    /**
     * 更新题目使用统计
     */
    void updateTopicUsageStats(List<Topic> topics);

    Resource generatePaperResource(Long id, String paperType);
    Resource generateWordResource(Long id, String paperType);

    /**
     * 获取分页的试卷列表，支持搜索、筛选和排序
     *
     * @param search 搜索关键词（标题模糊匹配）
     * @param type 试卷类型筛选
     * @param sortField 排序字段（数据库字段名）
     * @param isAsc 是否升序排列
     * @param page 页码（从0开始）
     * @param size 每页条数
     * @return 分页结果包装对象
     */
    PaginationResponse<Paper> getPapersWithPagination(String search, Integer type,
                                                     String sortField, boolean isAsc,
                                                     Integer page, Integer size);

    /**
     * 处理自由组卷请求
     * 支持按知识点精确控制题目数量及是否包含简答题
     *
     * @param request 自由组卷请求
     * @return 组卷响应
     */
    PaperGenerationResponse generateCustomPaper(CustomPaperRequest request);

    /**
     * 保存试卷下载记录
     *
     * @param download 试卷下载记录实体
     * @return 保存后的试卷下载记录
     */
    PaperDownload savePaperDownload(PaperDownload download);

    /**
     *  批量生成试卷
     *
     * @param request 批量生成请求
     * @return 批量生成响应
     */
    BatchPaperGenerationResponse generateBatchPapers(BatchPaperGenerationRequest request);

    /**
     * 预览试卷内容
     * 根据配置参数返回真实的题目用于预览
     *
     * @param request 预览请求参数
     * @return 预览响应，包含题目和统计信息
     */
    PaperPreviewResponse previewPaper(PaperPreviewRequest request);
}