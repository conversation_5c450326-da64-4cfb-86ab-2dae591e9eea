package com.edu.maizi_edu_sys.service;

import com.edu.maizi_edu_sys.entity.Paper;
import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.dto.ComprehensivePaperOverviewDTO;
import com.edu.maizi_edu_sys.dto.PaperWithTopicsDTO;
import com.edu.maizi_edu_sys.dto.PaperDetailDTO;
import com.edu.maizi_edu_sys.util.PaginationResponse;

import java.util.List;
import java.util.Map;

/**
 * Paper Service Interface
 * 处理与试卷相关的逻辑
 */
public interface PaperService {

    /**
     * 解析题目选项
     * @param optionsJson 选项JSON字符串
     * @return 选项列表
     */
    List<Map<String, String>> parseOptions(String optionsJson);

    /**
     * 解析组合题的子题目
     * @param subsJson 子题目JSON字符串
     * @return 子题目列表
     */
    List<Topic> parseSubTopics(String subsJson);

    PaperWithTopicsDTO getPaperWithTopicsDTOById(Long paperId);

    PaginationResponse<Paper> getPapersWithPagination(Integer userId, int page, int size, String sort, String search, String type);

    ComprehensivePaperOverviewDTO getComprehensivePaperOverview(Long paperId);

    /**
     * 获取试卷详细信息，包括基本信息、题目列表、类型、时间戳和难度分布
     * @param paperId 试卷ID
     * @return 试卷详情DTO
     */
    PaperDetailDTO getPaperDetail(Long paperId);
}