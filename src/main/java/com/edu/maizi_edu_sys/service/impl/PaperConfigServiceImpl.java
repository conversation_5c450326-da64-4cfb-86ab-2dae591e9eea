package com.edu.maizi_edu_sys.service.impl;

import com.edu.maizi_edu_sys.dto.PaperConfigDTO;
import com.edu.maizi_edu_sys.dto.PaginationResponse;
import com.edu.maizi_edu_sys.dto.KnowledgePointConfigRequest;
import com.edu.maizi_edu_sys.entity.PaperConfig;
import com.edu.maizi_edu_sys.entity.KnowledgePoint;
import com.edu.maizi_edu_sys.repository.PaperConfigRepository;
import com.edu.maizi_edu_sys.repository.KnowledgePointRepository;
import com.edu.maizi_edu_sys.service.PaperConfigService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 试卷配置服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PaperConfigServiceImpl implements PaperConfigService {

    private final PaperConfigRepository paperConfigRepository;
    private final KnowledgePointRepository knowledgePointRepository;
    private final ObjectMapper objectMapper;

    @Override
    @Transactional
    public PaperConfigDTO saveConfig(PaperConfigDTO configDTO) {
        log.info("保存试卷配置: {}", configDTO.getConfigName());

        // 验证配置数据
        validateConfig(configDTO);

        // 检查配置名称是否重复
        if (isConfigNameExists(configDTO.getConfigName(), configDTO.getUserId(), null)) {
            throw new IllegalArgumentException("配置名称已存在: " + configDTO.getConfigName());
        }

        //  如果设置为默认配置，先清除该用户的其他默认配置
        if (configDTO.getIsDefault() != null && configDTO.getIsDefault()) {
            clearOtherDefaultConfigs(configDTO.getUserId());
        }

        // 转换为实体
        PaperConfig config = convertToEntity(configDTO);
        config.setCreatedAt(LocalDateTime.now());
        config.setUpdatedAt(LocalDateTime.now());

        // 如果是第一个配置，设置为默认配置
        if (paperConfigRepository.countByUserId(configDTO.getUserId()) == 0) {
            config.setIsDefault(true);
        }

        // 保存配置
        PaperConfig savedConfig = paperConfigRepository.save(config);
        log.info("配置保存成功，ID: {}", savedConfig.getId());

        return convertToDTO(savedConfig);
    }

    @Override
    @Transactional
    public PaperConfigDTO updateConfig(Long id, PaperConfigDTO configDTO) {
        log.info("更新试卷配置，ID: {}", id);

        // 查找现有配置
        PaperConfig existingConfig = paperConfigRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("配置不存在，ID: " + id));

        // 检查权限
        if (!existingConfig.getUserId().equals(configDTO.getUserId())) {
            throw new IllegalArgumentException("无权限修改此配置");
        }

        // 验证配置数据
        validateConfig(configDTO);

        // 检查配置名称是否重复（排除当前配置）
        if (isConfigNameExists(configDTO.getConfigName(), configDTO.getUserId(), id)) {
            throw new IllegalArgumentException("配置名称已存在: " + configDTO.getConfigName());
        }

        //  如果设置为默认配置，先清除该用户的其他默认配置
        if (configDTO.getIsDefault() != null && configDTO.getIsDefault() && !existingConfig.getIsDefault()) {
            clearOtherDefaultConfigs(configDTO.getUserId());
        }

        // 更新配置
        updateEntityFromDTO(existingConfig, configDTO);
        existingConfig.setUpdatedAt(LocalDateTime.now());

        PaperConfig updatedConfig = paperConfigRepository.save(existingConfig);
        log.info("配置更新成功，ID: {}", updatedConfig.getId());

        return convertToDTO(updatedConfig);
    }

    @Override
    @Transactional
    public void deleteConfig(Long id, Long userId) {
        log.info("删除试卷配置，ID: {}", id);

        PaperConfig config = paperConfigRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("配置不存在，ID: " + id));

        // 检查权限
        if (!config.getUserId().equals(userId)) {
            throw new IllegalArgumentException("无权限删除此配置");
        }

        // 如果是默认配置，需要重新设置默认配置
        if (config.getIsDefault()) {
            List<PaperConfig> userConfigs = paperConfigRepository.findByUserIdOrderByLastUsedAtDescCreatedAtDesc(userId);
            if (userConfigs.size() > 1) {
                // 设置最近使用的其他配置为默认配置
                PaperConfig newDefault = userConfigs.stream()
                    .filter(c -> !c.getId().equals(id))
                    .findFirst()
                    .orElse(null);
                if (newDefault != null) {
                    newDefault.setIsDefault(true);
                    paperConfigRepository.save(newDefault);
                }
            }
        }

        paperConfigRepository.delete(config);
        log.info("配置删除成功，ID: {}", id);
    }

    @Override
    public PaperConfigDTO getConfigById(Long id, Long userId) {
        PaperConfig config = paperConfigRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("配置不存在，ID: " + id));

        // 检查权限（自己的配置或公共配置）
        if (!config.getUserId().equals(userId) && !config.getIsPublic()) {
            throw new IllegalArgumentException("无权限访问此配置");
        }

        return convertToDTO(config);
    }

    @Override
    public List<PaperConfigDTO> getUserConfigs(Long userId) {
        List<PaperConfig> configs = paperConfigRepository.findByUserIdOrderByLastUsedAtDescCreatedAtDesc(userId);
        return configs.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }

    @Override
    public PaginationResponse<PaperConfigDTO> getUserConfigsPaginated(Long userId, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<PaperConfig> configPage = paperConfigRepository.findByUserIdOrderByLastUsedAtDescCreatedAtDesc(userId, pageable);

        List<PaperConfigDTO> configs = configPage.getContent().stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());

        PaginationResponse<PaperConfigDTO> response = new PaginationResponse<>();
        response.setContent(configs);
        response.setTotalElements(configPage.getTotalElements());
        response.setTotalPages(configPage.getTotalPages());
        response.setNumber(configPage.getNumber());
        response.setSize(configPage.getSize());
        response.setFirst(configPage.isFirst());
        response.setLast(configPage.isLast());
        response.setHasNext(configPage.hasNext());
        response.setHasPrevious(configPage.hasPrevious());
        return response;
    }

    @Override
    public List<PaperConfigDTO> getUserAndPublicConfigs(Long userId) {
        List<PaperConfig> configs = paperConfigRepository.findByUserIdOrPublicOrderByLastUsedAtDescCreatedAtDesc(userId);
        return configs.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }

    @Override
    public PaginationResponse<PaperConfigDTO> getUserAndPublicConfigsPaginated(Long userId, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<PaperConfig> configPage = paperConfigRepository.findByUserIdOrPublicOrderByLastUsedAtDescCreatedAtDesc(userId, pageable);

        List<PaperConfigDTO> configs = configPage.getContent().stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());

        PaginationResponse<PaperConfigDTO> response = new PaginationResponse<>();
        response.setContent(configs);
        response.setTotalElements(configPage.getTotalElements());
        response.setTotalPages(configPage.getTotalPages());
        response.setNumber(configPage.getNumber());
        response.setSize(configPage.getSize());
        response.setFirst(configPage.isFirst());
        response.setLast(configPage.isLast());
        response.setHasNext(configPage.hasNext());
        response.setHasPrevious(configPage.hasPrevious());
        return response;
    }

    @Override
    public List<PaperConfigDTO> searchConfigs(Long userId, String keyword, boolean includePublic) {
        List<PaperConfig> configs;
        if (includePublic) {
            configs = paperConfigRepository.findByUserIdOrPublicAndConfigNameContainingOrderByLastUsedAtDescCreatedAtDesc(userId, keyword);
        } else {
            configs = paperConfigRepository.findByUserIdAndConfigNameContainingOrderByLastUsedAtDescCreatedAtDesc(userId, keyword);
        }

        return configs.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }

    @Override
    public PaperConfigDTO getDefaultConfig(Long userId) {
        Optional<PaperConfig> defaultConfig = paperConfigRepository.findByUserIdAndIsDefaultTrue(userId);
        return defaultConfig.map(this::convertToDTO).orElse(null);
    }

    @Override
    @Transactional
    public void setDefaultConfig(Long id, Long userId) {
        // 清除当前默认配置
        Optional<PaperConfig> currentDefault = paperConfigRepository.findByUserIdAndIsDefaultTrue(userId);
        if (currentDefault.isPresent()) {
            currentDefault.get().setIsDefault(false);
            paperConfigRepository.save(currentDefault.get());
        }

        // 设置新的默认配置
        PaperConfig config = paperConfigRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("配置不存在，ID: " + id));

        if (!config.getUserId().equals(userId)) {
            throw new IllegalArgumentException("无权限设置此配置为默认");
        }

        config.setIsDefault(true);
        paperConfigRepository.save(config);
        log.info("设置默认配置成功，ID: {}", id);
    }

    @Override
    public List<PaperConfigDTO> getRecentlyUsedConfigs(Long userId, int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        List<PaperConfig> configs = paperConfigRepository.findRecentlyUsedByUserId(userId, pageable);
        return configs.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }

    @Override
    public List<PaperConfigDTO> getMostUsedConfigs(Long userId, int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        List<PaperConfig> configs = paperConfigRepository.findMostUsedByUserId(userId, pageable);
        return configs.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void useConfig(Long id, Long userId) {
        PaperConfig config = paperConfigRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("配置不存在，ID: " + id));

        // 检查权限（自己的配置或公共配置）
        if (!config.getUserId().equals(userId) && !config.getIsPublic()) {
            throw new IllegalArgumentException("无权限使用此配置");
        }

        // 更新使用次数和最后使用时间
        config.setUsageCount(config.getUsageCount() + 1);
        config.setLastUsedAt(LocalDateTime.now());
        paperConfigRepository.save(config);

        log.info("配置使用记录更新，ID: {}, 使用次数: {}", id, config.getUsageCount());
    }

    /**
     *  DTO到Entity的转换
     */
    private PaperConfig convertToEntity(PaperConfigDTO dto) {
        try {
            PaperConfig entity = new PaperConfig();
            entity.setId(dto.getId());
            entity.setConfigName(dto.getConfigName());
            entity.setDescription(dto.getDescription());
            entity.setUserId(dto.getUserId());
            entity.setTitleTemplate(dto.getTitleTemplate());
            entity.setPaperType(dto.getPaperType());
            entity.setPaperCount(dto.getPaperCount());

            // 题型配置
            entity.setSingleChoiceCount(dto.getSingleChoiceCount());
            entity.setSingleChoiceScore(dto.getSingleChoiceScore());
            entity.setMultipleChoiceCount(dto.getMultipleChoiceCount());
            entity.setMultipleChoiceScore(dto.getMultipleChoiceScore());
            entity.setJudgmentCount(dto.getJudgmentCount());
            entity.setJudgmentScore(dto.getJudgmentScore());
            entity.setFillCount(dto.getFillCount());
            entity.setFillScore(dto.getFillScore());
            entity.setShortAnswerCount(dto.getShortAnswerCount());
            entity.setShortAnswerScore(dto.getShortAnswerScore());

            // 难度分布（转换为JSON）
            if (dto.getDifficultyDistribution() != null) {
                entity.setDifficultyDistribution(objectMapper.writeValueAsString(dto.getDifficultyDistribution()));
            }

            // 知识点配置（转换为JSON）
            if (dto.getKnowledgePointConfigs() != null) {
                entity.setKnowledgePointConfigs(objectMapper.writeValueAsString(dto.getKnowledgePointConfigs()));
            }

            entity.setIsDefault(dto.getIsDefault());
            entity.setIsPublic(dto.getIsPublic());
            entity.setUsageCount(dto.getUsageCount());
            entity.setCreatedAt(dto.getCreatedAt());
            entity.setUpdatedAt(dto.getUpdatedAt());
            entity.setLastUsedAt(dto.getLastUsedAt());

            return entity;
        } catch (Exception e) {
            log.error("转换DTO到Entity失败: {}", e.getMessage(), e);
            throw new RuntimeException("配置数据转换失败", e);
        }
    }

    /**
     *  Entity到DTO的转换
     */
    private PaperConfigDTO convertToDTO(PaperConfig entity) {
        try {
            PaperConfigDTO dto = new PaperConfigDTO();
            dto.setId(entity.getId());
            dto.setConfigName(entity.getConfigName());
            dto.setDescription(entity.getDescription());
            dto.setUserId(entity.getUserId());
            dto.setTitleTemplate(entity.getTitleTemplate());
            dto.setPaperType(entity.getPaperType());
            dto.setPaperCount(entity.getPaperCount());

            // 题型配置
            dto.setSingleChoiceCount(entity.getSingleChoiceCount());
            dto.setSingleChoiceScore(entity.getSingleChoiceScore());
            dto.setMultipleChoiceCount(entity.getMultipleChoiceCount());
            dto.setMultipleChoiceScore(entity.getMultipleChoiceScore());
            dto.setJudgmentCount(entity.getJudgmentCount());
            dto.setJudgmentScore(entity.getJudgmentScore());
            dto.setFillCount(entity.getFillCount());
            dto.setFillScore(entity.getFillScore());
            dto.setShortAnswerCount(entity.getShortAnswerCount());
            dto.setShortAnswerScore(entity.getShortAnswerScore());

            // 难度分布（从JSON转换）
            if (entity.getDifficultyDistribution() != null && !entity.getDifficultyDistribution().isEmpty()) {
                dto.setDifficultyDistribution(objectMapper.readValue(entity.getDifficultyDistribution(),
                    PaperConfigDTO.DifficultyDistribution.class));
            }

            // 知识点配置（从JSON转换并填充知识点名称）
            if (entity.getKnowledgePointConfigs() != null && !entity.getKnowledgePointConfigs().isEmpty()) {
                TypeReference<List<KnowledgePointConfigRequest>> typeRef = new TypeReference<List<KnowledgePointConfigRequest>>() {};
                List<KnowledgePointConfigRequest> knowledgePointConfigs = objectMapper.readValue(entity.getKnowledgePointConfigs(), typeRef);

                // 填充知识点名称
                enrichKnowledgePointConfigsWithNames(knowledgePointConfigs);

                dto.setKnowledgePointConfigs(knowledgePointConfigs);
            }

            dto.setIsDefault(entity.getIsDefault());
            dto.setIsPublic(entity.getIsPublic());
            dto.setUsageCount(entity.getUsageCount());
            dto.setCreatedAt(entity.getCreatedAt());
            dto.setUpdatedAt(entity.getUpdatedAt());
            dto.setLastUsedAt(entity.getLastUsedAt());

            return dto;
        } catch (Exception e) {
            log.error("转换Entity到DTO失败: {}", e.getMessage(), e);
            throw new RuntimeException("配置数据转换失败", e);
        }
    }

    /**
     *  从DTO更新Entity
     */
    private void updateEntityFromDTO(PaperConfig entity, PaperConfigDTO dto) {
        try {
            entity.setConfigName(dto.getConfigName());
            entity.setDescription(dto.getDescription());
            entity.setTitleTemplate(dto.getTitleTemplate());
            entity.setPaperType(dto.getPaperType());
            entity.setPaperCount(dto.getPaperCount());

            // 题型配置
            entity.setSingleChoiceCount(dto.getSingleChoiceCount());
            entity.setSingleChoiceScore(dto.getSingleChoiceScore());
            entity.setMultipleChoiceCount(dto.getMultipleChoiceCount());
            entity.setMultipleChoiceScore(dto.getMultipleChoiceScore());
            entity.setJudgmentCount(dto.getJudgmentCount());
            entity.setJudgmentScore(dto.getJudgmentScore());
            entity.setFillCount(dto.getFillCount());
            entity.setFillScore(dto.getFillScore());
            entity.setShortAnswerCount(dto.getShortAnswerCount());
            entity.setShortAnswerScore(dto.getShortAnswerScore());

            // 难度分布
            if (dto.getDifficultyDistribution() != null) {
                entity.setDifficultyDistribution(objectMapper.writeValueAsString(dto.getDifficultyDistribution()));
            }

            // 知识点配置
            if (dto.getKnowledgePointConfigs() != null) {
                entity.setKnowledgePointConfigs(objectMapper.writeValueAsString(dto.getKnowledgePointConfigs()));
            }

            entity.setIsDefault(dto.getIsDefault());
            entity.setIsPublic(dto.getIsPublic());
        } catch (Exception e) {
            log.error("更新Entity失败: {}", e.getMessage(), e);
            throw new RuntimeException("配置数据更新失败", e);
        }
    }

    /**
     *  清除用户的其他默认配置
     */
    private void clearOtherDefaultConfigs(Long userId) {
        try {
            List<PaperConfig> defaultConfigs = paperConfigRepository.findAllByUserIdAndIsDefaultTrue(userId);
            if (!defaultConfigs.isEmpty()) {
                defaultConfigs.forEach(config -> config.setIsDefault(false));
                paperConfigRepository.saveAll(defaultConfigs);
                log.info("清除用户 {} 的 {} 个默认配置", userId, defaultConfigs.size());
            }
        } catch (Exception e) {
            log.warn("清除默认配置时出现警告: {}", e.getMessage());
            // 不抛出异常，允许继续保存新配置
        }
    }

    /**
     *  配置验证
     */
    @Override
    public void validateConfig(PaperConfigDTO configDTO) {
        if (configDTO == null) {
            throw new IllegalArgumentException("配置数据不能为空");
        }

        if (configDTO.getConfigName() == null || configDTO.getConfigName().trim().isEmpty()) {
            throw new IllegalArgumentException("配置名称不能为空");
        }

        if (configDTO.getConfigName().length() > 100) {
            throw new IllegalArgumentException("配置名称长度不能超过100个字符");
        }

        if (configDTO.getUserId() == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }

        // 验证题型配置
        if (configDTO.getTotalQuestions() <= 0) {
            throw new IllegalArgumentException("试卷总题数必须大于0");
        }

        if (configDTO.getTotalScore() <= 0) {
            throw new IllegalArgumentException("试卷总分必须大于0");
        }

        // 验证难度分布
        if (configDTO.getDifficultyDistribution() != null) {
            PaperConfigDTO.DifficultyDistribution difficulty = configDTO.getDifficultyDistribution();
            double total = difficulty.getEasy() + difficulty.getMedium() + difficulty.getHard();
            if (Math.abs(total - 1.0) > 0.01) {
                throw new IllegalArgumentException("难度分布比例总和必须等于1.0");
            }
        }

        //  验证知识点配置 - 允许简答题模式
        boolean hasKnowledgePoints = configDTO.getKnowledgePointConfigs() != null && !configDTO.getKnowledgePointConfigs().isEmpty();
        boolean hasShortAnswerQuestions = configDTO.getShortAnswerCount() != null && configDTO.getShortAnswerCount() > 0;

        if (!hasKnowledgePoints && !hasShortAnswerQuestions) {
            throw new IllegalArgumentException("至少需要选择一个知识点或配置简答题");
        }

        log.info("配置验证通过: {}", configDTO.getConfigName());
    }

    @Override
    public boolean isConfigNameExists(String configName, Long userId, Long excludeId) {
        if (excludeId != null) {
            return paperConfigRepository.existsByConfigNameAndUserIdAndIdNot(configName, userId, excludeId);
        } else {
            return paperConfigRepository.findByConfigNameAndUserId(configName, userId).isPresent();
        }
    }

    /**
     *  复制配置
     */
    @Override
    @Transactional
    public PaperConfigDTO copyConfig(Long id, Long userId, String newConfigName) {
        log.info("复制配置，原配置ID: {}, 新配置名称: {}", id, newConfigName);

        // 获取原配置
        PaperConfig originalConfig = paperConfigRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("配置不存在，ID: " + id));

        // 检查权限（自己的配置或公共配置）
        if (!originalConfig.getUserId().equals(userId) && !originalConfig.getIsPublic()) {
            throw new IllegalArgumentException("无权限复制此配置");
        }

        // 检查新配置名称是否重复
        if (isConfigNameExists(newConfigName, userId, null)) {
            throw new IllegalArgumentException("配置名称已存在: " + newConfigName);
        }

        // 创建新配置
        PaperConfig newConfig = new PaperConfig();
        newConfig.setConfigName(newConfigName);
        newConfig.setDescription("复制自: " + originalConfig.getConfigName());
        newConfig.setUserId(userId);
        newConfig.setTitleTemplate(originalConfig.getTitleTemplate());
        newConfig.setPaperType(originalConfig.getPaperType());
        newConfig.setPaperCount(originalConfig.getPaperCount());

        // 复制题型配置
        newConfig.setSingleChoiceCount(originalConfig.getSingleChoiceCount());
        newConfig.setSingleChoiceScore(originalConfig.getSingleChoiceScore());
        newConfig.setMultipleChoiceCount(originalConfig.getMultipleChoiceCount());
        newConfig.setMultipleChoiceScore(originalConfig.getMultipleChoiceScore());
        newConfig.setJudgmentCount(originalConfig.getJudgmentCount());
        newConfig.setJudgmentScore(originalConfig.getJudgmentScore());
        newConfig.setFillCount(originalConfig.getFillCount());
        newConfig.setFillScore(originalConfig.getFillScore());
        newConfig.setShortAnswerCount(originalConfig.getShortAnswerCount());
        newConfig.setShortAnswerScore(originalConfig.getShortAnswerScore());

        // 复制其他配置
        newConfig.setDifficultyDistribution(originalConfig.getDifficultyDistribution());
        newConfig.setKnowledgePointConfigs(originalConfig.getKnowledgePointConfigs());
        newConfig.setIsDefault(false); // 复制的配置不设为默认
        newConfig.setIsPublic(false);  // 复制的配置不设为公共
        newConfig.setUsageCount(0);    // 重置使用次数

        PaperConfig savedConfig = paperConfigRepository.save(newConfig);
        log.info("配置复制成功，新配置ID: {}", savedConfig.getId());

        return convertToDTO(savedConfig);
    }

    /**
     *  导出单个配置
     */
    @Override
    public Resource exportConfig(Long id, Long userId) {
        log.info("导出配置，ID: {}", id);

        PaperConfig config = paperConfigRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("配置不存在，ID: " + id));

        // 检查权限
        if (!config.getUserId().equals(userId) && !config.getIsPublic()) {
            throw new IllegalArgumentException("无权限导出此配置");
        }

        try {
            PaperConfigDTO configDTO = convertToDTO(config);
            // 清除敏感信息
            configDTO.setId(null);
            configDTO.setUserId(null);
            configDTO.setUsageCount(0);
            configDTO.setCreatedAt(null);
            configDTO.setUpdatedAt(null);
            configDTO.setLastUsedAt(null);
            configDTO.setIsDefault(false);
            configDTO.setIsPublic(false);

            String jsonContent = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(configDTO);
            byte[] content = jsonContent.getBytes("UTF-8");

            String filename = "paper_config_" + config.getConfigName().replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5]", "_") +
                             "_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".json";

            return new ByteArrayResource(content) {
                @Override
                public String getFilename() {
                    return filename;
                }
            };
        } catch (Exception e) {
            log.error("导出配置失败: {}", e.getMessage(), e);
            throw new RuntimeException("配置导出失败", e);
        }
    }

    /**
     *  批量导出配置
     */
    @Override
    public Resource exportConfigs(List<Long> ids, Long userId) {
        log.info("批量导出配置，数量: {}", ids.size());

        List<PaperConfigDTO> configs = ids.stream()
            .map(id -> {
                try {
                    return getConfigById(id, userId);
                } catch (Exception e) {
                    log.warn("跳过无权限访问的配置，ID: {}", id);
                    return null;
                }
            })
            .filter(config -> config != null)
            .map(config -> {
                // 清除敏感信息
                config.setId(null);
                config.setUserId(null);
                config.setUsageCount(0);
                config.setCreatedAt(null);
                config.setUpdatedAt(null);
                config.setLastUsedAt(null);
                config.setIsDefault(false);
                config.setIsPublic(false);
                return config;
            })
            .collect(Collectors.toList());

        try {
            String jsonContent = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(configs);
            byte[] content = jsonContent.getBytes("UTF-8");

            String filename = "paper_configs_batch_" +
                             LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".json";

            return new ByteArrayResource(content) {
                @Override
                public String getFilename() {
                    return filename;
                }
            };
        } catch (Exception e) {
            log.error("批量导出配置失败: {}", e.getMessage(), e);
            throw new RuntimeException("配置批量导出失败", e);
        }
    }

    /**
     *  从文件导入配置
     */
    @Override
    @Transactional
    public List<PaperConfigDTO> importConfigs(MultipartFile file, Long userId) {
        log.info("导入配置文件: {}", file.getOriginalFilename());

        if (file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        String filename = file.getOriginalFilename();
        if (filename == null || !filename.toLowerCase().endsWith(".json")) {
            throw new IllegalArgumentException("只支持JSON格式的配置文件");
        }

        try {
            String content = new String(file.getBytes(), "UTF-8");
            List<PaperConfigDTO> importConfigs;

            // 尝试解析为单个配置或配置数组
            try {
                importConfigs = objectMapper.readValue(content, new TypeReference<List<PaperConfigDTO>>() {});
            } catch (Exception e) {
                // 如果解析数组失败，尝试解析单个配置
                PaperConfigDTO singleConfig = objectMapper.readValue(content, PaperConfigDTO.class);
                importConfigs = Arrays.asList(singleConfig);
            }

            List<PaperConfigDTO> savedConfigs = new ArrayList<>();
            int duplicateCount = 0;

            for (PaperConfigDTO configDTO : importConfigs) {
                try {
                    // 设置用户ID
                    configDTO.setUserId(userId);
                    configDTO.setId(null); // 清除ID，作为新配置保存

                    // 处理重名配置
                    String originalName = configDTO.getConfigName();
                    String uniqueName = originalName;
                    int suffix = 1;
                    while (isConfigNameExists(uniqueName, userId, null)) {
                        uniqueName = originalName + "_" + suffix;
                        suffix++;
                        duplicateCount++;
                    }
                    configDTO.setConfigName(uniqueName);

                    // 验证并保存配置
                    validateConfig(configDTO);
                    PaperConfigDTO savedConfig = saveConfig(configDTO);
                    savedConfigs.add(savedConfig);

                    log.info("导入配置成功: {}", savedConfig.getConfigName());
                } catch (Exception e) {
                    log.warn("导入配置失败: {}, 错误: {}", configDTO.getConfigName(), e.getMessage());
                }
            }

            log.info("配置导入完成，成功: {}, 重名处理: {}", savedConfigs.size(), duplicateCount);
            return savedConfigs;

        } catch (Exception e) {
            log.error("导入配置文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("配置文件导入失败: " + e.getMessage(), e);
        }
    }

    /**
     * 填充知识点配置中的知识点名称
     */
    private void enrichKnowledgePointConfigsWithNames(List<KnowledgePointConfigRequest> knowledgePointConfigs) {
        if (knowledgePointConfigs == null || knowledgePointConfigs.isEmpty()) {
            return;
        }

        try {
            // 收集所有需要查询的知识点ID
            List<Integer> knowledgeIds = knowledgePointConfigs.stream()
                .map(KnowledgePointConfigRequest::getKnowledgeId)
                .filter(Objects::nonNull)
                .map(Long::intValue)
                .distinct()
                .collect(Collectors.toList());

            if (knowledgeIds.isEmpty()) {
                log.debug("没有需要查询的知识点ID");
                return;
            }

            log.debug("准备查询知识点ID: {}", knowledgeIds);

            // 批量查询知识点信息
            List<KnowledgePoint> knowledgePoints = knowledgePointRepository.selectBatchIds(knowledgeIds);
            log.debug("查询到 {} 个知识点", knowledgePoints.size());

            // 创建两个映射：一个用主键ID，一个用knowledgeId
            Map<Long, String> idToNameMap = new HashMap<>();
            Map<Long, String> knowledgeIdToNameMap = new HashMap<>();

            for (KnowledgePoint kp : knowledgePoints) {
                String name = kp.getName();
                // 使用主键ID作为key
                if (kp.getId() != null) {
                    idToNameMap.put(kp.getId(), name);
                }
                // 使用knowledgeId作为key
                if (kp.getKnowledgeId() != null) {
                    knowledgeIdToNameMap.put(Long.valueOf(kp.getKnowledgeId()), name);
                }
            }

            // 填充知识点名称
            for (KnowledgePointConfigRequest config : knowledgePointConfigs) {
                if (config.getKnowledgeId() != null) {
                    String knowledgeName = null;

                    // 首先尝试用主键ID查找
                    knowledgeName = idToNameMap.get(config.getKnowledgeId());

                    // 如果主键ID查找失败，尝试用knowledgeId查找
                    if (knowledgeName == null) {
                        knowledgeName = knowledgeIdToNameMap.get(config.getKnowledgeId());
                    }

                    if (knowledgeName != null) {
                        config.setKnowledgeName(knowledgeName);
                        log.debug("为知识点ID {} 填充名称: {}", config.getKnowledgeId(), knowledgeName);
                    } else {
                        log.debug("未找到知识点ID {} 的名称", config.getKnowledgeId());
                        config.setKnowledgeName("未知知识点 (ID: " + config.getKnowledgeId() + ")");
                    }
                }
            }

            log.debug("成功填充 {} 个知识点配置的名称", knowledgePointConfigs.size());
        } catch (Exception e) {
            log.warn("填充知识点名称时出现错误: {}", e.getMessage(), e);

            // 为所有配置设置默认名称，避免前端显示空白
            for (KnowledgePointConfigRequest config : knowledgePointConfigs) {
                if (config.getKnowledgeId() != null && (config.getKnowledgeName() == null || config.getKnowledgeName().isEmpty())) {
                    config.setKnowledgeName("知识点 (ID: " + config.getKnowledgeId() + ")");
                }
            }

            // 不抛出异常，允许继续处理
        }
    }

    /**
     *  获取配置统计信息
     */
    @Override
    public ConfigStatistics getConfigStatistics(Long userId) {
        long totalConfigs = paperConfigRepository.countByUserId(userId);
        long publicConfigs = paperConfigRepository.countByIsPublicTrue();

        List<PaperConfig> recentConfigs = paperConfigRepository.findRecentlyUsedByUserId(userId, PageRequest.of(0, 1));
        long recentlyUsed = recentConfigs.size();

        List<PaperConfig> mostUsedConfigs = paperConfigRepository.findMostUsedByUserId(userId, PageRequest.of(0, 1));
        PaperConfigDTO mostUsedConfig = mostUsedConfigs.isEmpty() ? null : convertToDTO(mostUsedConfigs.get(0));

        List<PaperConfig> userConfigs = paperConfigRepository.findByUserIdOrderByLastUsedAtDescCreatedAtDesc(userId);
        PaperConfigDTO latestConfig = userConfigs.isEmpty() ? null : convertToDTO(userConfigs.get(0));

        return new ConfigStatistics(totalConfigs, publicConfigs, recentlyUsed, mostUsedConfig, latestConfig);
    }
}
