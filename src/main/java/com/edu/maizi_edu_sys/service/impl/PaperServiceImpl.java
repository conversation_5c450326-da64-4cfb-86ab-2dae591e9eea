package com.edu.maizi_edu_sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.edu.maizi_edu_sys.dto.ComprehensivePaperOverviewDTO;
import com.edu.maizi_edu_sys.dto.KpConfigInOverviewDTO;
import com.edu.maizi_edu_sys.dto.KnowledgePointConfigRequest;
import com.edu.maizi_edu_sys.dto.PaperWithTopicsDTO;
import com.edu.maizi_edu_sys.dto.SimpleKnowledgePointDto;
import com.edu.maizi_edu_sys.dto.TopicInOverviewDTO;
import com.edu.maizi_edu_sys.entity.KnowledgePoint;
import com.edu.maizi_edu_sys.entity.Paper;
import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.repository.KnowledgePointRepository;
import com.edu.maizi_edu_sys.repository.PaperRepository;
import com.edu.maizi_edu_sys.service.PaperService;
import com.edu.maizi_edu_sys.util.PaginationResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import com.edu.maizi_edu_sys.dto.PaperDetailDTO;

/**
 * Paper Service Implementation
 * 处理与试卷相关的逻辑
 */
@Service
@Slf4j
public class PaperServiceImpl implements PaperService {

    private final ObjectMapper objectMapper;
    private final PaperRepository paperRepository;
    private final KnowledgePointRepository knowledgePointRepository;
    private final ModelMapper modelMapper;

    public PaperServiceImpl(ObjectMapper objectMapper,
                           PaperRepository paperRepository,
                           KnowledgePointRepository knowledgePointRepository,
                           ModelMapper modelMapper) {
        this.objectMapper = objectMapper;
        this.paperRepository = paperRepository;
        this.knowledgePointRepository = knowledgePointRepository;
        this.modelMapper = modelMapper;
    }

    /**
     * 解析题目选项
     * @param optionsJson 选项JSON字符串
     * @return 选项列表
     */
    @Override
    public List<Map<String, String>> parseOptions(String optionsJson) {
        if (optionsJson == null || optionsJson.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            return objectMapper.readValue(optionsJson, new TypeReference<List<Map<String, String>>>() {});
        } catch (JsonProcessingException e) {
            log.error("Failed to parse options JSON: {}", e.getMessage());
            log.debug("Invalid options JSON: {}", optionsJson);
            return new ArrayList<>();
        }
    }

    /**
     * 解析组合题的子题目
     * @param subsJson 子题目JSON字符串
     * @return 子题目列表
     */
    @Override
    public List<Topic> parseSubTopics(String subsJson) {
        if (subsJson == null || subsJson.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            return objectMapper.readValue(subsJson, new TypeReference<List<Topic>>() {});
        } catch (JsonProcessingException e) {
            log.error("Failed to parse sub-topics JSON: {}", e.getMessage());
            log.debug("Invalid sub-topics JSON: {}", subsJson);
            return new ArrayList<>();
        }
    }

    private List<Topic> getTopicsFromPaperContent(String paperContentJson) {
        if (StringUtils.isBlank(paperContentJson)) {
            return Collections.emptyList();
        }
        try {
            return objectMapper.readValue(paperContentJson, new TypeReference<List<Topic>>() {});
        } catch (JsonProcessingException e) {
            log.error("Failed to parse paper content JSON to List<Topic>: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    @Transactional(readOnly = true)
    public ComprehensivePaperOverviewDTO getComprehensivePaperOverview(Long paperId) {
        Paper paper = paperRepository.selectById(paperId);
        if (paper == null) {
            throw new RuntimeException("Paper not found with id: " + paperId);
        }

        ComprehensivePaperOverviewDTO overviewDTO = modelMapper.map(paper, ComprehensivePaperOverviewDTO.class);

        List<KpConfigInOverviewDTO> kpConfigsInOverview = new ArrayList<>();
        if (StringUtils.isNotBlank(paper.getConfig())) {
            try {
                List<KnowledgePointConfigRequest> rawKpConfigs = objectMapper.readValue(paper.getConfig(),
                        new TypeReference<List<KnowledgePointConfigRequest>>() {});

                List<Integer> kpIdsForNameFetch = rawKpConfigs.stream()
                        .map(KnowledgePointConfigRequest::getKnowledgeId)
                        .filter(Objects::nonNull)
                        .map(Long::intValue)
                        .distinct()
                        .collect(Collectors.toList());

                Map<Long, String> kpIdToNameMap = Collections.emptyMap();
                if (!kpIdsForNameFetch.isEmpty()) {
                    List<KnowledgePoint> kps = knowledgePointRepository.selectBatchIds(kpIdsForNameFetch);
                    kpIdToNameMap = kps.stream()
                            .collect(Collectors.toMap(KnowledgePoint::getId, KnowledgePoint::getName));
                }

                for (KnowledgePointConfigRequest rawConfig : rawKpConfigs) {
                    KpConfigInOverviewDTO configDTO = modelMapper.map(rawConfig, KpConfigInOverviewDTO.class);
                    if (rawConfig.getKnowledgeId() != null) {
                        configDTO.setKnowledgeName(kpIdToNameMap.getOrDefault(rawConfig.getKnowledgeId(), "Unknown KP ID: " + rawConfig.getKnowledgeId()));
                    } else {
                        configDTO.setKnowledgeName("KP ID Not Specified");
                    }
                    kpConfigsInOverview.add(configDTO);
                }
            } catch (Exception e) {
                log.error("Error parsing Paper.config for paper " + paperId + ": " + e.getMessage(), e);
            }
        }
        overviewDTO.setOriginalKpConfigs(kpConfigsInOverview);

        List<Topic> topics = getTopicsFromPaperContent(paper.getContent());
        List<TopicInOverviewDTO> topicsInOverview = new ArrayList<>();

        if (!topics.isEmpty()) {
            List<Integer> topicKpIds = topics.stream()
                                           .map(Topic::getKnowId)
                                           .filter(Objects::nonNull)
                                           .distinct()
                                           .collect(Collectors.toList());

            Map<Long, KnowledgePoint> relatedKpsMap = Collections.emptyMap();
            if (!topicKpIds.isEmpty()) {
                List<KnowledgePoint> kps = knowledgePointRepository.selectBatchIds(topicKpIds);
                relatedKpsMap = kps.stream().collect(Collectors.toMap(KnowledgePoint::getId, Function.identity()));
            }

            for (Topic topic : topics) {
                TopicInOverviewDTO topicDTO = modelMapper.map(topic, TopicInOverviewDTO.class);
                if (topic.getKnowId() != null) {
                    KnowledgePoint relatedKp = relatedKpsMap.get(topic.getKnowId().longValue());
                    if (relatedKp != null) {
                        SimpleKnowledgePointDto simpleKpDto = modelMapper.map(relatedKp, SimpleKnowledgePointDto.class);
                        topicDTO.setRelatedKnowledgePoints(Collections.singletonList(simpleKpDto));
                    } else {
                        topicDTO.setRelatedKnowledgePoints(Collections.singletonList(new SimpleKnowledgePointDto(topic.getKnowId().longValue(), "Unknown KP ID: " + topic.getKnowId())));
                    }
                } else {
                    topicDTO.setRelatedKnowledgePoints(Collections.emptyList());
                }
                topicsInOverview.add(topicDTO);
            }
        }
        overviewDTO.setTopicsInPaper(topicsInOverview);
        overviewDTO.setQuestionCount(topicsInOverview.size());

        Map<String, Integer> actualTopicTypeCounts = topicsInOverview.stream()
                .collect(Collectors.groupingBy(TopicInOverviewDTO::getType, Collectors.summingInt(t -> 1)));
        overviewDTO.setActualTopicTypeCounts(actualTopicTypeCounts);

        Map<String, Long> difficultyCounts = topicsInOverview.stream()
                .filter(t -> t.getDifficultyLevel() != null)
                .collect(Collectors.groupingBy(TopicInOverviewDTO::getDifficultyLevel, Collectors.counting()));
        long totalTopicsWithDifficulty = topicsInOverview.stream().filter(t -> t.getDifficultyLevel() != null).count();
        Map<String, Double> actualDifficultyDistribution = new HashMap<>();
        if (totalTopicsWithDifficulty > 0) {
            difficultyCounts.forEach((level, count) ->
                actualDifficultyDistribution.put(level, (double) count / totalTopicsWithDifficulty)
            );
        }
        overviewDTO.setActualDifficultyDistribution(actualDifficultyDistribution);

        return overviewDTO;
    }

    @Override
    @Transactional(readOnly = true)
    public PaperWithTopicsDTO getPaperWithTopicsDTOById(Long paperId) {
        Paper paper = paperRepository.selectById(paperId);
        if (paper == null) {
            log.warn("Paper not found with id: {}", paperId);
            return null;
        }

        PaperWithTopicsDTO paperWithTopicsDTO = modelMapper.map(paper, PaperWithTopicsDTO.class);

        List<Topic> topics = getTopicsFromPaperContent(paper.getContent());
        paperWithTopicsDTO.setTopics(topics);

        return paperWithTopicsDTO;
    }

    @Override
    @Transactional(readOnly = true)
    public PaginationResponse<Paper> getPapersWithPagination(Integer userId, int pageNum, int pageSize, String sort, String search, String type) {
        Page<Paper> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<Paper> queryWrapper = new LambdaQueryWrapper<>();

        if (StringUtils.isNotBlank(search)) {
            queryWrapper.like(Paper::getTitle, search);
        }

        if (StringUtils.isNotBlank(type)) {
            try {
                Integer typeInt = Integer.parseInt(type);
                 queryWrapper.eq(Paper::getType, typeInt);
            } catch (NumberFormatException e) {
                log.warn("Invalid type format for pagination: {}. Expected an integer.", type);
            }
        }

        if (StringUtils.isNotBlank(sort)) {
            String[] sortParams = sort.split("_");
            if (sortParams.length == 2) {
                String sortField = sortParams[0];
                boolean isAsc = "asc".equalsIgnoreCase(sortParams[1]);

                if ("title".equalsIgnoreCase(sortField)) {
                    queryWrapper.orderBy(true, isAsc, Paper::getTitle);
                } else if ("createTime".equalsIgnoreCase(sortField)) {
                    queryWrapper.orderBy(true, isAsc, Paper::getCreateTime);
                } else if ("id".equalsIgnoreCase(sortField)) {
                     queryWrapper.orderBy(true, isAsc, Paper::getId);
                } else {
                    queryWrapper.orderBy(true, false, Paper::getCreateTime); // Default sort desc
                }
            } else {
                 queryWrapper.orderBy(true, false, Paper::getCreateTime); // Default sort desc
            }
        } else {
            queryWrapper.orderBy(true, false, Paper::getCreateTime); // Default sort desc
        }

        Page<Paper> resultPage = paperRepository.selectPage(page, queryWrapper);

        return new PaginationResponse<>(
                resultPage.getRecords(),
                resultPage.getTotal(),
                resultPage.getCurrent(),
                resultPage.getPages(),
                resultPage.getSize()
        );
    }

    private Map<String, Double> calculateDifficultyDistribution(List<Topic> topics) {
        Map<String, Long> counts = new HashMap<>();
        counts.put("1", 0L); // Easy
        counts.put("2", 0L); // Medium
        counts.put("3", 0L); // Hard
        long totalTopicsWithDifficulty = 0;

        if (topics == null || topics.isEmpty()) {
            // Return a default or empty distribution if there are no topics
            Map<String, Double> emptyDistribution = new HashMap<>();
            emptyDistribution.put("1", 0.0);
            emptyDistribution.put("2", 0.0);
            emptyDistribution.put("3", 0.0);
            return emptyDistribution;
        }

        for (Topic topic : topics) {
            Double difficulty = topic.getDifficulty();
            if (difficulty != null) {
                totalTopicsWithDifficulty++;
                if (difficulty <= 0.4) {
                    counts.put("1", counts.get("1") + 1);
                } else if (difficulty <= 0.7) {
                    counts.put("2", counts.get("2") + 1);
                } else {
                    counts.put("3", counts.get("3") + 1);
                }
            }
        }

        Map<String, Double> distribution = new HashMap<>();
        if (totalTopicsWithDifficulty > 0) {
            for (Map.Entry<String, Long> entry : counts.entrySet()) {
                distribution.put(entry.getKey(), (double) entry.getValue() / totalTopicsWithDifficulty);
            }
        } else {
            // If no topics have difficulty, return 0 distribution for each category
            distribution.put("1", 0.0);
            distribution.put("2", 0.0);
            distribution.put("3", 0.0);
        }
        return distribution;
    }

    @Override
    @Transactional(readOnly = true)
    public PaperDetailDTO getPaperDetail(Long paperId) {
        Paper paper = paperRepository.selectById(paperId);
        if (paper == null) {
            log.warn("Paper not found with id: {}", paperId);
            // Consider throwing a specific exception or returning null based on API contract
            return null;
        }

        List<Topic> topics = getTopicsFromPaperContent(paper.getContent());
        PaperDetailDTO dto = PaperDetailDTO.fromEntity(paper, topics);

        // Calculate and set difficulty distribution
        dto.setDifficultyDistribution(calculateDifficultyDistribution(topics));

        // Note: topicsByType, typeCountMap, typeScoreMap are not populated here
        // as they were not part of the immediate requirement for the modal.
        // They can be added if needed for the "View Full Paper" functionality.

        return dto;
    }
}