package com.edu.maizi_edu_sys.service.impl;

import com.edu.maizi_edu_sys.entity.Book;
import com.edu.maizi_edu_sys.entity.User;
import com.edu.maizi_edu_sys.entity.dto.ApiResponse;
import com.edu.maizi_edu_sys.repository.BookRepository;
import com.edu.maizi_edu_sys.service.BookService;
import com.edu.maizi_edu_sys.service.UserService;
import com.edu.maizi_edu_sys.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class BookServiceImpl implements BookService {

    private final BookRepository bookRepository;
    private final JwtUtil jwtUtil;
    private final UserService userService;

    @Override
    public ApiResponse<?> searchBooks(String keyword) {
        try {
            log.info("Searching books with keyword: '{}' in service layer", keyword);

            // 记录查询前的状态
            long totalBooks = bookRepository.count();
            log.info("Total books in database before search: {}", totalBooks);

            // 执行搜索 - 尝试使用原生SQL查询以提高性能
            List<Book> books;
            try {
                log.info("Attempting to use native SQL query for better performance");
                books = bookRepository.searchBooksNative(keyword);
                log.info("Native SQL query successful");
            } catch (Exception e) {
                log.warn("Native SQL query failed, falling back to JPQL: {}", e.getMessage());
                books = bookRepository.searchBooks(keyword);
            }

            // 记录搜索结果
            log.info("Search completed. Found {} books matching keyword '{}'", books.size(), keyword);

            // 记录搜索结果的详细信息
            if (!books.isEmpty()) {
                log.info("First 5 search results:");
                books.stream().limit(5).forEach(book ->
                    log.info("Book: id={}, title='{}', type='{}'",
                            book.getId(), book.getTitle(), book.getType())
                );
            }

            return ApiResponse.success(books);
        } catch (Exception e) {
            log.error("Error searching books: {}", e.getMessage(), e);
            return ApiResponse.error("搜索书籍失败: " + e.getMessage());
        }
    }

    @Override
    public ApiResponse<?> getAllBooks() {
        try {
            List<Book> books = bookRepository.findAll();
            return ApiResponse.success(books);
        } catch (Exception e) {
            log.error("Error getting all books: {}", e.getMessage(), e);
            return ApiResponse.error("获取所有书籍失败: " + e.getMessage());
        }
    }

    @Override
    public ApiResponse<?> getBookById(Long id) {
        try {
            Optional<Book> bookOpt = bookRepository.findById(id);
            if (bookOpt.isPresent()) {
                return ApiResponse.success(bookOpt.get());
            } else {
                return ApiResponse.error("书籍不存在");
            }
        } catch (Exception e) {
            log.error("Error getting book by id: {}", e.getMessage(), e);
            return ApiResponse.error("获取书籍详情失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public ApiResponse<?> createBook(Book book, String token) {
        try {
            // Get user from token
            String username = jwtUtil.getUsernameFromToken(token);
            User user = userService.getByUsername(username);

            // Set user to book
            book.setUser(user);

            Book savedBook = bookRepository.save(book);
            return ApiResponse.success(savedBook);
        } catch (Exception e) {
            log.error("Error creating book: {}", e.getMessage(), e);
            return ApiResponse.error("创建书籍失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public ApiResponse<?> updateBook(Long id, Book bookDetails, String token) {
        try {
            // Get user from token
            String username = jwtUtil.getUsernameFromToken(token);
            User user = userService.getByUsername(username);

            Optional<Book> bookOpt = bookRepository.findById(id);
            if (!bookOpt.isPresent()) {
                return ApiResponse.error("书籍不存在");
            }

            Book book = bookOpt.get();

            // Check if the book belongs to the user
            if (book.getUser() != null && !book.getUser().getId().equals(user.getId())) {
                return ApiResponse.error("无权修改此书籍");
            }

            // Update book details
            book.setKnowId(bookDetails.getKnowId());
            book.setTitle(bookDetails.getTitle());
            book.setType(bookDetails.getType());
            book.setUrl(bookDetails.getUrl());
            book.setDescription(bookDetails.getDescription());

            Book updatedBook = bookRepository.save(book);
            return ApiResponse.success(updatedBook);
        } catch (Exception e) {
            log.error("Error updating book: {}", e.getMessage(), e);
            return ApiResponse.error("更新书籍失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public ApiResponse<?> deleteBook(Long id, String token) {
        try {
            // Get user from token
            String username = jwtUtil.getUsernameFromToken(token);
            User user = userService.getByUsername(username);

            Optional<Book> bookOpt = bookRepository.findById(id);
            if (!bookOpt.isPresent()) {
                return ApiResponse.error("书籍不存在");
            }

            Book book = bookOpt.get();

            // Check if the book belongs to the user
            if (book.getUser() != null && !book.getUser().getId().equals(user.getId())) {
                return ApiResponse.error("无权删除此书籍");
            }

            bookRepository.delete(book);
            return ApiResponse.success(null);
        } catch (Exception e) {
            log.error("Error deleting book: {}", e.getMessage(), e);
            return ApiResponse.error("删除书籍失败: " + e.getMessage());
        }
    }

    @Override
    public ApiResponse<?> getBooksByType(String type) {
        try {
            List<Book> books = bookRepository.findByType(type);
            return ApiResponse.success(books);
        } catch (Exception e) {
            log.error("Error getting books by type: {}", e.getMessage(), e);
            return ApiResponse.error("获取指定类型书籍失败: " + e.getMessage());
        }
    }

    @Override
    public ApiResponse<?> getBooksByUser(String token) {
        try {
            // Get user from token
            String username = jwtUtil.getUsernameFromToken(token);
            User user = userService.getByUsername(username);

            List<Book> books = bookRepository.findByUserId(user.getId());
            return ApiResponse.success(books);
        } catch (Exception e) {
            log.error("Error getting books by user: {}", e.getMessage(), e);
            return ApiResponse.error("获取用户书籍失败: " + e.getMessage());
        }
    }

    @Override
    public List<Book> searchBooks(String query, int maxResults) {
        return bookRepository.findByTitleContainingOrDescriptionContaining(query, query)
            .stream().limit(maxResults)
            .collect(Collectors.toList());
    }
}