package com.edu.maizi_edu_sys.util;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.regex.*;

public class JsonOverwriteFixer {

    // 匹配options数组最后一个元素后的逗号
    // 修正后的正则表达式
    private static final Pattern TRAILING_COMMA = Pattern.compile(
            ",\\s*(?=\\s*\\])",
            Pattern.DOTALL
    );

    public static void main(String[] args) {
        try {
            processFile(new File("C:\\Users\\<USER>\\IdeaProjects\\maizi_edu_sys\\src\\main\\resources\\json\\test.json"));
            System.out.println("JSON修复完成");
        } catch (Exception e) {
            System.err.println("处理失败: " + e.getMessage());
        }
    }

    private static void processFile(File file) throws IOException {
        // 读取文件内容
        String content = readFile(file);

        // 修正JSON格式
        String fixed = fixJson(content);

        System.out.println(fixed);
        // 直接覆盖写入
        writeFile(file, fixed);
    }

    private static String readFile(File file) throws IOException {
        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(
                        Files.newInputStream(file.toPath()),
                        StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
        }
        return content.toString();
    }

    private static String fixJson(String json) {
        // 处理多层options数组
        Matcher matcher = TRAILING_COMMA.matcher(json);
        return matcher.replaceAll(""); // 直接删除非法逗号
    }

    private static void writeFile(File file, String content) throws IOException {
        // 原子化写入（先写临时文件再重命名）
        File tempFile = new File(file.getParent(), file.getName() + ".tmp");
        try (BufferedWriter writer = new BufferedWriter(
                new OutputStreamWriter(
                        Files.newOutputStream(tempFile.toPath()),
                        StandardCharsets.UTF_8))) {
            writer.write(content);
        }

        // 替换原文件
        if (!tempFile.renameTo(file)) {
            throw new IOException("文件覆盖失败");
        }
    }
}