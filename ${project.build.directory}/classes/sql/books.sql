-- Create books table
CREATE TABLE IF NOT EXISTS books (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    type VARCHAR(100),
    url VARCHAR(1000) NOT NULL,
    description TEXT,
    user_id BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Insert sample books data
INSERT INTO books (title, type, url, description, user_id) VALUES
('Java编程思想', '编程', 'https://www.yuque.com/books/java-thinking', 'Java编程基础与高级特性的经典教材', 1),
('Spring实战', '框架', 'https://www.yuque.com/books/spring-in-action', 'Spring框架入门与实战指南', 1),
('MySQL必知必会', '数据库', 'https://www.yuque.com/books/mysql-crash-course', 'MySQL数据库入门教程', 1),
('深入理解Java虚拟机', '编程', 'https://www.yuque.com/books/understanding-jvm', 'Java虚拟机原理与实现', 2),
('算法导论', '计算机科学', 'https://www.yuque.com/books/algorithms', '计算机算法理论与实践', 2),
('Python编程：从入门到实践', '编程', 'https://www.yuque.com/books/python-crash-course', 'Python编程快速入门指南', 3); 