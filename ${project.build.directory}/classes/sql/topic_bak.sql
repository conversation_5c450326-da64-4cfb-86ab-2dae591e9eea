/*
 Navicat Premium Data Transfer

 Source Server         : MySQL
 Source Server Type    : MySQL
 Source Server Version : 80400
 Source Host           : localhost:3306
 Source Schema         : maizipapergendb

 Target Server Type    : MySQL
 Target Server Version : 80400
 File Encoding         : 65001

 Date: 12/05/2025 19:09:28
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for topic_bak
-- ----------------------------
DROP TABLE IF EXISTS `topic_bak`;
CREATE TABLE `topic_bak`  (
  `id` int(0) UNSIGNED NOT NULL AUTO_INCREMENT,
  `know_id` int(0) UNSIGNED NOT NULL DEFAULT 0 COMMENT '知识点id',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '题目类型',
  `title` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '题目标题',
  `options` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目数据(json)',
  `subs` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '组合题数据(json，仅组合题使用)',
  `answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '答案',
  `parse` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '答案解析',
  `score` int(0) UNSIGNED NOT NULL DEFAULT 3 COMMENT '分值',
  `source` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '来源',
  `sort` tinyint(0) UNSIGNED NOT NULL DEFAULT 1 COMMENT '排序值(0-255)',
  `difficulty` double NOT NULL COMMENT '题目难度',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_know_id`(`know_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 100281 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '题目表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
