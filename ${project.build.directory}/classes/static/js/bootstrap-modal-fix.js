/**
 * Bootstrap Modal.js fix
 * 
 * This script fixes the "Cannot read properties of null (reading 'style')" error
 * that occurs in Bootstrap's modal.js _hideModal method
 */

(function() {
    console.log('Bootstrap Modal.js fix loaded');
    
    // Wait for bootstrap to be loaded
    function checkAndFixBootstrap() {
        if (typeof bootstrap === 'undefined' || !bootstrap.Modal) {
            // Bootstrap not loaded yet, retry later
            setTimeout(checkAndFixBootstrap, 100);
            return;
        }
        
        // Fix for _hideModal method
        fixBootstrapModalHide();
    }
    
    // Fix the _hideModal method in Bootstrap Modal
    function fixBootstrapModalHide() {
        try {
            const originalHideModal = bootstrap.Modal.prototype._hideModal;
            
            if (!originalHideModal) {
                console.warn('Could not find Bootstrap Modal _hideModal method to patch');
                return;
            }
            
            // Replace with safe version
            bootstrap.Modal.prototype._hideModal = function(event) {
                try {
                    // Get references to needed elements
                    const element = this._element;
                    
                    // Safety check - element must exist
                    if (!element) {
                        console.warn('Modal element is null in _hideModal');
                        return;
                    }
                    
                    // Original code with safety checks
                    element.style.display = 'none';
                    
                    // Safety check before manipulating classList
                    if (element.getAttribute('aria-modal')) {
                        element.removeAttribute('aria-modal');
                    }
                    
                    if (element.getAttribute('role')) {
                        element.removeAttribute('role');
                    }
                    
                    this._isTransitioning = false;
                    
                    // Call to _showBackdrop with null check
                    if (typeof this._backdrop === 'function') {
                        this._backdrop(null);
                    } else {
                        // Clean up manually if no backdrop callback
                        const backdropElements = document.querySelectorAll('.modal-backdrop');
                        backdropElements.forEach(el => {
                            el.parentNode?.removeChild(el);
                        });
                    }
                    
                    // If event is provided, handle event
                    if (event) {
                        // Emit hidden.bs.modal event
                        const hiddenEvent = new Event('hidden.bs.modal');
                        element.dispatchEvent(hiddenEvent);
                    }
                } catch (error) {
                    console.error('Error in patched _hideModal:', error);
                    
                    // Fallback cleanup
                    try {
                        document.querySelectorAll('.modal-backdrop').forEach(el => {
                            el.parentNode?.removeChild(el);
                        });
                        
                        document.body.classList.remove('modal-open');
                        document.body.style.overflow = '';
                        document.body.style.paddingRight = '';
                    } catch (e) {
                        console.error('Fallback cleanup also failed:', e);
                    }
                }
            };
            
            console.log('Bootstrap Modal _hideModal method patched successfully');
        } catch (error) {
            console.error('Error patching Bootstrap Modal:', error);
        }
    }
    
    // Fix for modal transitions
    function fixModalTransitions() {
        // Ensure only one modal shows at a time
        $(document).on('show.bs.modal', '.modal', function() {
            const zIndex = 1040 + (10 * $('.modal:visible').length);
            $(this).css('z-index', zIndex);
            
            // Ensure backdrop appears under the modal
            setTimeout(() => {
                $('.modal-backdrop')
                    .not('.modal-stack')
                    .css('z-index', zIndex - 1)
                    .addClass('modal-stack');
            }, 0);
        });
        
        // Handle modal opening from another modal
        $(document).on('click', '[data-bs-toggle="modal"]', function() {
            if ($(this).parents('.modal').length) {
                const parentModal = $(this).parents('.modal');
                
                // Record the parent modal for later
                const targetModal = $($(this).data('bs-target') || $(this).attr('href'));
                if (targetModal.length) {
                    targetModal.data('parent-modal', parentModal.attr('id'));
                }
                
                // Add modal-open class to body to maintain scrollbar compensation
                $('body').addClass('modal-open');
            }
        });
        
        console.log('Modal transition fixes installed');
    }
    
    // Start checking for Bootstrap and fix when available
    checkAndFixBootstrap();
    
    // Fix modal transitions
    if (typeof $ !== 'undefined') {
        $(document).ready(fixModalTransitions);
    } else {
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof $ !== 'undefined') {
                fixModalTransitions();
            }
        });
    }
    
    console.log('Bootstrap Modal.js fix script initialized');
})(); 