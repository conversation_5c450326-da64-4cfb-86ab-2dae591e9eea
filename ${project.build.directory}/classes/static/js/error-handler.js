/**
 * Enhanced error handling script
 * 
 * This script fixes:
 * 1. Improves error logging and formatting
 * 2. Prevents raw error object from being displayed to users
 * 3. Adds structured error information for debugging
 * 4. Handles cross-origin script errors properly
 */

// Execute immediately
(function() {
    console.log('Enhanced error handler loaded');

    // Save the original error handler if it exists
    const originalErrorHandler = window.onerror;
    
    // Keep track of handled errors to avoid duplicates
    const handledErrors = new Set();
    
    // Track if we've shown the ChatState error already
    let chatStateErrorShown = false;

    // Keep track of consecutive "Script error" messages to avoid spamming
    let scriptErrorCount = 0;
    let lastScriptErrorTime = 0;
    
    // Helper function to generate an error key for deduplication
    function getErrorKey(message, source, lineno, colno) {
        return `${message}:${source}:${lineno}:${colno}`;
    }
    
    // Helper function to check if error might be related to ChatState
    function isChatStateError(error) {
        return error && (
            (error.message && error.message.includes('ChatState')) ||
            (error.stack && error.stack.includes('ChatState')) ||
            (error.stack && error.stack.includes('initChatState'))
        );
    }
    
    // Automatically attempt to fix ChatState errors
    function fixChatStateError() {
        if (chatStateErrorShown) return;
        
        console.warn('检测到ChatState相关错误，尝试自动修复...');
        chatStateErrorShown = true;
        
        try {
            // Check if we have the fix script
            if (typeof window.initChatState === 'function') {
                console.log('使用现有的initChatState函数修复ChatState');
                window.initChatState();
            } else {
                console.warn('未找到initChatState函数，尝试直接创建简单的ChatState对象');
                
                // Create a minimal ChatState object to prevent errors
                window.ChatState = window.ChatState || {
                    saveBookUrl: function(url, context) {
                        console.log(`[紧急ChatState] 保存书籍URL: ${url || ''}, 上下文: ${context || 'current'}`);
                        
                        // Update UI directly
                        if (context === 'new') {
                            $('#newBookUrl').val(url || '');
                        } else {
                            $('#currentBookUrl').val(url || '');
                        }
                        
                        // Try to save to localStorage
                        try {
                            if (context === 'new') {
                                const newChat = JSON.parse(localStorage.getItem('newChat') || '{}');
                                newChat.bookUrl = url || '';
                                localStorage.setItem('newChat', JSON.stringify(newChat));
                            } else {
                                const currentChat = JSON.parse(localStorage.getItem('currentChat') || '{}');
                                currentChat.bookUrl = url || '';
                                localStorage.setItem('currentChat', JSON.stringify(currentChat));
                            }
                        } catch (e) {
                            console.error('保存状态到localStorage失败:', e);
                        }
                    },
                    reset: function(context) {
                        console.log(`[紧急ChatState] 重置状态, 上下文: ${context || 'both'}`);
                        
                        // Reset UI
                        if (context === 'new' || context === 'both') {
                            $('#newBookUrl').val('');
                            $('#newKnowId').val('');
                        }
                        
                        if (context === 'current' || context === 'both') {
                            $('#currentBookUrl').val('');
                            $('#knowId').val('');
                        }
                        
                        // Try to reset localStorage
                        try {
                            if (context === 'new' || context === 'both') {
                                localStorage.setItem('newChat', JSON.stringify({
                                    bookUrl: '',
                                    knowId: '',
                                    title: '新建对话'
                                }));
                            }
                            
                            if (context === 'current' || context === 'both') {
                                localStorage.setItem('currentChat', JSON.stringify({
                                    bookUrl: '',
                                    knowId: '',
                                    title: '新对话'
                                }));
                            }
                        } catch (e) {
                            console.error('重置状态到localStorage失败:', e);
                        }
                    }
                };
            }
            
            console.log('ChatState自动修复完成');
        } catch (e) {
            console.error('ChatState自动修复失败:', e);
        }
    }
    
    // Improved error handler
    window.onerror = function(message, source, lineno, colno, error) {
        // Check for ChatState error in the message
        if (message && message.includes('ChatState') && message.includes('未定义')) {
            fixChatStateError();
        }
        
        // Handle "Script error" messages from cross-origin scripts
        if (message === 'Script error.' && !source && (!lineno || lineno === 0) && (!colno || colno === 0)) {
            // Track consecutive script errors
            const now = Date.now();
            if (now - lastScriptErrorTime < 5000) {
                scriptErrorCount++;
            } else {
                scriptErrorCount = 1;
            }
            lastScriptErrorTime = now;
            
            // Log but don't show too many consecutive script errors
            if (scriptErrorCount <= 3) {
                console.warn('Cross-origin script error detected - cannot display details due to CORS restrictions');
                
                // Try to determine if this might be related to ChatState
                if (error && isChatStateError(error)) {
                    fixChatStateError();
                }
            } else if (scriptErrorCount === 4) {
                console.warn('多个跨域脚本错误被检测到，后续类似错误将被限制显示');
            }
            
            return false;
        }
        
        // Reset script error count for different error types
        if (message !== 'Script error.') {
            scriptErrorCount = 0;
        }
        
        // Generate a key for this error
        const errorKey = getErrorKey(message, source, lineno, colno);
        
        // Skip if we've already handled this exact error
        if (handledErrors.has(errorKey)) {
            return false;
        }
        
        // Add to handled errors
        handledErrors.add(errorKey);
        
        // Set a timeout to remove from handled errors after a while
        setTimeout(() => {
            handledErrors.delete(errorKey);
        }, 5000);
        
        // Format error details for console
        const errorDetails = {
            message: message,
            source: source,
            line: lineno,
            column: colno,
            stack: error ? error.stack : 'No stack trace available'
        };
        
        // Check for ChatState error in the error object
        if (error && isChatStateError(error)) {
            fixChatStateError();
        }
        
        // Log structured error to console
        console.error('Captured error:', errorDetails);
        
        // Show user-friendly message
        if (typeof showToast === 'function') {
            // Extract just the essential part of the error
            let userMessage = '';
            
            // Parse the message to make it more readable
            if (typeof message === 'string') {
                // Check for specific error patterns
                if (message.includes('ChatState') && message.includes('未定义')) {
                    userMessage = 'ChatState模块加载出错，已自动修复';
                } else {
                    // Remove "Uncaught" prefix and script location
                    userMessage = message.replace(/^Uncaught /i, '');
                    
                    // Extract just the main error message
                    const mainErrorMatch = userMessage.match(/^([^:@\n]+)/);
                    if (mainErrorMatch) {
                        userMessage = mainErrorMatch[1];
                    }
                    
                    // Limit length
                    if (userMessage.length > 100) {
                        userMessage = userMessage.substring(0, 97) + '...';
                    }
                }
            } else {
                userMessage = '页面发生错误';
            }
            
            // Show toast with source file information
            const sourceFile = source ? source.split('/').pop() : 'unknown';
            
            // Only show errors from our own scripts, not from CDNs or third-party scripts
            if (source && (source.includes('/js/') || !source.includes('://'))) {
                showToast(`${userMessage} (${sourceFile}:${lineno})`, 'error');
            }
        }
        
        // Call the original handler if it exists
        if (typeof originalErrorHandler === 'function') {
            return originalErrorHandler(message, source, lineno, colno, error);
        }
        
        // Prevent the error from showing in the console again
        return true;
    };
    
    // Enhanced unhandledrejection handler
    window.addEventListener('unhandledrejection', function(event) {
        // Skip if no event or reason
        if (!event || !event.reason) return;
        
        // Generate an error key for this rejection
        const errorKey = `unhandledRejection:${event.reason.message || 'Unknown reason'}`;
        
        // Skip if we've already handled this rejection
        if (handledErrors.has(errorKey)) {
            event.preventDefault();
            return;
        }
        
        // Add to handled errors
        handledErrors.add(errorKey);
        
        // Set a timeout to remove from handled errors after a while
        setTimeout(() => {
            handledErrors.delete(errorKey);
        }, 5000);
        
        // Get error details
        const error = event.reason;
        const errorDetails = {
            message: error ? (error.message || 'Promise rejected') : 'Promise rejected',
            stack: error && error.stack ? error.stack : 'No stack trace available',
            type: 'unhandledrejection'
        };
        
        // Check for ChatState error in the rejection
        if (error && isChatStateError(error)) {
            fixChatStateError();
        }
        
        // Log structured error to console
        console.error('Unhandled promise rejection:', errorDetails);
        
        // Show user-friendly message for network errors only
        if (typeof showToast === 'function') {
            // Skip toast for non-network errors or third-party scripts
            if (error && (
                error.name === 'NetworkError' || 
                error.message.includes('network') ||
                error.message.includes('fetch') ||
                error.message.includes('ajax')
            )) {
                let userMessage = '网络请求失败';
                
                if (error.message) {
                    userMessage = error.message;
                    // Limit length
                    if (userMessage.length > 100) {
                        userMessage = userMessage.substring(0, 97) + '...';
                    }
                }
                
                showToast(userMessage, 'error');
            }
        }
        
        // Prevent default browser handling
        event.preventDefault();
    });
    
    // Fix for modal issues that might cause errors
    function setupModalErrorPrevention() {
        const cleanupModals = function() {
            try {
                // This helps prevent "cannot read property offsetWidth of null" errors
                // by removing abandoned tooltips or modal backdrops
                $('.tooltip-container, .book-tooltip, .tooltip-box').remove();
                $('.modal-backdrop:not(.show)').remove();
                
                // Fix for body styles
                if (!$('.modal.show').length && $('body').hasClass('modal-open')) {
                    $('body').removeClass('modal-open').css('overflow', '');
                }
            } catch (e) {
                console.warn('Error during preventative modal cleanup:', e);
            }
        };
        
        // Run modal cleanup periodically
        setInterval(cleanupModals, 5000);
        
        // Run cleanup after location changes
        window.addEventListener('popstate', cleanupModals);
        window.addEventListener('pushstate', cleanupModals);
        window.addEventListener('hashchange', cleanupModals);
    }
    
    // Set up modal error prevention
    setupModalErrorPrevention();
    
    // Set up automatic initialization of ChatState when ready
    $(document).ready(function() {
        setTimeout(function() {
            if (typeof window.initChatState === 'function') {
                window.initChatState();
            }
        }, 500);
    });
    
    console.log('Enhanced error handlers installed');
})(); 