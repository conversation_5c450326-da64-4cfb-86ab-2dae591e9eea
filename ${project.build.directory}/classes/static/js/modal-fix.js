/**
 * Modal fix script
 * 
 * This script adds targeted fixes for modal-related issues:
 * 1. Fixes improper cleanup when closing modals
 * 2. Prevents body scroll issues
 * 3. Improves modal transition handling
 * 4. Prevents addBookModal from closing immediately
 * 5. Fixes z-index issues with modals stacking
 */

$(document).ready(function() {
    console.log('Modal fix script loaded');
    
    // Global references to track modals
    const activeModals = new Set();
    let modalTransitioning = false;
    
    // Base z-index for modals
    const BASE_MODAL_Z_INDEX = 1050;
    
    // Fix z-index for existing modals
    function fixModalZIndices() {
        // Book search modal should appear over new chat modal
        $('#bookSearchModal').css('z-index', BASE_MODAL_Z_INDEX + 10);
        $('#addBookModal').css('z-index', BASE_MODAL_Z_INDEX + 20);
        $('#newChatModal').css('z-index', BASE_MODAL_Z_INDEX);
        
        // Also fix associated backdrops
        setTimeout(() => {
            const backdrops = $('.modal-backdrop');
            if (backdrops.length > 1) {
                // If multiple backdrops exist, assign appropriate z-indices
                backdrops.each(function(index) {
                    $(this).css('z-index', BASE_MODAL_Z_INDEX - 1 + (index * 10));
                });
            }
        }, 10);
    }
    
    // Fix addBookModal issue by preventing immediate close
    const preventAddBookModalClosing = function(event) {
        const $modal = $(this);
        const modalId = $modal.attr('id');
        
        if (modalId === 'addBookModal') {
            // Check if the modal is opening from the bookSearchModal
            const openingFromSearchModal = $('#bookSearchModal').hasClass('show');
            
            if (openingFromSearchModal) {
                console.log('Fixing addBookModal opening from bookSearchModal');
                
                // Ensure bookSearchModal is properly handled
                const bookSearchModal = document.getElementById('bookSearchModal');
                if (bookSearchModal) {
                    // Hide bookSearchModal using Bootstrap API first
                    try {
                        const instance = bootstrap.Modal.getInstance(bookSearchModal);
                        if (instance) {
                            // Set a flag to prevent cleanup during transition
                            modalTransitioning = true;
                            
                            // Add a class to body to mark transition state
                            $('body').addClass('modal-transitioning');
                            
                            // Delay to allow CSS transitions to complete
                            setTimeout(() => {
                                modalTransitioning = false;
                                $('body').removeClass('modal-transitioning');
                            }, 500);
                        }
                    } catch (e) {
                        console.warn('Error handling bookSearchModal:', e);
                    }
                }
            }
        }
        
        // Always fix z-index when a modal is shown
        fixModalZIndices();
    };
    
    // Function to ensure correct modal stacking
    function fixModalStacking() {
        // Ensure bookSearchModal appears on top when opened from newChatModal
        $('#modalBookSearchBtn, #bookSearchBtn').on('click', function() {
            const sourceModalId = $(this).closest('.modal').attr('id');
            console.log(`Opening search modal from ${sourceModalId || 'main page'}`);
            
            // Set a flag to prevent immediate cleanup
            modalTransitioning = true;
            
            // Fix z-indices immediately and after a delay
            fixModalZIndices();
            
            setTimeout(() => {
                modalTransitioning = false;
                fixModalZIndices();
            }, 500);
        });
    }
    
    // Override Bootstrap's Modal hide to ensure proper cleanup
    const originalModalHide = $.fn.modal.Constructor?.prototype.hide;
    if (originalModalHide) {
        $.fn.modal.Constructor.prototype.hide = function(...args) {
            try {
                // Get modal ID before hiding
                const modalId = this._element?.id;
                
                // Skip if we're in transition between modals
                if (modalTransitioning && modalId === 'bookSearchModal') {
                    console.log('Skipping immediate hide during modal transition');
                    return;
                }
                
                // Run original hide
                const result = originalModalHide.apply(this, args);
                
                // Run additional cleanup
                setTimeout(() => {
                    // Skip cleanup for addBookModal when it's just being shown
                    if (modalId === 'addBookModal' && $('#addBookModal').hasClass('show')) {
                        console.log('Skipping cleanup for active addBookModal');
                        return;
                    }
                    
                    // Remove backdrop and reset body
                    $('.modal-backdrop').remove();
                    $('body').removeClass('modal-open').css({
                        'overflow': '',
                        'padding-right': ''
                    });
                    
                    console.log(`Modal cleanup completed for: ${modalId || 'unknown'}`);
                }, 300);
                
                return result;
            } catch (e) {
                console.error('Error in enhanced modal hide:', e);
                
                // Fallback to direct cleanup
                $('.modal-backdrop').remove();
                $('body').removeClass('modal-open').css('overflow', '');
                
                // Still try to call original
                try {
                    return originalModalHide.apply(this, args);
                } catch (e2) {
                    console.error('Fallback modal hide also failed:', e2);
                }
            }
        };
        
        console.log('Enhanced Bootstrap Modal hide method');
    }
    
    // Fix for modal backdrop issues when using data-bs-target
    $(document).on('click', '[data-bs-toggle="modal"][data-bs-target]', function(e) {
        const targetSelector = $(this).attr('data-bs-target');
        if (!targetSelector) return;
        
        const sourceModalId = $(this).closest('.modal').attr('id');
        if (sourceModalId) {
            // Store this info for modal transition handling
            $(targetSelector).data('openedFrom', sourceModalId);
            
            // Set transition flag
            modalTransitioning = true;
            
            // Set correct z-index for target modal
            if (targetSelector === '#bookSearchModal') {
                setTimeout(() => {
                    $(targetSelector).css('z-index', BASE_MODAL_Z_INDEX + 10);
                    
                    // Adjust backdrop z-index
                    const backdrops = $('.modal-backdrop');
                    if (backdrops.length > 1) {
                        $(backdrops[backdrops.length - 1]).css('z-index', BASE_MODAL_Z_INDEX + 9);
                    }
                }, 10);
            }
            
            // Release flag after transition should be complete
            setTimeout(() => {
                modalTransitioning = false;
                fixModalZIndices();
            }, 500);
        }
    });
    
    // Handle cancel button on new chat modal
    $('#cancelNewChat').off('click').on('click', function(e) {
        e.preventDefault();
        console.log('Cancel button clicked on new chat modal');
        
        try {
            // Get the modal instance and hide it
            const modalElement = document.getElementById('newChatModal');
            if (modalElement) {
                const modalInstance = bootstrap.Modal.getInstance(modalElement);
                if (modalInstance) {
                    modalInstance.hide();
                } else {
                    // Fallback to jQuery
                    $('#newChatModal').modal('hide');
                }
            }
            
            // Ensure fields are cleared
            $('#newKnowId').val('');
            $('#newBookUrl').val('');
            
            // Reset the state if ChatState exists
            if (window.ChatState) {
                try {
                    if (typeof ChatState.reset === 'function') {
                        ChatState.reset('new');
                    } else {
                        // Manual reset
                        ChatState.newChat = {
                            bookUrl: '',
                            knowId: '',
                            title: '新建对话'
                        };
                        if (typeof ChatState.saveToStorage === 'function') {
                            ChatState.saveToStorage();
                        }
                    }
                } catch (e) {
                    console.warn('Error resetting ChatState:', e);
                }
            }
            
            // Run comprehensive modal cleanup
            setTimeout(function() {
                cleanupAllModals();
            }, 100);
        } catch (error) {
            console.error('Error handling cancel new chat:', error);
        }
    });
    
    // Comprehensive modal cleanup
    window.cleanupAllModals = window.cleanupAllModals || function() {
        console.log('Running comprehensive modal cleanup');
        
        try {
            // Don't remove backdrops when transitioning between modals
            if (modalTransitioning) {
                console.log('Skipping cleanup during modal transition');
                return false;
            }
            
            // Remove all backdrops
            $('.modal-backdrop').remove();
            
            // Reset body styles - only if no modal is currently active
            if (!$('.modal.show').length) {
                $('body')
                    .removeClass('modal-open')
                    .css({
                        'overflow': '',
                        'padding-right': ''
                    });
            }
            
            // Only hide modals that are not currently active
            $('.modal:not(.show)').each(function() {
                $(this).css('display', 'none');
                
                // Dispose of Bootstrap modal instances that are not active
                try {
                    const instance = bootstrap.Modal.getInstance(this);
                    if (instance && !$(this).hasClass('show')) {
                        instance.dispose();
                    }
                } catch (e) {
                    // Ignore errors
                }
            });
            
            // Remove all tooltips
            $('.tooltip-container, .book-tooltip, .tooltip-box').remove();
            
            console.log('Comprehensive modal cleanup completed');
            return true;
        } catch (e) {
            console.error('Error in comprehensive modal cleanup:', e);
            
            // Last resort cleanup
            try {
                document.querySelectorAll('.modal-backdrop').forEach(el => el.remove());
                document.body.classList.remove('modal-open');
                document.body.style.overflow = '';
                document.body.style.paddingRight = '';
            } catch (e2) {
                console.error('Last resort cleanup also failed:', e2);
            }
            
            return false;
        }
    };
    
    // Add direct event handling to all modal close buttons
    function setupModalCloseHandlers() {
        // Remove any existing handlers
        $(document).off('click.modalFix', '[data-bs-dismiss="modal"], [data-dismiss="modal"], .close, .btn-close');
        
        // Add new handlers
        $(document).on('click.modalFix', '[data-bs-dismiss="modal"], [data-dismiss="modal"], .close, .btn-close', function(e) {
            const modalElement = $(this).closest('.modal')[0];
            if (!modalElement) return;
            
            const modalId = modalElement.id;
            console.log(`Modal close button clicked: ${modalId}`);
            
            // Ensure the modal is properly hidden
            try {
                const modalInstance = bootstrap.Modal.getInstance(modalElement);
                if (modalInstance) {
                    modalInstance.hide();
                } else {
                    // Fallback to jQuery
                    $(modalElement).modal('hide');
                }
            } catch (error) {
                console.error('Error hiding modal:', error);
                
                // Direct cleanup as last resort
                $(modalElement).removeClass('show').css('display', 'none');
                $('.modal-backdrop').remove();
                $('body').removeClass('modal-open').css('overflow', '');
            }
            
            // Run cleanup with delay
            setTimeout(cleanupAllModals, 100);
        });
    }
    
    // Add hooks for modal events to fix transition issues
    $(document).on('show.bs.modal', preventAddBookModalClosing);
    
    $(document).on('shown.bs.modal', function(e) {
        const modalId = e.target.id;
        activeModals.add(modalId);
        
        // Fix z-index for properly stacked modals
        fixModalZIndices();
        
        // Clear transition flag
        setTimeout(() => {
            modalTransitioning = false;
            // Fix z-indices again after transition
            fixModalZIndices();
        }, 300);
        
        console.log(`Modal shown: ${modalId}`);
    });
    
    $(document).on('hidden.bs.modal', function(e) {
        const modalId = e.target.id;
        activeModals.delete(modalId);
        
        console.log(`Modal hidden: ${modalId}`);
        
        // Run cleanup with delay, but only if no transition is in progress
        if (!modalTransitioning) {
            setTimeout(cleanupAllModals, 100);
        }
        
        // Fix z-indices of remaining modals
        setTimeout(fixModalZIndices, 100);
    });
    
    // Set up modal close handlers
    setupModalCloseHandlers();
    
    // Add special handling for ESC key
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape' || e.keyCode === 27) {
            if ($('.modal.show').length) {
                console.log('ESC key detected with open modal, running cleanup');
                setTimeout(cleanupAllModals, 100);
            }
        }
    });
    
    // Fix for addBookModal and bookSearchModal interaction
    $('#bookSearchModal').on('hide.bs.modal', function() {
        // Check if we're opening the addBookModal
        if ($('#addBookModal').hasClass('show') || $('body').hasClass('modal-transitioning')) {
            console.log('Preventing backdrop removal during modal transition');
            
            // Keep at least one backdrop
            $('.modal-backdrop').slice(1).remove();
            return;
        }
    });
    
    // Add special handling for the book search and add book buttons
    $('#bookSearchBtn, #modalBookSearchBtn').off('click').on('click', function(e) {
        if ($(this).closest('#newChatModal').length) {
            console.log('Opening book search from new chat modal');
            // Set flag for transition
            modalTransitioning = true;
            
            // Store source modal info
            $('#bookSearchModal').data('source-modal', 'newChatModal');
            
            // Fix z-index after modal is shown
            setTimeout(() => {
                $('#bookSearchModal').css('z-index', BASE_MODAL_Z_INDEX + 10);
                $('.modal-backdrop').last().css('z-index', BASE_MODAL_Z_INDEX + 9);
                modalTransitioning = false;
            }, 300);
        }
    });
    
    // Initialize modal stacking fix
    fixModalStacking();
    
    // Fix initial z-indices
    fixModalZIndices();
    
    console.log('Modal fix script initialization complete');
}); 